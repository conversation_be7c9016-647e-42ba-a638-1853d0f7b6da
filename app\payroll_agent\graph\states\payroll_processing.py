from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

from app.payroll_agent.models.payroll_processing import WorkerCommand


class PayrollProcessingOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    payroll_commands_agent_1: List[WorkerCommand] = Field(default_factory=list, description="payroll commands from agent 1")
    payroll_commands_agent_2: List[WorkerCommand] = Field(default_factory=list, description="payroll commands from agent 2")
    processed_payrolls: List[WorkerCommand] = Field(default_factory=list, description="final processed payroll_entries")
    agents_matched: bool = Field(default=False, description="Whether the agents matched")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")
    run_time: Optional[dict] = Field(None, description="Time taken to run the classification graph")
    llm_usage: Optional[dict] = Field(default_factory=dict, description="LLM usage info: model, input tokens, output tokens")