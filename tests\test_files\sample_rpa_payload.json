{"clients": [{"accountNumber": "********", "legalName": "Test account", "authorized": true}], "comment": {"uid": "123", "id": "1234", "timestamp": "2025-09-05T17:00:20.227000", "thread_id": "1234", "user_properties": {"number:Participant Count": 3.0, "number:Position in Thread": 1.0, "number:Recipient Count": 2.0, "string:Business Unit ID": "", "string:Folder": "", "string:Has Signature": "", "string:Mailbox ID": "", "string:Message ID": "", "string:Product ID": "", "string:Segment ID": "", "string:Sender": "", "string:Sender Domain": "", "string:Thread": ""}, "messages": [{"from": "xxx", "to": ["xxxx"], "cc": [], "sent_at": "xxx", "body": {"text": "xxx"}, "subject": {"text": "xxx"}, "signature": {"text": ""}}], "text_format": "plain", "attachments": [], "source_id": "d235d0c890ab4d0b", "last_modified": "2025-09-05T17:00:20.227000", "created_at": "2025-09-05T17:00:20.227000", "context": "*************"}, "sequence_id": "test_sequence_789", "labels": [{"name": ["Payroll"], "probability": 0.95}], "entities": [{"id": "test_entity_001", "name": "client-id", "spans": [{"content_part": "subject", "message_index": 0, "utf16_byte_start": 25, "utf16_byte_end": 32, "char_start": 25, "char_end": 32}], "kind": "client-id", "formatted_value": "Week 34", "probability": 0.85, "capture_ids": [], "span": {"content_part": "subject", "message_index": 0, "utf16_byte_start": 25, "utf16_byte_end": 32, "char_start": 25, "char_end": 32}}], "label_properties": [{"property_id": "0000000000000001", "property_name": "tone", "value": 1.5}, {"property_id": "0000000000000002", "property_name": "quality_of_service", "value": 0.9}], "bodyThreaded": "xxx"}