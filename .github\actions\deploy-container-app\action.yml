name: 'Deploy Payroll Email Agent'
description: 'Reusable action to deploy the payroll email agent to Azure Container Apps'

inputs:
  environment:
    description: 'Target environment (dev, demo, test_paychex)'
    required: true
  azure-subscription-id:
    description: 'Azure Subscription ID'
    required: true
  azure-credentials:
    description: 'Azure Credentials JSON for Service Principal'
    required: true
  azure-registry-password:
    description: 'Azure Container Registry password (admin user)'
    required: true
  git-sha:
    description: 'Git SHA for image tagging'
    required: true
    default: ${{ github.sha }}

runs:
  using: 'composite'
  steps:
    - name: Set environment variables
      shell: bash
      run: |
        # Set common variables (same for all environments)
        echo "AZURE_REGISTRY_LOGIN_SERVER=conpayrollsb001.azurecr.io" >> $GITHUB_ENV
        echo "AZURE_REGISTRY_USERNAME=conpayrollsb001" >> $GITHUB_ENV
        echo "AZURE_IMAGE_REPO=paychex/payroll-ai-agent" >> $GITHUB_ENV
        echo "DOCKER_DEFAULT_PLATFORM=linux/amd64" >> $GITHUB_ENV
        echo "IMAGE_TAG=${{ inputs.environment }}" >> $GITHUB_ENV
        echo "AZURE_RESOURCE_GROUP=rg-emailpayrollautomation-eastus-sb-001" >> $GITHUB_ENV
        echo "APP_CONFIG_NAME=appcfg-payrollai-sb-001" >> $GITHUB_ENV
        echo "KEY_VAULT_NAME=kv-payrollai-sb-001" >> $GITHUB_ENV
        
        # Set environment-specific container app name
        case "${{ inputs.environment }}" in
          "dev")
            echo "AZURE_CONTAINER_APP=conagenticengineeastussb001" >> $GITHUB_ENV
            ;;
          "demo")
            echo "AZURE_CONTAINER_APP=conagenticengineeastussb001-demo" >> $GITHUB_ENV
            ;;
          "test_paychex")
            echo "AZURE_CONTAINER_APP=conagenticengineeastussb001-test" >> $GITHUB_ENV
            ;;
          *)
            echo "Unknown environment: ${{ inputs.environment }}"
            exit 1
            ;;
        esac
        
    - name: Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ inputs.azure-credentials }}

    - name: Get ACR access token (OIDC)
      id: get-acr-token
      shell: bash
      run: |
        set -e
        TOKEN=$(az acr login --name ${{ env.AZURE_REGISTRY_USERNAME }} --expose-token --output tsv --query accessToken)
        echo "acr-token=$TOKEN" >> $GITHUB_OUTPUT

    - name: Docker login to ACR (OIDC token)
      uses: docker/login-action@v3
      with:
        registry: ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}
        username: 00000000-0000-0000-0000-000000000000
        password: ${{ steps.get-acr-token.outputs.acr-token }}

    - name: Build and push Docker image
      shell: bash
      run: |
        echo "Building docker image from commit: ${{ inputs.git-sha }} for environment: ${{ inputs.environment }}"

        # Build image with both SHA and latest tags
        docker build -f Dockerfile.agent -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }} .
        docker build -f Dockerfile.agent -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest .

        echo "Pushing images to ACR..."
        docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }}
        docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest
        echo "Successfully pushed images to ACR"

    - name: Deploy to Azure Container App
      id: deploy-to-aca
      shell: bash
      env:
        SUBSCRIPTION_ID: ${{ inputs.azure-subscription-id }}
      run: |
          set -euo pipefail

          az cloud set -n AzureCloud
          az config set extension.use_dynamic_install=yes_without_prompt
          az config set defaults.subscription=${SUBSCRIPTION_ID}

          az extension add -n containerapp -y || az extension update -n containerapp || true

          az group show \
            --name "${{ env.AZURE_RESOURCE_GROUP }}" \
            --subscription "${SUBSCRIPTION_ID}" >/dev/null

          IMAGE_TAG="${{ env.IMAGE_TAG }}.${{ inputs.git-sha }}"
          FULL_IMAGE_NAME="${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:$IMAGE_TAG"

          echo "RG=${{ env.AZURE_RESOURCE_GROUP }} APP=${{ env.AZURE_CONTAINER_APP }}"
          echo "Deploying image: $FULL_IMAGE_NAME to ${{ env.AZURE_CONTAINER_APP }}"

          az containerapp update \
            --subscription ${{ inputs.azure-subscription-id }} \
            -g ${{ env.AZURE_RESOURCE_GROUP }} \
            -n ${{ env.AZURE_CONTAINER_APP }} \
            --image $FULL_IMAGE_NAME \
            --cpu 2 --memory 4Gi \
            --replace-env-vars \
              OPENAI_API_KEY="$OPENAI_API_KEY" \
              LANGCHAIN_API_KEY="$LANGCHAIN_API_KEY" \
              LANGCHAIN_TRACING_V2="$LANGCHAIN_TRACING_V2" \
              LANGCHAIN_PROJECT="$LANGCHAIN_PROJECT" \
              LANGCHAIN_ENDPOINT="$LANGCHAIN_ENDPOINT" \
              LANGGRAPH_API_URL="$LANGGRAPH_API_URL" \
              MCP_SERVERS__PAYCHEX__CLIENT_ID="$MCP_SERVERS__PAYCHEX__CLIENT_ID" \
              MCP_SERVERS__PAYCHEX__CLIENT_SECRET="$MCP_SERVERS__PAYCHEX__CLIENT_SECRET" \
              MCP_SERVERS__PAYCHEX__TRANSPORT="$MCP_SERVERS__PAYCHEX__TRANSPORT" \
              MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="$MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL" \
              MCP_SERVERS__PAYCHEX__URL="$MCP_SERVERS__PAYCHEX__URL" \
              LLM_TYPE="$LLM_TYPE" \
              AZURE_ENDPOINT="$AZURE_ENDPOINT" \
              API_VERSION="$API_VERSION" \
              DASHBOARD_API_URL="$DASHBOARD_API_URL"

          APP_URL=$(az containerapp show -g ${{ env.AZURE_RESOURCE_GROUP }} -n ${{ env.AZURE_CONTAINER_APP }} --query properties.configuration.ingress.fqdn -o tsv)
          echo "container-app-url=https://$APP_URL" >> $GITHUB_OUTPUT

    - name: Clean up Docker images
      if: always()
      shell: bash
      run: |
        docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }} || true
        docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest || true

outputs:
  container-app-url:
    description: 'The URL of the deployed container app'
    value: ${{ steps.deploy-to-aca.outputs.container-app-url }}
