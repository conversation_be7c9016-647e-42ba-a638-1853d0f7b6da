"""
MarkItDown Attachment Processor Utility

This module provides utilities for processing various file attachments
using Microsoft's MarkItDown library and custom OCR. Supports Excel, PDF, Word,
PowerPoint, Images, Audio, HTML, CSV, JSON, XML, ZIP, EPub files and more.
"""

import os
import time
import json
import asyncio
import base64
from io import BytesIO
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseModel

try:
    import fitz  # pymupdf (optional, used to render PDF pages)
except Exception:
    fitz = None

from pdf2image import convert_from_path
from PIL import Image, ImageSequence

from app.cli.logging_utils import setup_logger
from markitdown import MarkItDown
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.llm_utils import invoke_structured_llm_with_retry, create_llm_with_structured_output
from app.payroll_agent.models.company_worker_lookup import AttachmentHandwrittenAnalysis

# Set up the logger
logger = setup_logger(__name__)

# Global MarkItDown instance
_markitdown_instance = None

# Constants
VISUAL_FILE_EXTENSIONS = {".png", ".jpg", ".jpeg", ".tif", ".tiff", ".bmp", ".gif", ".pdf"}
MAX_IMAGE_SIZE = 800
MAX_IMAGE_BYTES = 100_000
OCR_TIMEOUT_SECONDS = 30

class TextExtractionResult(BaseModel):
    file: str
    extracted_text: str
    confidence: int
    text_found: bool

# Image processing functions
def create_image_data_url(file_path: str, max_size: int = 800, max_bytes: int = 100_000) -> tuple[str, int]:
    """Create compressed image data URL from file."""
    try:
        img = _load_image_from_file(file_path)
        if img is None:
            return None, 0
        
        # Resize if too large
        img = _resize_image_if_needed(img, max_size)
        
        # Compress to JPEG
        return _compress_image_to_data_url(img, max_bytes)
        
    except Exception as e:
        logger.error(f"Failed to create image data URL for {file_path}: {e}")
        return None, 0

def _load_image_from_file(file_path: str) -> Image.Image:
    """Load image from file, handling PDFs and regular images."""
    suffix = Path(file_path).suffix.lower()
    
    if suffix == ".pdf":
        return _render_pdf_first_page(file_path)
    else:
        return _load_regular_image(file_path)

def _render_pdf_first_page(file_path: str) -> Image.Image:
    """Render first page of PDF using available libraries."""
    if fitz is not None:
        try:
            doc = fitz.open(str(file_path))
            page = doc.load_page(0)
            mat = fitz.Matrix(1.2, 1.2)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            doc.close()
            logger.info("Rendered PDF first page with pymupdf")
            return img
        except Exception as e:
            logger.warning(f"pymupdf render failed: {e}")
    
    try:
        pages = convert_from_path(str(file_path), first_page=1, last_page=1, dpi=120)
        if pages:
            logger.info("Rendered PDF first page with pdf2image")
            return pages[0].convert("RGB")
    except Exception as e:
        logger.error(f"pdf2image render failed: {e}")
    
    return None

def _load_regular_image(file_path: str) -> Image.Image:
    """Load regular image file, handling multi-frame images."""
    try:
        opened = Image.open(file_path)
        
        # Handle multi-frame images (GIF, TIFF)
        frames = []
        for i, frame in enumerate(ImageSequence.Iterator(opened)):
            if i >= 5:  # Max 5 frames
                break
            frames.append(frame.convert("RGB"))
        
        if len(frames) <= 1:
            return opened.convert("RGB")
        else:
            return _stitch_frames_vertically(frames)
            
    except Exception as e:
        logger.error(f"Failed to load image {file_path}: {e}")
        return None

def _stitch_frames_vertically(frames: list) -> Image.Image:
    """Stitch multiple frames vertically."""
    total_height = sum(f.height for f in frames)
    max_width = max(f.width for f in frames)
    
    stitched = Image.new("RGB", (max_width, total_height), (255, 255, 255))
    y = 0
    for frame in frames:
        stitched.paste(frame, (0, y))
        y += frame.height
    
    return stitched

def _resize_image_if_needed(img: Image.Image, max_size: int) -> Image.Image:
    """Resize image if it exceeds max_size."""
    w, h = img.size
    if max(w, h) > max_size:
        scale = max_size / max(w, h)
        new_size = (int(w * scale), int(h * scale))
        return img.resize(new_size, Image.LANCZOS)
    return img

def _compress_image_to_data_url(img: Image.Image, max_bytes: int) -> tuple[str, int]:
    """Compress image to data URL under max_bytes."""
    buf = BytesIO()
    quality = 60
    
    img.save(buf, format="JPEG", quality=quality, optimize=True)
    raw = buf.getvalue()
    
    # Reduce quality until under max_bytes
    while len(raw) > max_bytes and quality > 10:
        quality -= 10
        buf.seek(0)
        buf.truncate(0)
        img.save(buf, format="JPEG", quality=quality, optimize=True)
        raw = buf.getvalue()
    
    data_url = "data:image/jpeg;base64," + base64.b64encode(raw).decode("ascii")
    return data_url, len(raw)

# Enhanced version of the original function with better compression
def create_advanced_image_data_url(file_path: str, max_size: int = 1024, max_bytes: int = 200_000, max_image_frames: int = 5) -> tuple[str, int]:
    """
    Enhanced image processing with multi-frame support and aggressive compression.
    PDFs: render only the first page.
    Images: load all frames (up to max_image_frames) and stitch vertically.
    Returns (data_url, bytes_len).
    """
    p = Path(file_path)
    suffix = (p.suffix or "").lower()
    img = None

    # 1) PDF: render only first page
    if suffix == ".pdf":
        img = _render_pdf_first_page(file_path)
    # 2) Image files: open and handle multi-frame
    else:
        try:
            opened = Image.open(file_path)
            # If multi-frame (animated GIF / multi-page TIFF), collect frames
            try:
                frames = []
                for i, frame in enumerate(ImageSequence.Iterator(opened)):
                    if i >= max_image_frames:
                        break
                    frames.append(frame.convert("RGB"))
                if len(frames) == 0:
                    img = opened.convert("RGB")
                elif len(frames) == 1:
                    img = frames[0]
                else:
                    # stitch frames vertically
                    img = _stitch_frames_vertically(frames)
            except Exception:
                # fallback to single-frame conversion
                img = opened.convert("RGB")
            logger.info("Opened image file (frames handled) for LLM payload", extra={"file": file_path})
        except Exception as e:
            logger.warning("Unable to open file as image; returning tiny placeholder", extra={"file": file_path, "error": str(e)})
            placeholder = Image.new("RGB", (1, 1), color=(255, 255, 255))
            buf = BytesIO()
            placeholder.save(buf, format="JPEG", quality=10, optimize=True)
            raw = buf.getvalue()
            data_url = "data:image/jpeg;base64," + base64.b64encode(raw).decode("ascii")
            return data_url, len(raw)

    if img is None:
        return None, 0

    # Resize preserving aspect ratio - more aggressive
    w, h = img.size
    effective_max_size = min(max_size, 800)  # Cap at 800px
    if max(w, h) > effective_max_size:
        scale = effective_max_size / float(max(w, h))
        new_size = (int(w * scale), int(h * scale))
        img = img.resize(new_size, Image.LANCZOS)
        logger.debug("Resized image for LLM payload", extra={"file": file_path, "orig_size": (w, h), "new_size": new_size})

    # Compress to JPEG with aggressive settings
    buf = BytesIO()
    quality = 60  # Start lower
    img.save(buf, format="JPEG", quality=quality, optimize=True)
    raw = buf.getvalue()
    
    # More aggressive reduction
    while len(raw) > max_bytes and quality > 10:
        quality -= 10
        buf.seek(0)
        buf.truncate(0)
        img.save(buf, format="JPEG", quality=quality, optimize=True)
        raw = buf.getvalue()
        logger.debug("Reduced JPEG quality for LLM payload", extra={"file": file_path, "quality": quality, "bytes": len(raw)})

    if len(raw) > max_bytes:
        logger.warning("Could not compress image under max_bytes", extra={"file": file_path, "bytes": len(raw), "max_bytes": max_bytes})

    data_url = "data:image/jpeg;base64," + base64.b64encode(raw).decode("ascii")
    return data_url, len(raw)

# OCR functions
async def extract_text_with_ocr(file_path: str, ocr_prompt: str) -> str:
    """Extract text using OCR with structured output."""
    try:
        # Use the image processing
        data_url, payload_bytes = create_image_data_url(file_path)
        if not data_url:
            return ""
        
        logger.info(f"Prepared image for OCR: {file_path} ({payload_bytes} bytes)")

        # Create LLM with OCR schema
        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(model=settings.LLM_MODEL_MULTI_MODAL),
            schema=TextExtractionResult,
        )
        
        user_payload = json.dumps({
            "file": os.path.basename(file_path), 
            "image_data": data_url
        })

        message = [
            {"role": "system", "content": ocr_prompt},
            {"role": "user", "content": user_payload}
        ]

        raw_resp, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message)
        
        # Extract text from response
        if isinstance(raw_resp, TextExtractionResult):
            return raw_resp.extracted_text if raw_resp.text_found else ""
        elif hasattr(raw_resp, 'extracted_text'):
            return raw_resp.extracted_text
        else:
            return str(raw_resp)
        
    except Exception as e:
        logger.error(f"OCR failed for {file_path}: {e}")
        return ""

async def check_for_handwritten_notes(file_path: str, handwriting_prompt: str, state=None) -> AttachmentHandwrittenAnalysis:
    """
    Check for handwritten content in visual files using the enhanced image processing.
    """
    try:
        logger.info(f"Starting handwriting check: {file_path}")
        
        data_url, payload_bytes = create_advanced_image_data_url(file_path, max_size=800, max_bytes=100_000)
        if data_url is None:
            logger.warning("Failed to prepare image data URL for LLM payload")
            return AttachmentHandwrittenAnalysis(
                file=os.path.basename(file_path),
                confidence=100,
                reasoning="Failed to prepare image data URL for LLM payload",
            )
        
        logger.info(f"Prepared image payload: {payload_bytes} bytes")

        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(model=settings.LLM_MODEL_MULTI_MODAL),
            schema=AttachmentHandwrittenAnalysis,
        )

        user_payload = json.dumps({
            "file": os.path.basename(file_path), 
            "image_data": data_url
        })

        message = [
            {"role": "system", "content": handwriting_prompt},
            {"role": "user", "content": user_payload}
        ]

        logger.debug(f"Invoking LLM for handwriting check: {file_path}")
        raw_resp, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message)

        # Parse response into model
        analysis = _parse_handwriting_response(raw_resp, file_path)
        
        # Log usage if state provided
        if state:
            from app.payroll_agent.utils.logging import log_llm_usage
            try:
                log_llm_usage(
                    state=state,
                    step_name="attachment_handwriting_check",
                    llm=llm,
                    llm_response=raw_resp,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    output_state_attr="company_worker_lookup_output_state"
                )
            except Exception:
                logger.debug("log_llm_usage failed for handwriting check", exc_info=True)

        logger.info(f"Handwriting check complete: confidence={analysis.confidence}")
        return analysis

    except Exception as e:
        logger.error(f"Handwriting check failed for {file_path}: {e}")
        return _create_failed_handwriting_analysis(file_path, f"Analysis error: {type(e).__name__}")

def _parse_handwriting_response(raw_resp, file_path: str) -> AttachmentHandwrittenAnalysis:
    """Parse LLM response into AttachmentHandwrittenAnalysis model."""
    if isinstance(raw_resp, AttachmentHandwrittenAnalysis):
        return raw_resp
    
    try:
        return AttachmentHandwrittenAnalysis.model_validate(raw_resp)
    except Exception:
        try:
            return AttachmentHandwrittenAnalysis(**(raw_resp if isinstance(raw_resp, dict) else {}))
        except Exception:
            return _create_failed_handwriting_analysis(file_path, "Unparseable LLM output")

def _create_failed_handwriting_analysis(file_path: str, reason: str) -> AttachmentHandwrittenAnalysis:
    """Create failed handwriting analysis result."""
    return AttachmentHandwrittenAnalysis(
        file=os.path.basename(file_path),
        confidence=100,
        reasoning=reason
    )

# Processing functions
async def process_all_attachments(temp_file_paths: list, original_content: str, ocr_prompt: str, ocr_fallback_prompt: str, handwriting_prompt: str, state=None) -> tuple[str, list]:
    """Process all attachments and return updated content and handwriting results."""
    updated_content = original_content
    handwriting_results = []
    
    for temp_file_path, blob_path in temp_file_paths:
        try:
            # Process single attachment with both prompts
            attachment_content = await process_single_attachment(temp_file_path, blob_path, ocr_prompt, ocr_fallback_prompt)
            
            if attachment_content:
                updated_content += attachment_content
            
            # Check for handwritten notes if visual file
            if is_visual_file(temp_file_path):
                handwriting_analysis = await check_for_handwritten_notes(temp_file_path, handwriting_prompt, state)
                handwriting_results.append(handwriting_analysis)
            else:
                handwriting_results.append(create_non_visual_analysis(blob_path))
                
        except Exception as e:
            logger.error(f"Error processing attachment {blob_path}: {e}")
            updated_content += create_error_section(blob_path, e)
            handwriting_results.append(_create_failed_handwriting_analysis(blob_path, f"Processing failed: {e}"))
        finally:
            cleanup_temp_file(temp_file_path)
    
    return updated_content, handwriting_results

async def process_single_attachment(temp_file_path: str, blob_path: str, ocr_prompt: str, ocr_fallback_prompt: str) -> str:
    """Process a single attachment and return the content to append."""
    try:
        # Try MarkItDown first
        result = process_file_with_markitdown(temp_file_path)
        
        if result['success']:
            return f"""

---

**📎 ATTACHMENT: {result['file_name']}**
*File Type: {result['file_type']} | Size: {result['file_size']:,} bytes | Processed with: MarkItDown*

{result['markdown_content']}

---
"""
        
        # MarkItDown failed, try OCR for visual files
        if is_visual_file(temp_file_path):
            # Try structured OCR first
            ocr_content = await extract_text_with_ocr(temp_file_path, ocr_prompt)
            
            if not is_content_successfully_extracted(ocr_content):
                # Try fallback OCR
                ocr_content = await _extract_text_with_custom_llm_fallback(temp_file_path, ocr_fallback_prompt)
            
            if is_content_successfully_extracted(ocr_content):
                file_size = os.path.getsize(temp_file_path)
                return f"""

---

**📎 ATTACHMENT: {os.path.basename(blob_path)}**
*File Type: {Path(blob_path).suffix.lower()} | Size: {file_size:,} bytes | Processed with: Custom OCR*

{ocr_content}

---
"""
        
        return create_failed_extraction_section(blob_path)
        
    except asyncio.TimeoutError:
        return create_timeout_section(blob_path)
    except Exception as e:
        return create_error_section(blob_path, e)

async def try_ocr_extraction(temp_file_path: str, blob_path: str, ocr_prompt: str, ocr_fallback_prompt: str) -> str:
    """Try OCR extraction for visual files."""
    logger.info(f"🔄 Attempting OCR for {blob_path}")
    
    try:
        ocr_content = await asyncio.wait_for(
            extract_text_with_ocr(temp_file_path, ocr_prompt),
            timeout=OCR_TIMEOUT_SECONDS
        )
        
        if ocr_content and ocr_content.strip() and ocr_content != "No readable text found":
            file_size = os.path.getsize(temp_file_path)
            return f"""

---

**📎 ATTACHMENT: {os.path.basename(blob_path)}**
*File Type: {Path(temp_file_path).suffix} | Size: {file_size:,} bytes | Processed with: Azure OCR*

{ocr_content}

---
"""
        else:
            logger.warning(f"✗ OCR found no readable text in {blob_path}")
            return create_failed_extraction_section(blob_path)
            
    except asyncio.TimeoutError:
        logger.error(f"✗ OCR timed out for {blob_path}")
        return create_timeout_section(blob_path)
    except Exception as e:
        logger.error(f"✗ OCR failed for {blob_path}: {e}")
        return create_error_section(blob_path, e)

# Helper functions
def is_visual_file(file_path: str) -> bool:
    """Check if file is a visual type that needs handwriting analysis."""
    return Path(file_path).suffix.lower() in VISUAL_FILE_EXTENSIONS

def is_content_successfully_extracted(content: str) -> bool:
    """Check if content was successfully extracted (not just error section)."""
    return (content and 
            len(content.strip()) > 0 and
            "⚠️ ATTACHMENT PROCESSING FAILED" not in content and
            "Custom OCR Fallback" not in content)

def create_non_visual_analysis(blob_path: str) -> AttachmentHandwrittenAnalysis:
    """Create handwriting analysis for non-visual files."""
    ext = Path(blob_path).suffix.lower()
    return AttachmentHandwrittenAnalysis(
        file=os.path.basename(blob_path),
        confidence=0,
        reasoning=f"Handwriting check not needed for file type {ext}"
    )

def create_error_section(blob_path: str, error: Exception) -> str:
    """Create error section for failed processing."""
    return f"""

---

**⚠️ PROCESSING FAILED: {os.path.basename(blob_path)}**
*Error: {type(error).__name__}*

---
"""

def create_failed_extraction_section(blob_path: str) -> str:
    """Create section for failed extraction."""
    return f"""

---

**⚠️ UNABLE TO EXTRACT CONTENT FROM: {os.path.basename(blob_path)}**
*All extraction methods failed*

---
"""

def create_timeout_section(blob_path: str) -> str:
    """Create section for timeout errors."""
    return f"""

---

**⚠️ PROCESSING TIMEOUT: {os.path.basename(blob_path)}**
*OCR processing timed out after {OCR_TIMEOUT_SECONDS} seconds*

---
"""

def cleanup_temp_file(temp_file_path: str):
    """Clean up temporary file."""
    try:
        os.unlink(temp_file_path)
    except Exception as e:
        logger.warning(f"Failed to cleanup temp file {temp_file_path}: {e}")

async def _extract_text_with_custom_llm_fallback(file_path: str, ocr_prompt: str) -> str:
    """
    Fallback OCR using custom LLM when MarkItDown fails.
    Uses the same LLM configuration as the rest of the application.
    """
    try:
        logger.info(f"Starting custom LLM OCR fallback for {file_path}")
        
        # Use the enhanced image processing
        data_url, payload_bytes = create_advanced_image_data_url(file_path, max_size=1200, max_bytes=150_000)
        if data_url is None:
            logger.warning(f"Failed to prepare image data URL for OCR fallback: {file_path}")
            return ""
        
        logger.info(f"Prepared image for OCR fallback: {file_path} ({payload_bytes} bytes)")

        # Use the passed prompt instead of hardcoded one
        # OCR prompt parameter is now passed in

        # Create direct LLM (not structured output since we want raw text)
        llm = settings.LLM(model=settings.LLM_MODEL_MULTI_MODAL)
        
        # Create the message in the same format as handwriting detection
        message = [
            {"role": "system", "content": ocr_prompt},
            {"role": "user", "content": [
                {"type": "text", "text": f"Please extract all text from this document: {os.path.basename(file_path)}"},
                {"type": "image_url", "image_url": {"url": data_url}}
            ]}
        ]

        logger.debug(f"Invoking custom LLM for OCR fallback: {file_path}")
        
        # Use direct LLM call (not structured output)
        response = await llm.ainvoke(message)
        
        # Extract text from response
        if hasattr(response, 'content'):
            extracted_text = response.content
        else:
            extracted_text = str(response)
        
        logger.info(f"Custom LLM OCR fallback completed for {file_path}: {len(extracted_text)} characters extracted")
        logger.debug(f"OCR extracted text preview: {extracted_text[:500]}...")
        
        return extracted_text

    except Exception as e:
        logger.error(f"Custom LLM OCR fallback failed for {file_path}: {type(e).__name__} - {e}")
        return ""

async def add_attachment_to_email_content(
    original_email_content: str,
    attachment_path: str,
    markitdown_instance: Optional['MarkItDown'] = None
) -> str:
    """
    Process an attachment with MarkItDown and custom OCR fallback, then append to email content
    
    Args:
        original_email_content: The original email content text
        attachment_path: Path to the attachment file to process
        markitdown_instance: MarkItDown instance to use (optional)
    
    Returns:
        Updated email content with attachment content appended
    """
    file_name = os.path.basename(attachment_path)
    file_ext = Path(attachment_path).suffix.lower()
    
    # Try MarkItDown first
    result = process_file_with_markitdown(attachment_path, markitdown_instance)
    
    if result['success']:
        # MarkItDown succeeded
        attachment_section = f"""

        ---

        **📎 ATTACHMENT: {result['file_name']}**
        *File Type: {result['file_type']} | Size: {result['file_size']:,} bytes | Processed with: MarkItDown*

        {result['markdown_content']}

        ---
        """
        
        updated_content = original_email_content + attachment_section
        logger.info(f"Successfully added attachment content via MarkItDown")
        return updated_content
        
    else:
        # MarkItDown failed
        logger.warning(f"MarkItDown failed for {file_name}: {result['error']}")
        
        # For visual files, try custom OCR fallback
        if is_visual_file(attachment_path):
            try:
                logger.info(f"Attempting custom OCR fallback for {file_name}")
                ocr_content = await _extract_text_with_custom_llm_fallback(attachment_path)
                
                if ocr_content and ocr_content.strip() and ocr_content != "No readable text found":
                    # Custom OCR succeeded
                    file_size = os.path.getsize(attachment_path)
                    attachment_section = f"""

        ---

        **📎 ATTACHMENT: {file_name}**
        *File Type: {file_ext} | Size: {file_size:,} bytes | Processed with: Custom OCR Fallback*

        {ocr_content}

        ---
        """
                    
                    updated_content = original_email_content + attachment_section
                    logger.info(f"Successfully added attachment content via custom OCR fallback")
                    return updated_content
                else:
                    logger.warning(f"Custom OCR fallback found no readable text in {file_name}")
                    
            except Exception as ocr_error:
                logger.error(f"Custom OCR fallback failed for {file_name}: {ocr_error}")
        
        # Both methods failed or not applicable
        error_section = f"""

        ---

        **⚠️ ATTACHMENT PROCESSING FAILED: {file_name}**
        *MarkItDown Error: {result['error']}*
        *Custom OCR: {"Not attempted (non-visual file)" if not is_visual_file(attachment_path) else "Also failed"}*

        ---
        """
        updated_content = original_email_content + error_section
        logger.error(f"All processing methods failed for {file_name}")
        return updated_content

def setup_markitdown(use_openai: bool = True) -> Optional['MarkItDown']:
    """
    Initialize MarkItDown using your existing Azure OpenAI LLM configuration
    """
    try:
        from markitdown import MarkItDown
        logger.info("MarkItDown imported successfully")
    except ImportError as e:
        logger.error(f"Failed to import MarkItDown: {e}")
        return None

    try:
        if use_openai:
            # Use your existing LLM configuration
            logger.info("Setting up MarkItDown with existing Azure OpenAI LLM")
            
            # Create LLM using your settings
            base_llm = settings.LLM(model=settings.LLM_MODEL_MULTI_MODAL)
            logger.debug(f"Created base LLM with model: {settings.LLM_MODEL_MULTI_MODAL}")
            
            # Extract the Azure OpenAI client
            if hasattr(base_llm, 'client') and base_llm.client is not None:
                azure_client = base_llm.client
                logger.info("✓ Found Azure OpenAI client")
                
                try:
                    # Initialize MarkItDown with your existing Azure OpenAI client
                    md = MarkItDown(
                        llm_client=azure_client,
                        llm_model=settings.LLM_MODEL_MULTI_MODAL
                    )
                    logger.info(f"✓ MarkItDown initialized with Azure OpenAI ({settings.LLM_MODEL_MULTI_MODAL})")
                    return md
                    
                except Exception as init_error:
                    logger.warning(f"✗ MarkItDown Azure OpenAI initialization failed: {init_error}")
            else:
                logger.warning("✗ Could not extract Azure OpenAI client from LLM")

        # Fallback to basic MarkItDown (no LLM integration)
        md = MarkItDown()
        logger.info("MarkItDown initialized (basic mode - no OCR)")
        return md

    except Exception as e:
        logger.error(f"Failed to initialize MarkItDown: {e}")
        return None

def get_markitdown_instance() -> Optional['MarkItDown']:
    """Get or create the global MarkItDown instance"""
    global _markitdown_instance

    if _markitdown_instance is None:
        _markitdown_instance = setup_markitdown(use_openai=True)

    return _markitdown_instance

def process_file_with_markitdown(
    file_path: str,
    markitdown_instance: Optional['MarkItDown'] = None
) -> Dict[str, Any]:
    """Process any supported file with MarkItDown with enhanced debugging"""
    # Use global instance if none provided
    if markitdown_instance is None:
        markitdown_instance = get_markitdown_instance()
    
    result = {
        "file_path": file_path,
        "file_name": os.path.basename(file_path),
        "file_size": 0,
        "file_type": "",
        "success": False,
        "markdown_content": None,
        "error": None,
        "processing_time": 0,
        "content_length": 0
    }
    
    # Check if file exists
    if not os.path.exists(file_path):
        result["error"] = "File not found"
        logger.error(f"File not found: {file_path}")
        return result
    
    # Get file info
    result["file_size"] = os.path.getsize(file_path)
    result["file_type"] = Path(file_path).suffix.lower()
    
    # Check if MarkItDown is available
    if not markitdown_instance:
        result["error"] = "MarkItDown not initialized"
        logger.error("MarkItDown not initialized")
        return result
    
    logger.info(f"Processing attachment: {result['file_name']} ({result['file_type']})")
    logger.debug(f"File size: {result['file_size']:,} bytes")
    
    # Enhanced debugging - check MarkItDown configuration
    has_llm = hasattr(markitdown_instance, 'llm_client') and markitdown_instance.llm_client is not None
    has_llm_model = hasattr(markitdown_instance, 'llm_model') and markitdown_instance.llm_model is not None
    logger.info(f"MarkItDown LLM client: {'present' if has_llm else 'missing'}")
    logger.info(f"MarkItDown LLM model: {getattr(markitdown_instance, 'llm_model', 'not set')}")

    try:
        start_time = time.time()

        # Process the file with MarkItDown
        logger.debug(f"Calling markitdown_instance.convert({file_path})")
        conversion_result = markitdown_instance.convert(file_path)
        
        processing_time = time.time() - start_time
        result["processing_time"] = processing_time

        # Enhanced debugging of conversion result
        logger.debug(f"Conversion completed in {processing_time:.2f}s")
        logger.debug(f"Conversion result type: {type(conversion_result)}")
        logger.debug(f"Conversion result: {conversion_result}")
        
        if conversion_result:
            logger.debug(f"Has text_content attribute: {hasattr(conversion_result, 'text_content')}")
            if hasattr(conversion_result, 'text_content'):
                content = conversion_result.text_content
                logger.debug(f"text_content type: {type(content)}")
                logger.debug(f"text_content length: {len(content) if content else 0}")
                logger.debug(f"text_content preview: {repr(content[:200]) if content else 'None'}")
            else:
                content = str(conversion_result)
                logger.debug(f"Conversion result as string: {repr(content[:200])}")
        else:
            logger.debug("Conversion result is None")
            content = None

        if conversion_result and content and content.strip():
            result["markdown_content"] = content
            result["content_length"] = len(content)
            result["success"] = True

            logger.info("Processing successful!")
            logger.debug(f"Processing time: {processing_time:.2f} seconds")
            logger.debug(f"Content length: {result['content_length']:,} characters")

        else:
            result["error"] = "MarkItDown returned empty result"
            logger.error("Processing failed: Empty result")
            logger.error(f"Conversion result details:")
            logger.error(f"  - Result object: {conversion_result}")
            logger.error(f"  - Content: {repr(content) if content else 'None'}")
            logger.error(f"  - Content stripped: {repr(content.strip()) if content else 'None'}")

    except Exception as e:
        result["error"] = str(e)
        result["processing_time"] = time.time() - start_time
        logger.error(f"Processing failed with exception: {e}", exc_info=True)

    return result

def process_multiple_attachments(
    original_email_content: str,
    attachment_paths: list[str],
    markitdown_instance: Optional['MarkItDown'] = None
) -> str:
    """
    Process multiple attachments and append all results to the email content
    
    Args:
        original_email_content: The original email content text
        attachment_paths: List of paths to attachment files to process
        markitdown_instance: MarkItDown instance to use (optional)
    
    Returns:
        Updated email content with all attachment content appended
    """
    updated_content = original_email_content
    
    for attachment_path in attachment_paths:
        if os.path.exists(attachment_path):
            updated_content = add_attachment_to_email_content(
                updated_content, 
                attachment_path, 
                markitdown_instance
            )
        else:
            logger.warning(f"Attachment not found: {attachment_path}")
            # Add missing file note
            error_section = f"""

            ---

            **⚠️ ATTACHMENT NOT FOUND: {os.path.basename(attachment_path)}**
            *File path: {attachment_path}*

            ---
            """
            updated_content += error_section
    
    return updated_content
