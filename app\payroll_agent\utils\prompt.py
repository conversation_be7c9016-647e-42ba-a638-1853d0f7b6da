import yaml

from pathlib import Path
from functools import lru_cache
from typing import Any, Dict, List, Union


def load_prompt(name: str) -> dict:
    path = Path(__file__).resolve().parents[1] / "prompts" / f"{name}.yml"
    with open(path, "r", encoding='utf-8') as f:
        return yaml.safe_load(f)


def load_library(name: Path) -> dict:
    path = Path(__file__).resolve().parents[1] / "libraries" / f"{name}.yml"
    with open(path, "r", encoding='utf-8') as f:
        return yaml.safe_load(f)


def format_json(obj, indent_step: int = 2) -> str:
    """
    Render a JSON-like object (dict/list/scalar) to a YAML-like string.
    Also appends exactly one trailing newline so that when combined with
    your joiner (\\n - {x}), you get a blank line between items.
    """
    def is_scalar(x: Any) -> bool:
        return isinstance(x, (str, int, float, bool)) or x is None

    def quote(s: str) -> str:
        s = s.replace("\\", "\\\\").replace('"', '\\"')
        return f'"{s}"'

    def render_scalar(x: Any) -> str:
        if isinstance(x, str):
            return quote(x)
        if isinstance(x, bool):
            return "true" if x else "false"
        if x is None:
            return "null"
        return str(x)

    def render(x, indent: int) -> List[str]:
        sp = " " * indent
        if is_scalar(x):
            return [sp + render_scalar(x)]
        if isinstance(x, list):
            if not x:
                return [sp + "[]"]
            lines: List[str] = []
            for item in x:
                if is_scalar(item):
                    lines.append(sp + "- " + render_scalar(item))
                else:
                    lines.append(sp + "-")
                    lines.extend(render(item, indent + indent_step))
            return lines
        # dict
        if not x:
            return [sp + "{}"]
        lines: List[str] = []
        for k, v in x.items():  # preserves insertion order
            if is_scalar(v):
                lines.append(sp + f"{k}: {render_scalar(v)}")
            else:
                lines.append(sp + f"{k}:")
                lines.extend(render(v, indent + indent_step))
        return lines

    lines = render(obj, 0)
    if not lines:
        return "\n"  # still produce a newline so joiner yields spacing

    head, tail = lines[0], lines[1:]
    tail = [(" " * indent_step) + t if t.strip() else t for t in tail]
    s = "\n".join([head] + tail)

    # ➜ add exactly one trailing newline so successive items get a blank line
    if not s.endswith("\n"):
        s += "\n"
    return s


def process_list(lis: List[str]) -> str:
    return "".join(f"\n - {x}" for x in lis)


@lru_cache(maxsize=None)
def format_base_agent_prompt(base_agent_prompt: str):

    # Read Guide
    guide = load_library("payroll_extraction_guide")

    rateType_rules = guide['rateType_rules']
    guardrails = guide['guardrails']['do_not']
    common_pitfalls = guide['common_pitfalls']
    payment_guide = guide['payment_guide']
    return base_agent_prompt.format(
        rateType_rules=process_list([format_json(y) for y in rateType_rules]),
        guardrails=process_list(guardrails),
        common_pitfalls=process_list(common_pitfalls),
        payment_guide=process_list([format_json(x) for x in payment_guide])
    )