import pytest

from app.payroll_agent.utils.funcs import plus_minus_period_iso


@pytest.mark.parametrize("iso_str, months, days, expected", [
    ("2025-09-05T14:14:34.363000", 1, 10, "2025-10-15T14:14:34Z"),
    ("2025-09-05T08:55:00Z", 2, 5, "2025-11-10T08:55:00Z"),
    ("2025-09-05T08:55:00", 0, 1, "2025-09-06T08:55:00Z"),
    ("2025-09-05T08:55:00.123456Z", 0, 0, "2025-09-05T08:55:00Z"),
    ("2025-09-05T08:55:00.123456", 0, 0, "2025-09-05T08:55:00Z"),
])
def test_plus_minus_period_iso(iso_str, months, days, expected):
    assert plus_minus_period_iso(iso_str, months, days) == expected
