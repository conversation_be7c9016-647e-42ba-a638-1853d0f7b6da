name: 'Deploy Payroll Email Agent (nonprod)'
description: 'Reusable action to deploy the payroll email agent to Azure Container Apps (nonprod)'

inputs:
  azure-subscription-id:
    description: 'Azure Subscription ID'
    required: true
  git-sha:
    description: 'Git SHA for image tagging'
    required: true
    default: ${{ github.sha }}

runs:
  using: 'composite'
  steps:
    - name: Set environment variables
      shell: bash
      run: |
        # Set fixed nonprod variables
        echo "AZURE_REGISTRY_LOGIN_SERVER=conpayrollainp001.azurecr.io" >> $GITHUB_ENV
        echo "AZURE_IMAGE_REPO=paychex/payroll-ai-agent" >> $GITHUB_ENV
        echo "AZURE_REGISTRY_USERNAME=conpayrollainp001" >> $GITHUB_ENV
        echo "DOCKER_DEFAULT_PLATFORM=linux/amd64" >> $GITHUB_ENV
        echo "IMAGE_TAG=nonprod" >> $GITHUB_ENV
        echo "AZURE_RESOURCE_GROUP=rg-payrollai-eastus-n0-001" >> $GITHUB_ENV
        echo "AZURE_CONTAINER_APP=con-emailagent-eastus-n0-001" >> $GITHUB_ENV

    - name: Get ACR access token (OIDC)
      id: get-acr-token
      uses: azure/CLI@v2
      with:
        inlineScript: |
          # Use OIDC-authenticated Azure session (from azure/login) to obtain a short-lived ACR access token
          TOKEN=$(az acr login --name ${{ env.AZURE_REGISTRY_USERNAME }} --expose-token --output tsv --query accessToken)
          echo "acr-token=$TOKEN" >> $GITHUB_OUTPUT

    - name: Docker login to ACR (OIDC token)
      uses: docker/login-action@v3
      with:
        registry: ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}
        username: 00000000-0000-0000-0000-000000000000
        password: ${{ steps.get-acr-token.outputs.acr-token }}

    - name: Build and push Docker image
      shell: bash
      run: |
        echo "Building docker image from commit: ${{ inputs.git-sha }} for nonprod"
        
        # Build image with both SHA and latest tags
        docker build -f Dockerfile.agent -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }} .
        docker build -f Dockerfile.agent -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest .
        
        echo "Pushing images to ACR..."
        docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }}
        docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest
        echo "Successfully pushed images to ACR"

    - name: Deploy to Azure Container App
      id: deploy-to-aca
      uses: azure/CLI@v2
      with:
        inlineScript: |
          set -e
          az account set --subscription ${{ inputs.azure-subscription-id }}
          az config set extension.use_dynamic_install=yes_without_prompt
          # Ensure containerapp extension is present (robust against dynamic install issues)
          az extension add -n containerapp -y || az extension update -n containerapp || true

          IMAGE_TAG="${{ env.IMAGE_TAG }}.${{ inputs.git-sha }}"
          FULL_IMAGE_NAME="${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:$IMAGE_TAG"

          echo "RG=${{ env.AZURE_RESOURCE_GROUP }} APP=${{ env.AZURE_CONTAINER_APP }}"
          echo "Deploying image: $FULL_IMAGE_NAME to ${{ env.AZURE_CONTAINER_APP }}"

          az containerapp update \
            --name "${{ env.AZURE_CONTAINER_APP }}" \
            --resource-group "${{ env.AZURE_RESOURCE_GROUP }}" \
            --image "$FULL_IMAGE_NAME" \
            --set-env-vars \
              OPENAI_API_KEY="$OPENAI_API_KEY" \
              LANGCHAIN_API_KEY="$LANGCHAIN_API_KEY" \
              MCP_SERVERS__PAYCHEX__CLIENT_ID="$MCP_SERVERS__PAYCHEX__CLIENT_ID" \
              LANGCHAIN_PROJECT="$LANGCHAIN_PROJECT" \
              MCP_SERVERS__PAYCHEX__CLIENT_SECRET="$MCP_SERVERS__PAYCHEX__CLIENT_SECRET" \
              LANGCHAIN_TRACING_V2="$LANGCHAIN_TRACING_V2" \
              MCP_SERVERS__PAYCHEX__TRANSPORT="$MCP_SERVERS__PAYCHEX__TRANSPORT" \
              MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="$MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL" \
              MCP_SERVERS__PAYCHEX__URL="$MCP_SERVERS__PAYCHEX__URL" \
              LANGCHAIN_ENDPOINT="$LANGCHAIN_ENDPOINT" \
              LLM_TYPE="$LLM_TYPE" \
              AZURE_ENDPOINT="$AZURE_ENDPOINT" \
              API_VERSION="$API_VERSION" \
              AZURE_STORAGE_ACCOUNT_URL="$AZURE_STORAGE_ACCOUNT_URL" \
              DASHBOARD_API_URL="$DASHBOARD_API_URL" \
              AZURE_CLIENT_ID="$AZURE_CLIENT_ID" \
              ENABLE_DASHBOARD_INTEGRATION="$ENABLE_DASHBOARD_INTEGRATION" \
              EASY_AUTH_ENABLED="$EASY_AUTH_ENABLED"
          
          APP_URL=$(az containerapp show --name "${{ env.AZURE_CONTAINER_APP }}" --resource-group "${{ env.AZURE_RESOURCE_GROUP }}" --query properties.configuration.ingress.fqdn -o tsv)
          echo "container-app-url=https://$APP_URL" >> $GITHUB_OUTPUT

    - name: Clean up Docker images
      if: always()
      shell: bash
      run: |
        docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.${{ inputs.git-sha }} || true
        docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:${{ env.IMAGE_TAG }}.latest || true

outputs:
  container-app-url:
    description: 'The URL of the deployed container app'
    value: ${{ steps.deploy-to-aca.outputs.container-app-url }}
