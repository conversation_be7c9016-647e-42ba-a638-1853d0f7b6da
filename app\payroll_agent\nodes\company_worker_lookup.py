import json


from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.company_worker_lookup import (
    SenderEmailMetadata,
    WorkersMatch,
    PayPeriod,
    MatchedPayPeriod,
    CompanyScoreList,
    CompanyScore,
    AttachmentHandwrittenAnalysis
)
from app.payroll_agent.utils.logging import log_runtime, log_llm_usage
from app.payroll_agent.utils.mcp import get_mcp_service
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.llm_utils import invoke_structured_llm_with_retry, create_llm_with_structured_output
from app.payroll_agent.utils.blob_storage import download_all_attachments_for_ingest
from app.payroll_agent.utils.attachments import (
    process_all_attachments,
)
from app.payroll_agent.utils.funcs import (
    load_routers_config,
    first_day_of_month,
    days_between,
    convert_to_iso_date,
    plus_minus_period_iso,
    name_map,
    full_name
)


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("company_worker_lookup")
ROUTERS_CONFIG = load_routers_config('routers_config')['company_worker_lookup']

@log_runtime("company_worker_lookup_identify_sender", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_identify_sender(state: PayrollState) -> PayrollState:
    """Identify the sender and extract relevant information from the email."""
    logger.info("company_worker_lookup_identify_sender called...")
    try:
        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(model = settings.LLM_MODEL_NON_REASONING),
            schema=SenderEmailMetadata,
        )

        prompt = PROMPTS["company_worker_lookup_identify_sender"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]
        logger.debug(f"LLM prompt for company_worker_lookup_identify_sender: {message}")

        # Invoke the LLM with the prompt
        sender_info, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message)
        log_llm_usage(
            state=state,
            step_name="company_worker_lookup_identify_sender",
            llm=llm,
            llm_response=sender_info,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            output_state_attr="company_worker_lookup_output_state"
        )
        logger.debug(f"company_worker_lookup_identify_sender got structured output: {sender_info!r}")

        state.company_worker_lookup_output_state.sender_email_metadata = sender_info
        state.company_worker_lookup_output_state.employees_metadata = sender_info.employees_names or []
        logger.debug(f"company_worker_lookup_identify_sender returning PayrollState: {state!r}")

        return {}
    except Exception as e:
        logger.error(f"Error in company_worker_lookup_identify_sender: {type(e).__name__} - {e}", exc_info=True)
        return {}


@log_runtime("company_worker_lookup_get_sender_metadata_description", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_get_sender_metadata_description() -> str:
    """Generate a string describing each field with optional/required label and description."""
    description_lines = []
    for name, field in SenderEmailMetadata.model_fields.items():
        required = field.is_required()
        desc = field.description or "No description"
        status = "required" if required else "optional"
        description_lines.append(f"- `{name}` ({status}): {desc}")
    return "\n".join(description_lines)

@log_runtime("company_worker_lookup_retrieve_company_info", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_retrieve_company_info(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_retrieve_company_info called...")
    try:
        mcp_service = get_mcp_service()
        display_ids = state.input_state.displayId
        
        # Handle both single string and list of display IDs
        if isinstance(display_ids, str):
            display_ids_list = [display_ids]
        elif isinstance(display_ids, list):
            display_ids_list = display_ids
        else:
            logger.error(f"Invalid displayId format: {display_ids}")
            return state
        
        logger.info(f"Fetching company info for displayIds: {display_ids_list}")
        
        # Fetch company info for all display IDs
        companies = []
        for display_id in display_ids_list:
            try:
                response = await mcp_service.call_mcp_tool(
                    server_name='paychex',
                    tool_name='paychex_company_lookup',
                    tool_input={"display_id": display_id},
                    session_id=state.input_state.x_payx_sid
                )
                response = json.loads(response)
                
                if response.get('ok'):
                    company = response.get('company', {})
                    company['display_id'] = display_id  # Add display_id for reference
                    companies.append(company)
                    logger.debug(f"Company lookup successful for {display_id}: {company}")
                else:
                    logger.warning(f"Company lookup failed for {display_id}: {response}")
            except Exception as e:
                logger.error(f"Error fetching company info for {display_id}: {e}")
        
        if not companies:
            logger.error("No valid companies found for any display ID")
            return state
        
        # If only one company found, use it directly
        if len(companies) == 1:
            selected_company = companies[0]
            logger.info(f"Single company found, using: {selected_company}")
            state.company_worker_lookup_output_state.company_scores = [
                CompanyScore(
                    company_id=selected_company.get("companyId"),
                    confidence=100,
                    reasoning="Only one company found"
                )
            ]
            state.company_worker_lookup_output_state.company_selection_reason = "Only one company found"
            state.company_worker_lookup_output_state.company_lookup = selected_company
        else:
            logger.info(f"Multiple companies found ({len(companies)}), scoring all with LLM")
            company_scores = await _score_all_companies(state, companies)
            state.company_worker_lookup_output_state.company_scores = company_scores
            # Do not select a company here; let the router decide

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_retrieve_company_info - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


@log_runtime("company_worker_lookup_process_attachments", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_process_attachments(state: PayrollState) -> PayrollState:
    """Download and process all attachments from blob storage for the ingest ID."""
    logger.info("Processing attachments...")

    try:
        ingest_id = state.input_state.ingestId
        temp_file_paths = await download_all_attachments_for_ingest(ingest_id)
        
        if not temp_file_paths:
            return _set_no_attachments_state(state)

        # Get prompts for OCR and handwriting detection
        ocr_prompt = PROMPTS.get("company_worker_lookup_ocr_extract_text", "")
        ocr_fallback_prompt = PROMPTS.get("company_worker_lookup_ocr_fallback", "")
        handwriting_prompt = PROMPTS.get("company_worker_lookup_handwritten_notes", "")

        updated_content, handwriting_results = await process_all_attachments(
            temp_file_paths, 
            state.input_state.EmailContent,
            ocr_prompt,
            ocr_fallback_prompt,
            handwriting_prompt,
            state
        )
        
        return _update_state_with_results(state, updated_content, handwriting_results, len(temp_file_paths))

    except Exception as e:
        logger.error(f"Attachment processing error: {e}", exc_info=True)
        return _set_processing_failed_state(state)

def _set_no_attachments_state(state: PayrollState) -> PayrollState:
    """Set state when no attachments are found."""
    logger.info("No attachments found in blob storage")
    state.company_worker_lookup_output_state.attachments_found = False
    state.company_worker_lookup_output_state.attachments_processed = False
    state.company_worker_lookup_output_state.attachment_handwritten_notes_found = False
    state.company_worker_lookup_output_state.attachment_handwritten_notes = []
    return {}

def _set_processing_failed_state(state: PayrollState) -> PayrollState:
    """Set state when processing fails."""
    state.company_worker_lookup_output_state.attachments_processed = False
    state.company_worker_lookup_output_state.attachment_handwritten_notes_found = False
    state.company_worker_lookup_output_state.attachment_handwritten_notes = []
    return {}

def _update_state_with_results(state: PayrollState, updated_content: str, handwriting_results: list, total_files: int) -> PayrollState:
    """Update state with processing results."""
    state.input_state.EmailContent = updated_content
    state.company_worker_lookup_output_state.attachments_found = True
    state.company_worker_lookup_output_state.attachments_processed = len(handwriting_results) > 0
    state.company_worker_lookup_output_state.attachment_handwritten_notes = handwriting_results
    state.company_worker_lookup_output_state.attachment_handwritten_notes_found = False
    
    logger.info(f"Updated email content: {len(updated_content)} characters")
    logger.info(f"Processed {len(handwriting_results)}/{total_files} attachments")
    return {}

@log_runtime("company_worker_lookup_LLM_search_workers",
              "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_LLM_search_workers(emailContent: str, fullname_dict: dict) -> WorkersMatch:
    """Search for workers in the email content using LLM."""
    llm = create_llm_with_structured_output(
        base_llm=settings.LLM(), schema=WorkersMatch
    )

    prompt = PROMPTS["company_worker_lookup_search_workers"]

    data = dict(emailContent=emailContent, availableWorkers=fullname_dict)
    message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": json.dumps(data)}
        ]
    logger.debug(
        f"LLM prompt for company_worker_lookup_search_workers: {message}"
    )

    # Invoke the LLM with the prompt
    search_result, input_tokens, output_tokens = await (
        invoke_structured_llm_with_retry(llm, message)
    )
    log_llm_usage(
        state=WorkersMatch,
        step_name="company_worker_lookup_LLM_search_workers",
        llm=llm,
        llm_response=search_result,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        output_state_attr="company_worker_lookup_output_state"
    )
    logger.debug(f"LLM search result: {search_result!r}")

    return search_result


@log_runtime("company_worker_lookup_retrieve_workers", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_retrieve_workers(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_retrieve_workers called...")
    try:
        mcp_service = get_mcp_service()
        company_id = state.input_state.companyID

        logger.debug(f"Fetching worker info for CompanyID: {company_id}")
        response = await mcp_service.call_mcp_tool(
            server_name='paychex',
            tool_name='paychex_workers_lookup',
            tool_input={"company_id": company_id},
            session_id=state.input_state.x_payx_sid
        )
        # Parse the response
        workers_roster = json.loads(response)
        state.company_worker_lookup_output_state.workers_roster = workers_roster

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_retrieve_workers - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


@log_runtime("company_worker_lookup_workers_router", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_workers_router(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_workers_router called...")
    try:
        workers_roster = state.company_worker_lookup_output_state.workers_roster
        if workers_roster.get('error', False):
            error_message = workers_roster['error']
            logger.warning(f"company_worker_lookup_retrieve_workers_router failure: {error_message}")
            state.company_worker_lookup_output_state.termination_reason = error_message
            state.company_worker_lookup_output_state.termination_node = "company_worker_lookup_workers_router"
            state.company_worker_lookup_output_state.should_continue = False
            state.company_worker_lookup_output_state.workers_search_success = False
        elif workers_roster is None:
            error_message = "No worker roster found"
            logger.warning(f"company_worker_lookup_retrieve_workers_router failure: {error_message}")
            state.company_worker_lookup_output_state.termination_reason = error_message
            state.company_worker_lookup_output_state.termination_node = "company_worker_lookup_workers_router"
            state.company_worker_lookup_output_state.should_continue = False
            state.company_worker_lookup_output_state.workers_search_success = False
        else:
            state.company_worker_lookup_output_state.workers_search_success = True

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_retrieve_workers_router - {type(e).__name__} - {e}", exc_info=True)
        state.company_worker_lookup_output_state.termination_reason = str(e)
        state.company_worker_lookup_output_state.workers_search_success = False
        state.company_worker_lookup_output_state.termination_node = "company_worker_lookup_workers_router"
        state.company_worker_lookup_output_state.should_continue = False
    finally:
        return state


@log_runtime("company_worker_lookup_match_workers", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_match_workers(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_match_workers called...")
    try:
        workers_roster = state.company_worker_lookup_output_state.workers_roster
        workers = workers_roster['content']

        logger.info(f"Number of workers: {len(workers)}")

        # get full names for llm-search-based
        fullname_dict = {full_name(worker): worker['workerId'] for worker in workers}
        logger.info(f"Extracted names from worker roster: {fullname_dict}")


        ## LLM search
        llm_workers_match = await company_worker_lookup_LLM_search_workers(
            emailContent=state.input_state.EmailContent,
            fullname_dict=fullname_dict
        )
        logger.debug(f"llm_workers_match: {llm_workers_match}")

        # Store the structured result
        state.company_worker_lookup_output_state.llm_workers_match = llm_workers_match

        # Log extracted names
        extracted_names = [w.extracted_name for w in llm_workers_match.workers]
        logger.info(f"Extracted names from LLM: {extracted_names}")

        ### Build workers_matched dict for downstream processing
        workers_matched = {}
        for worker in llm_workers_match.workers:
            if worker.closest_match_successful and worker.worker_number is not None:
                worker_data = next((w for w in workers if w['workerId'] == str(worker.worker_number)), None)
                if worker_data:
                    workers_matched[worker.extracted_name] = worker_data

        state.company_worker_lookup_output_state.workers_matched = workers_matched

        # Log summary using structured data
        summary = llm_workers_match.summary
        logger.info(f"Worker matching summary:")
        logger.info(f"  - Total extracted: {summary.total_extracted}")
        logger.info(f"  - Successful matches: {summary.successful_matches}")
        logger.info(f"  - Confidence threshold: {summary.confidence_threshold}%")

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_match_workers - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


@log_runtime("company_worker_lookup_get_pay_period", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_get_pay_period(email_content: str, pay_periods: list, timestamp:str) -> MatchedPayPeriod | None:
    """Extract the check date from the email content."""
    try:
        llm = create_llm_with_structured_output(base_llm=settings.LLM(), schema=MatchedPayPeriod)

        prompt = PROMPTS["company_worker_lookup_search_pay_period"]

        content = dict(
            emailContent = email_content,
            payPeriods = [x.model_dump() for x in pay_periods],
            received_on = timestamp
        )

        message = [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": json.dumps(content)}
                ]

        # Invoke the LLM with the prompt
        search_result, input_tokens, output_tokens  = await invoke_structured_llm_with_retry(llm, message)
        log_llm_usage(
                state=MatchedPayPeriod,
                step_name="company_worker_lookup_get_payperiodID",
                llm=llm,
                llm_response=search_result,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                output_state_attr="company_worker_lookup_output_state"
            )
        logger.debug(f"LLM get_pay_period result: {search_result!r}")
        return search_result

    except Exception as e:
        logger.error(f"Error finding a pay period- {type(e).__name__} - {e}", exc_info=True)
        return None


@log_runtime("company_worker_lookup_get_payperiodID", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_lookup_get_payperiodID(state: PayrollState) -> dict:
    logger.info("company_worker_lookup_get_payperiodID called...")
    try:
        timestamp = state.input_state.timestamp
        company_id = state.input_state.companyID

        ## get pay periods
        mcp_service = get_mcp_service()

        from_iso = plus_minus_period_iso(first_day_of_month(timestamp), months=-1)
        to_iso = plus_minus_period_iso(timestamp, months=1, days=15)

        logger.debug(f"Fetching last pay periods for CompanyID: {company_id}, Using date range: {from_iso} to {to_iso}")

        response = await mcp_service.call_mcp_tool(
            server_name='paychex',
            tool_name='paychex_company_pay_periods',
            tool_input= {
                'company_id': company_id,
                'kwargs' :
                    {
                        'status': 'INITIAL',
                        'from_': from_iso,
                        'to': to_iso,
                    }
                },
                session_id=state.input_state.x_payx_sid
            )

        # Parse the response and add it to the state
        response = json.loads(response)

        if response['ok']:
            pay_periods = [PayPeriod.model_validate(x) for x in response['content']]
            state.company_worker_lookup_output_state.pay_periods = pay_periods

            pay_period_reponse = await company_worker_lookup_get_pay_period(
                email_content=state.input_state.EmailContent,
                pay_periods=pay_periods,
                timestamp=convert_to_iso_date(timestamp)
            )

            state.company_worker_lookup_output_state.payperiod = pay_period_reponse

            if pay_period_reponse.status == 'SUCCESS':
                state.company_worker_lookup_output_state.payperiodID = pay_period_reponse.payPeriodId
            else:
                state.company_worker_lookup_output_state.payperiodID = None
                logger.warning(f"Pay period not found for company {company_id}")

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_get_payperiodID - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return {}


@log_runtime("company_worker_lookup_terminate", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_terminate(state: PayrollState) -> PayrollState:
    """Terminate account_lookup with reason when client ID is invalid."""
    logger.warning("company_worker_lookup_terminate called...")
    reason = state.company_worker_lookup_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.company_worker_lookup_output_state.should_continue = False
    state.company_worker_lookup_output_state.termination_node = company_worker_lookup_terminate.__name__
    logger.debug(f"company_worker_lookup_terminate returning PayrollState: {state}")

    return state


@log_runtime("company_worker_lookup_router", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_router(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_router called...")

    company_data = state.company_worker_lookup_output_state

    # Fix: Check if llm_workers_match is None before accessing its workers attribute
    if company_data.llm_workers_match is None:
        logger.warning("llm_workers_match is None, setting empty list for workers")
        llm_workers = []
    else:
        llm_workers = company_data.llm_workers_match.workers or []

    # Also check if workers_matched is None or empty
    matched_workers = list(company_data.workers_matched.values()) if company_data.workers_matched else []

    failures = []

    if company_data.payperiodID is None:
        failures.append("No pay period found for the company")
    elif days_between(company_data.payperiod.checkDate, state.input_state.timestamp) > ROUTERS_CONFIG['max_days_between_check_date_and_pay_period']:
        failures.append(f"Check date {company_data.payperiod.checkDate} is too far from the received email date {state.input_state.timestamp}")
    else:
        pass

    # Check confidence only if we have workers
    if not llm_workers:
        failures.append("No workers found in LLM match")
    else:
        low_confidence_workers = [
            f"{x.extracted_name} ({x.closest_match_confidence}%)" 
            for x in llm_workers 
            if x.closest_match_confidence < ROUTERS_CONFIG["llm_name_matching_confidence"]
        ]
        if low_confidence_workers:
            failures.append(f"May be some workers terminated since LLM name matching confidence too low for {len(low_confidence_workers)}/{len(llm_workers)} workers: {', '.join(low_confidence_workers)}")

    if not matched_workers:
        failures.append("No matched workers found")
    elif len(matched_workers) < ROUTERS_CONFIG['min_num_of_active_employees']:
        failures.append("Not enough active employees")

    # Check if employees metadata is empty, but allow if LLM found workers
    # This handles cases like "same as last period" where no specific names
    # are mentioned but LLM worker search correctly identifies all workers
    if len(company_data.employees_metadata) == 0 and not matched_workers:
        failures.append(
            f"No employees detected in the email and no workers matched, got "
            f"{company_data.employees_metadata}"
        )

    if not failures:
        logger.info("Client config valid and payroll is for one client")
        company_data.worker_lookup_success = True
        company_data.should_continue = True
    else:
        error_message = "; ".join(failures)
        logger.warning(f"company_worker_lookup_router failure: {error_message}")
        company_data.termination_reason = error_message
        company_data.termination_node = "company_worker_lookup_router"
        company_data.should_continue = False

    return state


@log_runtime("company_worker_lookup_company_router", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_initial_router(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_company_router called...")

    company_data = state.company_worker_lookup_output_state
    failures = []

    # Company selection logic
    company_scores = company_data.company_scores or []
    CONFIDENCE_THRESHOLD = ROUTERS_CONFIG.get('company_selection_confidence_threshold', 79)
    over_threshold = [score for score in company_scores if score.confidence > CONFIDENCE_THRESHOLD]

    if len(over_threshold) == 1:
        selected_company_id = over_threshold[0].company_id
        logger.info(f"Selected company: {selected_company_id} (confidence: {over_threshold[0].confidence})")
        state.input_state.companyID = selected_company_id
        company_data.company_id_success = True
        company_data.company_selection_reason = (
            f"Selected only company with confidence above {CONFIDENCE_THRESHOLD}: {selected_company_id} ({over_threshold[0].confidence})"
        )
    elif len(over_threshold) > 1:
        error_message = (
            f"Ambiguous company match: multiple companies above confidence threshold {CONFIDENCE_THRESHOLD}: "
            + ", ".join(f"{score.company_id} ({score.confidence}%)" for score in over_threshold)
        )
        logger.warning(f"company_worker_lookup_company_router failure: {error_message}")
        failures.append(error_message)
        company_data.company_id_success = False
        company_data.should_continue = False
    else:
        error_message = (
            f"No company match above confidence threshold {CONFIDENCE_THRESHOLD}. "
            f"Scores: " + ", ".join(f"{score.company_id} ({score.confidence}%)" for score in company_scores)
        )
        logger.warning(f"company_worker_lookup_company_router failure: {error_message}")
        failures.append(error_message)
        company_data.company_id_success = False
        company_data.should_continue = False

    # Additional initial validation
    if not hasattr(company_data, 'sender_email_metadata') or company_data.sender_email_metadata is None:
        failures.append("Sender identification failed")

    # Attachment processing and handwritten-note decision
    if company_data.attachments_found:
        if not company_data.attachments_processed:
            failures.append("Attachment processing failed")
            logger.warning("Attachment processing failed - manual review")
            # keep handwritten flag conservative when processing failed
            company_data.attachment_handwritten_notes_found = False
        else:
            # compute handwritten flag from stored analyses (robust to dict or model instances)
            attachments = getattr(company_data, "attachment_handwritten_notes", []) or []
            def _conf(item):
                """
                Return integer confidence.
                - Treat 0 as a valid confidence.
                - If confidence is missing or unparseable, return 100 (conservative error).
                """
                try:
                    if isinstance(item, dict):
                        val = item.get("confidence", None)
                    else:
                        val = getattr(item, "confidence", None)
                    if val is None:
                        return 100
                    return int(val)
                except Exception:
                    return 100
            logger.warning("Attachment processed successfully, checking handwritten check result")
 
            threshold = ROUTERS_CONFIG.get("handwritten_confidence_threshold", 79)
            found = any(_conf(a) > threshold for a in attachments)
            if found:
                conf = [_conf(a) for a in attachments]
                failures.append(f"Handwritten notes check failed: Conf: {conf} threshold: {threshold}")

            if company_data.attachment_handwritten_notes_found:
                failures.append("Handwritten notes detected in attachments")
                logger.warning("Handwritten notes detected - manual review")

    if company_data.termination_reason:
        failures.append(f"Previous step failed: {company_data.termination_reason}")

    if not failures:
        logger.info("Company selection and initial validation passed - proceeding to next phase")
        company_data.initial_validation_success = True
        company_data.should_continue = True
    else:
        error_message = "; ".join(failures)
        logger.warning(f"company_worker_lookup_company_router validation failed: {error_message}")
        company_data.termination_reason = error_message
        company_data.termination_node = "company_worker_lookup_initial_router"
        company_data.initial_validation_success = False
        company_data.should_continue = False

    return state


@log_runtime("company_worker_get_agent_context", "company_worker_lookup_", "company_worker_lookup_output_state")
async def company_worker_get_agent_context(state: PayrollState) -> PayrollState:
    """Get the agent context for the company worker lookup."""
    logger.info("company_worker_get_agent_context called...")
    try:

        name_mapping = name_map(state.company_worker_lookup_output_state.llm_workers_match)
        worker_ids = [d['worker_id'] for d in name_mapping.values()]

        # get MCP service
        mcp_service = get_mcp_service()

        context = await mcp_service.call_mcp_tool(
            server_name="paychex",
            tool_name="paychex_get_agent_context",
            tool_input={
                "company_id": state.input_state.companyID,
                "timestamp": state.input_state.timestamp,
                "pay_period_id": state.company_worker_lookup_output_state.payperiodID,
                "worker_ids": worker_ids
            },
            session_id=state.input_state.x_payx_sid
        )

        result = json.loads(context)

        # map names and worker_ids
        worker_matched = state.company_worker_lookup_output_state.workers_matched
        workers_info_old = result.get('workers_info', {})

        new_workers_info = dict()
        for worker_name, info in worker_matched.items():
            new_workers_info[worker_name] = workers_info_old.get(info.get('workerId'), {})
            new_workers_info[worker_name]['worker_id'] = info.get('workerId')
            new_workers_info[worker_name]['registered_name'] = full_name(info)

        # update workers_info with new worker_ids and names
        result['workers_info'] = new_workers_info

        # save context to state
        state.company_worker_lookup_output_state.agent_context = result
    except Exception as e:
        logger.error(f"Error in company_worker_get_agent_context - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


@log_runtime("company_worker_lookup_finishing_node", "company_worker_lookup_", "company_worker_lookup_output_state")
def company_worker_lookup_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_finishing_node called...")
    return state


async def _score_all_companies(state: PayrollState, companies: list) -> list[dict]:
    """Use LLM to score all companies from multiple options, with detailed logging."""
    try:
        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(model=settings.LLM_MODEL_NON_REASONING),
            schema=CompanyScoreList,
        )

        company_options = []
        mcp_service = get_mcp_service()
        for company in companies:
            company_dict = dict(company)
            company_id = company.get('companyId') or company.get('company_id')
            workers = []
            if company_id:
                try:
                    response = await mcp_service.call_mcp_tool(
                        server_name='paychex',
                        tool_name='paychex_workers_lookup',
                        tool_input={"company_id": company_id},
                        session_id=state.input_state.x_payx_sid
                    )
                    workers_roster = json.loads(response)
                    all_workers = workers_roster.get('content', [])
                    # Only include selected fields for each worker
                    for w in all_workers:
                        workers.append({
                            'workerId': w.get('workerId'),
                            'workerType': w.get('workerType'),
                            'hireDate': w.get('hireDate'),
                            'name': w.get('name'),
                            'currentStatus': w.get('currentStatus')
                        })
                except Exception as e:
                    logger.error(f"Error fetching workers for company {company_id}: {e}")
            company_dict['workers'] = workers
            company_options.append(company_dict)

        prompt = PROMPTS["company_worker_lookup_select_company"].format(
            company_options=json.dumps(company_options, indent=2)
        )

        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]

        logger.debug(f"LLM message payload: {json.dumps(message, indent=2)}")

        # Invoke the LLM
        try:
            score_list, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message)
            logger.info(f"Raw LLM output from company match: {score_list}")
            log_llm_usage(
                state=state,
                step_name="company_worker_lookup_score_all_companies",
                llm=llm,
                llm_response=score_list,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                output_state_attr="company_worker_lookup_output_state"
            )
            logger.debug(f"Company scoring result (parsed): {score_list}")
            return score_list.scores
        except Exception as parse_exc:
            logger.error(f"Error parsing LLM output: {parse_exc}")
            return []

    except Exception as e:
        logger.error(f"Error in LLM company scoring setup: {e}", exc_info=True)
        return []
