import yaml
import logging

from pathlib import Path
from typing import List, Literal, Optional
from pydantic import BaseModel, Field, create_model

from app.payroll_agent.utils.knockouts import load_knockout_library, create_knockout_model, create_knockout_batch_models, create_knockout_rules_text_for_batch

logger = logging.getLogger(__name__)


with open(Path(__file__).parent.parent / "config" / "rules_config.yml", "r", encoding="utf-8") as f:
    rules_config = yaml.safe_load(f)
max_rules_per_chunk = rules_config["classification"]["business_knockout"]["max_rules_per_chunk"]


def load_classification_models():
    """Load classification models from YAML configuration."""
    config_path = Path(__file__).parent.parent / "libraries" / "classification.yaml"
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    models = {}
    
    for model_name, model_config in config.items():
        fields = {}
        for field_name, field_config in model_config.get('fields', {}).items():
            field_type = field_config.get('type', 'str')
            required = field_config.get('required', True)
            description = field_config.get('description', '')
            options = field_config.get('options', [])
            
            # Determine the field type
            if field_type == 'int':
                python_type = int
            elif field_type == 'str':
                python_type = str
            elif field_type == 'list':
                python_type = List[str]
            elif field_type == 'literal' and options:
                python_type = Literal[tuple(options)]
            else:
                python_type = str
            
            # Create the field
            if required:
                field_value = Field(..., description=description)
            else:
                field_value = Field(default=None, description=description)
            
            fields[field_name] = (python_type, field_value)
        
        # Create the model class name
        class_name = ''.join(word.capitalize() for word in model_name.split('_'))
        
        # Create the Pydantic model
        model_class = create_model(class_name, **fields)
        models[class_name] = model_class
    
    return models


# Load data
classification_models = load_classification_models()
business_knockout_rules = load_knockout_library(Path(__file__).parent.parent / "libraries" / "classification_business_knockout_library.yaml")
technical_knockout_rules = load_knockout_library(Path(__file__).parent.parent / "libraries" / "classification_technical_knockout_library.yaml")

# Create model instances
IntentTriage = classification_models['IntentTriage']
RequestTypeTags = classification_models['RequestTypeTags']
ComplexityTags = classification_models['ComplexityTags']

# For business knockouts
BusinessKnockouts = create_knockout_model(business_knockout_rules, model_name="BusinessKnockouts")
BUSINESS_KNOCKOUT_BATCHES = create_knockout_batch_models(business_knockout_rules, max_rules_per_chunk, model_prefix="BusinessBatch")

# For technical knockouts
TechnicalKnockouts = create_knockout_model(technical_knockout_rules, model_name="TechnicalKnockouts")
TECHNICAL_KNOCKOUT_BATCHES = create_knockout_batch_models(technical_knockout_rules, max_rules_per_chunk, model_prefix="TechnicalBatch")

# Create constants
TRIAGE_FIELDS_TEXT = "Your existing triage fields text"  # Replace with actual content

# Exports
__all__ = [
    'IntentTriage', 'RequestTypeTags', 'ComplexityTags', 
    'BusinessKnockouts', 'TechnicalKnockouts', 
    'BUSINESS_KNOCKOUT_BATCHES', 'TECHNICAL_KNOCKOUT_BATCHES',
    'business_knockout_rules', 'technical_knockout_rules',
    'TRIAGE_FIELDS_TEXT',
    'create_knockout_rules_text_for_batch',
    'create_knockout_model', 'create_knockout_batch_models'
]
