{"cells": [{"cell_type": "markdown", "id": "866953fb", "metadata": {}, "source": ["### Ground Truth Test Cases Formatting Notebook\n", "\n", "#### Overview\n", "This notebook processes the Payroll Processing Golden Set Excel file and converts it into the JSON format required for API endpoint testing.\n", "\n", "#### Purpose\n", "- Convert Excel ground truth data into structured JSON test cases\n", "- Format data for the `/process-email` endpoint evaluation\n", "- Extract and clean email content from raw email strings\n", "- Generate test cases with proper metadata and expected outcomes\n", "\n", "#### Input\n", "- **File**: `Payroll_processing_golden_set.xlsx` from SharePoint\n", "- **Location**: Download from [SharePoint Link](https://paychex-my.sharepoint.com/:x:/r/personal/sjuel_paychex_com/_layouts/15/Doc.aspx?sourcedoc=%7B95E3F005-184C-4372-81D2-03C4ABC82916%7D&file=Payroll_processing_golden_set.xlsx)\n", "- **Expected Columns**: \n", "  - `displayID`: Test case identifier(s)\n", "  - `Full email`: Raw email content\n", "  - `TImeStamp`: Email timestamp\n", "  - `New GT ID`: Unique test case ID\n", "  - `From_email`: Sender email address\n", "  - `To_email`: Recipient email address(es)\n", "  - `Status`: Test case validation status\n", "\n", "#### Output\n", "- **File**: `{date}_Payroll_processing_golden_set.json`\n", "- **Format**: JSON array of test cases formatted for API consumption\n", "- **Structure**: Each test case contains:\n", "  - Display ID and metadata\n", "  - Email content (converted from HTML to plain text)\n", "  - Sender/recipient information\n", "  - Timestamps in ISO format\n", "\n", "#### Usage Instructions\n", "1. Download the latest GT Excel file from SharePoint\n", "2. Update the `input_path` variable with the correct filename\n", "3. Run all cells in sequence\n", "4. Generated JSON file will be saved to the evaluation directory\n", "\n", "#### Notes\n", "- The notebook handles HTML-to-text conversion for email content\n", "- Email parsing extracts sender/recipient information using regex patterns\n", "- DisplayID fields are converted to lists to handle multiple test scenarios\n", "- Timestamps are standardized to ISO format for API compatibility"]}, {"cell_type": "markdown", "id": "03154989", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 5, "id": "8abdea39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (2025.5.1)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.1.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install fsspec\n"]}, {"cell_type": "code", "execution_count": 6, "id": "008ead25", "metadata": {}, "outputs": [], "source": ["base_path = r'C:\\Users\\<USER>\\payroll-email-agent\\app\\payroll_agent\\evaluation\\ground_truth'\n", "input_path =r\"\\250911_Payroll_processing_golden_set.xlsx\"\n", "output_file = r'\\250911_Payroll_processing_golden_set.json'"]}, {"cell_type": "markdown", "id": "3e3134f9", "metadata": {}, "source": ["#### Import"]}, {"cell_type": "code", "execution_count": 7, "id": "84f0ec792a75a827", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "\n", "import email"]}, {"cell_type": "markdown", "id": "70936e15", "metadata": {}, "source": ["#### Functions"]}, {"cell_type": "code", "execution_count": 8, "id": "d14965e4d58ee68", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:09.611150Z", "start_time": "2025-07-21T04:51:09.601215Z"}}, "outputs": [], "source": ["def html_to_plain_text(raw_email_string):\n", "    try:\n", "        # Step 1: Convert the raw string to bytes\n", "        raw_bytes = raw_email_string.encode('utf-8', errors='ignore')\n", "        # Step 2: Parse email message\n", "        msg = email.message_from_bytes(raw_bytes)\n", "        # Step 3: Walk through parts and extract the plain or HTML content\n", "        plain_parts = []\n", "        html_parts = []\n", "        for part in msg.walk():\n", "            content_type = part.get_content_type()\n", "            content_disposition = str(part.get('Content-Disposition'))\n", "            if 'attachment' in content_disposition:\n", "                continue # skip attachments\n", "            payload = part.get_payload(decode=True)\n", "            charset = part.get_content_charset() or 'utf-8'\n", "            if content_type == 'text/plain':\n", "                text = payload.decode(charset, errors='replace')\n", "                plain_parts.append(text)\n", "            elif content_type == 'text/html':\n", "                html = payload.decode(charset, errors='replace')\n", "                html_parts.append(html)\n", "        if plain_parts:\n", "            return \"\\n\".join(plain_parts).strip()\n", "        elif html_parts:\n", "            # If no plain text, fall back to BeautifulSoup HTML parsing\n", "            combined_html = \"\\n\".join(html_parts)\n", "            soup = BeautifulSoup(combined_html, 'html.parser')\n", "            return soup.get_text().strip()\n", "        else:\n", "            return \"\"\n", "    except Exception as e:\n", "        print(f\"[ERROR] Failed to parse email: {e}\")\n", "        return \"\"\n", "\n", "def extract_from_email_regex(raw_email):\n", "    # 1) Capture the entire 'From:' header (up to the next newline).\n", "    # We assume there's only one From: line and it's on a single line.\n", "    from_header_pattern = re.compile(\n", "        r'^From:\\s*(.*?)\\r?\\n'\n", "        , # Capture everything on the From: line\n", "        re.IGNORECASE | re.MULTILINE\n", "        )\n", "\n", "    match = from_header_pattern.search(raw_email)\n", "    if not match:\n", "        print(\"No 'From:' header found.\")\n", "        return None\n", "    else:\n", "        from_line = match.group(1).strip()\n", "        # 2) Extract the address from the from_line.\n", "        # We allow two forms:\n", "        # <AUTHOR> <EMAIL> or Name <email@domain>\n", "        # (B) Bare email: email@domain\n", "        from_email_pattern = re.compile(\n", "            r'(?:\"[^\"]*\"|[^\"<]+)?<([^>]+)>' # bracketed email\n", "            r'|([a-zA-Z0-9_.+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9.\\-]+)'\n", "            , # bare email\n", "            re.IGNORECASE\n", "            )\n", "        email_match = from_email_pattern.search(from_line)\n", "        if email_match:\n", "            # Exactly one of group(1) or group(2) will be non-empty\n", "            email_addr = email_match.group(1) if email_match.group(1) else email_match.group(2)\n", "            # print(\"From email is:\", email_addr)\n", "            return email_addr\n", "        else:\n", "            print(\"No email address found in From: header.\")\n", "        return None\n", "\n", "\n", "def extract_to_email_regex(raw_email):\n", "    # 1) Capture the entire 'To:' header. This regex:\n", "    # - Starts at a line beginning with 'To:'\n", "    # - Grabs everything until the next header line or end of string\n", "    # - Uses DOTALL so we can capture multiple lines.\n", "    to_header_pattern = re.compile(\n", "        r'^To:\\s*(.*?)\\n(?=[A-Za-z-]+?:|$)'\n", "        ,\n", "        re.IGNORECASE | re.DOTALL | re.MULTILINE\n", "        )\n", "    # 2) Extract all addresses. We have two possible forms:\n", "    # (A) Display name + < email >\n", "    # (B) Bare email (no < >).\n", "    # We'll use alternation in the same regex to handle both patterns.\n", "    # - The capture groups are (1) for the bracketed email, (2) for the bare email.\n", "    addresses_pattern = re.compile(\n", "        r'(?:\"[^\"]*\"|[^\"<,]+)?<([^>]+)>' # <AUTHOR> <EMAIL>\n", "        r'|([a-zA-Z0-9_.+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9.\\-]+)', # or abare email\n", "        re.IGNORECASE\n", "        )\n", "    match = to_header_pattern.search(raw_email)\n", "    if match:\n", "        to_line = match.group(1)\n", "        # 3) Find all possible addresses:\n", "        addresses = []\n", "        for m in addresses_pattern.finditer(to_line):\n", "            # One (and only one) group in each match will be non-empty\n", "            bracketed_email = m.group(1)\n", "            bare_email = m.group(2)\n", "            # Whichever one is not None is our address\n", "            email = bracketed_email if bracketed_email else bare_email\n", "            addresses.append(email)\n", "        if not addresses:\n", "            print(\"No to addresses found.\")\n", "        else:\n", "            # 4) Return the *first* address that ends in @acme.com.\n", "            # If none match, return the very first address found.\n", "            px_address = next((addr for addr in addresses if\n", "            addr.lower().endswith('@paychex.com')), None)\n", "            if px_address:\n", "                # print(\"Selected address:\", px_address)\n", "                return px_address\n", "            else:\n", "                # print(\"Selected address:\", addresses[0])\n", "                return addresses[0]\n", "    else:\n", "        print(\"No 'To:' line found.\")\n", "    return None\n", "\n", "# Now apply to your DataFrame\n", "\n", "def process_emails(df):\n", "    # Convert HTML to plain text\n", "    df['displayID'] = df['displayID'].astype(str)\n", "    df['plain_text_email'] = df['Full email'].apply(html_to_plain_text)\n", "    df['TimeStamp'] = pd.to_datetime(df['TImeStamp'], errors='coerce')\n", "    return df\n", "\n", "\n", "def map_case_to_placeholder(row):\n", "    \"\"\"Maps a test case to the placeholder structure.\"\"\"\n", "    return {\n", "      \"displayId\": row['displayID'],\n", "      \"comment\": {\n", "        \"uid\": str(row['New GT ID']),\n", "        \"id\":  str(row['New GT ID']),\n", "        \"timestamp\": row['TimeStamp'].isoformat(),\n", "        \"user_properties\": {\n", "          \"string:Sender\": row['From_email'],\n", "          \"string:Sender Domain\": row['From_email'],\n", "        },\n", "        \"messages\": [\n", "          {\n", "            \"from\": row['From_email'],\n", "            \"to\": row['T'] if type(row['To_email']) == list else [row['To_email']] ,\n", "            \"body\": {\n", "              \"text\": row['plain_text_email'],\n", "            },\n", "            \"subject\": {\n", "              \"text\":  \"Subject\",\n", "            }\n", "          }\n", "        ]\n", "      }\n", "    }\n", "\n", "def write_upstream_test_file(test_path, df):\n", "    path = os.path.join('evaluation_cases', test_path)\n", "    js = [map_case_to_placeholder(case) for i, case in df.iterrows()]\n", "\n", "    output_path = os.path.join('evaluation_cases', test_path)\n", "    with open(output_path, 'w', encoding='utf-8') as f:\n", "      json.dump(js, f, indent=2, ensure_ascii=False)"]}, {"cell_type": "markdown", "id": "b8fac70b", "metadata": {}, "source": ["#### Load in latest GT"]}, {"cell_type": "code", "execution_count": 9, "id": "23fb8fa4b19724cf", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:10.539326Z", "start_time": "2025-07-21T04:51:10.487085Z"}}, "outputs": [], "source": ["data_path = base_path + input_path\n", "df = pd.read_excel(data_path, sheet_name='Golden_set')"]}, {"cell_type": "code", "execution_count": 10, "id": "4e87a426", "metadata": {}, "outputs": [], "source": ["# Update displayID to list\n", "def to_list(x):\n", "    if pd.isna(x):\n", "        return []\n", "    if isinstance(x, list):\n", "        return x\n", "    s = str(x).strip()\n", "    # try parse JSON-like list, otherwise split on commas\n", "    try:\n", "        v = ast.literal_eval(s)\n", "        return v if isinstance(v, list) else [str(v)]\n", "    except Exception:\n", "        return [part.strip() for part in s.split(\",\") if part.strip()]\n", "\n", "# convert column in-place\n", "df[\"displayID\"] = df[\"displayID\"].apply(to_list)"]}, {"cell_type": "code", "execution_count": 11, "id": "f5bf1c9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["['11111181']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"displayID\"].iloc[0]"]}, {"cell_type": "code", "execution_count": 12, "id": "749228c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["['[\"Y6333699\"', '\"11023537\"]']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"New GT ID\"] == 49][\"displayID\"].iloc[0]"]}, {"cell_type": "code", "execution_count": 13, "id": "29d54742f0a82011", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:10.855768Z", "start_time": "2025-07-21T04:51:10.849729Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(178, 16)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email source</th>\n", "      <th>Email ID from source</th>\n", "      <th>Testing Case</th>\n", "      <th>Testing category</th>\n", "      <th>Cases covered</th>\n", "      <th>Simplified cases covered</th>\n", "      <th>Status</th>\n", "      <th>Full email</th>\n", "      <th>Email to use</th>\n", "      <th>Email subject</th>\n", "      <th>TImeStamp</th>\n", "      <th>To_email</th>\n", "      <th>From_email</th>\n", "      <th>displayID</th>\n", "      <th>Client note to consider</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Original 100</td>\n", "      <td>5.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-15 17:51:06.753</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>[11111181]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Original 100</td>\n", "      <td>7.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>[70126790]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Original 100</td>\n", "      <td>7.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>[70126790]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Original 100</td>\n", "      <td>8.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT</td>\n", "      <td>AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 11:43:09.923</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>[18143909]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>Original 100</td>\n", "      <td>9.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 17:47:02.330</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>[Y6463960]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   New GT ID  Email source  Email ID from source         Testing Case  \\\n", "0          1  Original 100                   5.0  Standard Processing   \n", "1          2  Original 100                   7.0  Standard Processing   \n", "2          3  Original 100                   7.0  Standard Processing   \n", "3          4  Original 100                   8.0  Standard Processing   \n", "4          6  Original 100                   9.0  Standard Processing   \n", "\n", "   Testing category                   Cases covered  \\\n", "0  Standard Payroll           REGULAR_HOURS, AMOUNT   \n", "1  Standard Payroll                 HOURS_WITH_RATE   \n", "2  Standard Payroll                 HOURS_WITH_RATE   \n", "3  Standard Payroll                          AMOUNT   \n", "4  Standard Payroll  STANDARD_SALARY, REGULAR_HOURS   \n", "\n", "         Simplified cases covered                       Status  \\\n", "0           REGULAR_HOURS, AMOUNT  Confirmed test case with GT   \n", "1                 HOURS_WITH_RATE  Confirmed test case with GT   \n", "2                 HOURS_WITH_RATE  Confirmed test case with GT   \n", "3                          AMOUNT  Confirmed test case with GT   \n", "4  STANDARD_SALARY, REGULAR_HOURS  Confirmed test case with GT   \n", "\n", "                                          Full email  \\\n", "0  Good morning:\\n\\nPlease make and process all p...   \n", "1  Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "2  Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "3  Please pay clayton <PERSON> 1000 check date 4/2...   \n", "4  Hello,\\n\\nPlease see payroll below from last w...   \n", "\n", "                                        Email to use Email subject  \\\n", "0  Good morning:\\n\\nPlease make and process all p...           NaN   \n", "1  Thank you payroll w/e 4/15, checks dated 4/18\\...           NaN   \n", "2  Thank you payroll w/e 4/15, checks dated 4/18\\...           NaN   \n", "3  Please pay clayton <PERSON> 1000 check date 4/2...           NaN   \n", "4  Hello,\\n\\nPlease see payroll below from last w...           NaN   \n", "\n", "                 TImeStamp                To_email  \\\n", "0  2025-04-15 17:51:06.753   <EMAIL>   \n", "1  2025-04-16 14:14:34.363     <EMAIL>   \n", "2  2025-04-16 14:14:34.363     <EMAIL>   \n", "3  2025-04-21 11:43:09.923  <EMAIL>   \n", "4  2025-04-21 17:47:02.330    <EMAIL>   \n", "\n", "                     From_email   displayID Client note to consider  \n", "0  <EMAIL>  [11111181]                     NaN  \n", "1         <EMAIL>  [70126790]                     NaN  \n", "2         <EMAIL>  [70126790]                     NaN  \n", "3        <EMAIL>  [18143909]                     NaN  \n", "4            <EMAIL>  [Y6463960]                     NaN  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["print(df.shape)\n", "df.head()"]}, {"cell_type": "markdown", "id": "22a21451", "metadata": {"vscode": {"languageId": "bat"}}, "source": ["#### Process email"]}, {"cell_type": "code", "execution_count": 14, "id": "536ac667503cc964", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:11.363625Z", "start_time": "2025-07-21T04:51:11.349498Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email source</th>\n", "      <th>Email ID from source</th>\n", "      <th>Testing Case</th>\n", "      <th>Testing category</th>\n", "      <th>Cases covered</th>\n", "      <th>Simplified cases covered</th>\n", "      <th>Status</th>\n", "      <th>Full email</th>\n", "      <th>Email to use</th>\n", "      <th>Email subject</th>\n", "      <th>TImeStamp</th>\n", "      <th>To_email</th>\n", "      <th>From_email</th>\n", "      <th>displayID</th>\n", "      <th>Client note to consider</th>\n", "      <th>plain_text_email</th>\n", "      <th>TimeStamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Original 100</td>\n", "      <td>5.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-15 17:51:06.753</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>['11111181']</td>\n", "      <td>NaN</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>2025-04-15 17:51:06.753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Original 100</td>\n", "      <td>7.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>['70126790']</td>\n", "      <td>NaN</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Original 100</td>\n", "      <td>7.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>['70126790']</td>\n", "      <td>NaN</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Original 100</td>\n", "      <td>8.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT</td>\n", "      <td>AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 11:43:09.923</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>['18143909']</td>\n", "      <td>NaN</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>2025-04-21 11:43:09.923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>Original 100</td>\n", "      <td>9.0</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 17:47:02.330</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>['Y6463960']</td>\n", "      <td>NaN</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>2025-04-21 17:47:02.330</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   New GT ID  Email source  Email ID from source         Testing Case  \\\n", "0          1  Original 100                   5.0  Standard Processing   \n", "1          2  Original 100                   7.0  Standard Processing   \n", "2          3  Original 100                   7.0  Standard Processing   \n", "3          4  Original 100                   8.0  Standard Processing   \n", "4          6  Original 100                   9.0  Standard Processing   \n", "\n", "   Testing category                   Cases covered  \\\n", "0  Standard Payroll           REGULAR_HOURS, AMOUNT   \n", "1  Standard Payroll                 HOURS_WITH_RATE   \n", "2  Standard Payroll                 HOURS_WITH_RATE   \n", "3  Standard Payroll                          AMOUNT   \n", "4  Standard Payroll  STANDARD_SALARY, REGULAR_HOURS   \n", "\n", "         Simplified cases covered                       Status  \\\n", "0           REGULAR_HOURS, AMOUNT  Confirmed test case with GT   \n", "1                 HOURS_WITH_RATE  Confirmed test case with GT   \n", "2                 HOURS_WITH_RATE  Confirmed test case with GT   \n", "3                          AMOUNT  Confirmed test case with GT   \n", "4  STANDARD_SALARY, REGULAR_HOURS  Confirmed test case with GT   \n", "\n", "                                          Full email  \\\n", "0  Good morning:\\n\\nPlease make and process all p...   \n", "1  Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "2  Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "3  Please pay clayton <PERSON> 1000 check date 4/2...   \n", "4  Hello,\\n\\nPlease see payroll below from last w...   \n", "\n", "                                        Email to use Email subject  \\\n", "0  Good morning:\\n\\nPlease make and process all p...           NaN   \n", "1  Thank you payroll w/e 4/15, checks dated 4/18\\...           NaN   \n", "2  Thank you payroll w/e 4/15, checks dated 4/18\\...           NaN   \n", "3  Please pay clayton <PERSON> 1000 check date 4/2...           NaN   \n", "4  Hello,\\n\\nPlease see payroll below from last w...           NaN   \n", "\n", "                 TImeStamp                To_email  \\\n", "0  2025-04-15 17:51:06.753   <EMAIL>   \n", "1  2025-04-16 14:14:34.363     <EMAIL>   \n", "2  2025-04-16 14:14:34.363     <EMAIL>   \n", "3  2025-04-21 11:43:09.923  <EMAIL>   \n", "4  2025-04-21 17:47:02.330    <EMAIL>   \n", "\n", "                     From_email     displayID Client note to consider  \\\n", "0  <EMAIL>  ['11111181']                     NaN   \n", "1         <EMAIL>  ['70126790']                     NaN   \n", "2         <EMAIL>  ['70126790']                     NaN   \n", "3        <EMAIL>  ['18143909']                     NaN   \n", "4            <EMAIL>  ['Y6463960']                     NaN   \n", "\n", "                                    plain_text_email               TimeStamp  \n", "0  Good morning:\\n\\nPlease make and process all p... 2025-04-15 17:51:06.753  \n", "1  Thank you payroll w/e 4/15, checks dated 4/18\\... 2025-04-16 14:14:34.363  \n", "2  Thank you payroll w/e 4/15, checks dated 4/18\\... 2025-04-16 14:14:34.363  \n", "3  Please pay clay<PERSON> 1000 check date 4/2... 2025-04-21 11:43:09.923  \n", "4  Hello,\\n\\nPlease see payroll below from last w... 2025-04-21 17:47:02.330  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df = process_emails(df)\n", "df.head()"]}, {"cell_type": "markdown", "id": "eda6e73f", "metadata": {}, "source": ["#### Return in json format"]}, {"cell_type": "code", "execution_count": 15, "id": "cef00193ecaf9508", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:53:18.274399Z", "start_time": "2025-07-21T04:53:18.270813Z"}}, "outputs": [], "source": ["write_upstream_test_file(base_path + output_file, df)"]}, {"cell_type": "code", "execution_count": 16, "id": "11eb58e8351369c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C:\\Users\\<USER>\\payroll-email-agent\\app\\payroll_agent\\evaluation\\ground_truth\\250911_Payroll_processing_golden_set.json\n"]}], "source": ["print(base_path + output_file)"]}, {"cell_type": "code", "execution_count": null, "id": "71c37506", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}