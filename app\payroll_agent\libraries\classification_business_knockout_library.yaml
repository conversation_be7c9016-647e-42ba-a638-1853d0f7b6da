knockout_rules:

  - id: PEO
    description: Customer is making a request related to Paychex's Professional Employer Organization (PEO) services
    condition: Customer explicitly states that they organization is a part of a PEO offering or they indicate they are on a PEO-offering by asking for a PEO-specific offerings such as employee onboarding through PEO. There must be an explicit mention of PEO for this condition to met."
    examples:
    - Can the PEO team help with enrolling this new hire in benefits?
    - This employee was hired under our PEO plan - can you confirm they are fully set up?


  - id: UPDATE_EE_DIRECT_DEPOSIT #these will need to be parsed in the attachments as well
    description: Customer is asking to add new direct deposit banking information or change existing direct deposit banking information
    condition: Customer is explictly requesting to add a new direct deposit banking account or modify an existing direct deposit account.
    examples:
    - Attached please find a copy of the direct deposit form for employee, <PERSON>.
    - Here's the direct deposit form I submitted. All payrolls will be direct deposited.
    - Can you add direct deposit for <PERSON>. Here is his banking information.


  - id: UPDATE_EE_SSN #these will need to be parsed in the attachments as well
    description: Request that contains a full nine-digit Social Security number for an employee (not just last four)
    condition: In payroll-entry request, the customer email or attachment shows a contiguous 9-digit SSN pattern (XXX-XX-XXXX or XXXXXXXXX); ignore if only the last 4 digits are present. Request will typically made to add or modify an employee.
    examples:
    - Please add the below employee to the payroll system. Jane Doe, 123 Main St. Anytown, DOB 1/1/2000, SSN ***********


  - id: CREATE_OFF_CYCLE_PAY_PERIOD #Will require further validation in CA
    description: Request to run payroll outside the client's normal schedule, including corrections or edits to an already-submitted run
    condition: Customer uses phrases such as "off-cycle", "special run", "outside of normal schedule", or specifies a pay period that does not match the client's regular cadence. There should be an explicit mention that this is not part of a usual payroll schedule. Adding employees to an already submitted payroll, is not automatically considered an off cycle payroll unless that run already been processed.
    examples:
    - We plan to terminate an employee this week, and will need to issue a paycheck which is outside of our normal schedule. Could you please activate the paycheck for today's date not April 11. Employee is Jane Doe. Termination will occur today.
    - A rehired associates hours did not populate, and she was not paid. I just started an off cycle, can you process it for me?


  - id: START_OR_RESUME_BACK_DATED_PAY_PERIOD #updated to reflect that this should be based on the check date
    description: Process payroll for a pay period with a check date that occurred in the past (not the current or next scheduled period)
    condition: Customer sends a payroll-entry request for a pay period with a check date in the past or includes wording like "back date." Do not trigger soley on work week or pay period ranges (e.g., a payroll processing request sent 4/11 with the line "see hours below for 3/16-3/31" might have a check date after 4/11). 
    examples:
    - "Sent April 15th: \n\n Can we process a payroll for the month of February? They didn't have enough money at that time for payroll."
    - "Sent April 22nd: \n\n System has available pay period 04/07/2025 to 04/18/2025 with check date 04/21/2025. Can you process that the two week period starting April 7."


  - id: ADD_WORKER
    description: Add a newly hired employee so they can be paid
    condition: Customer explicitly requests to "add", "hire", or "set up" a new employee to be added to the company payroll AND for that employee to get paid. Typically they will include full name, address, SSN, etc in the text of the e-mail or in the attachment. Customer's ask to add a payroll for an employee does not automatically mean it's a new employee, and more often means that the customer wants that person to get paid for that pay period.
    examples:
    - Please add 1099 employee Jane Doe a total of 20 hours@ $30 per hour for a total of  $600.00
    - 1 new hire in Las Vegas. John Doe is a Journeyman.


  - id: UPDATE_CLIENT_OR_EE_PAY_COMPONENTS
    description: Change permanent pay settings (base salary, hourly rate, recurring deductions, bonuses) at either the client or employee level
    condition: Customer instructs a permanent change to salary, hourly rate, default deduction, or recurring compensation item. Do not trigger if the request is limited to a one-time adjustment on a specific payroll run (e.g., adding a bonus or correcting hours for a single check).
    examples:
    - Please run payroll for April 15th. Everything is the same, except we will no longer have the Housing Allowance for John Doe. Please remove the allowance going forward.
    - Attached, please find the employee deductions based on our new rates effective on April 1, 2025. These are the monthly employee portion. Please update for payroll. 
    - John Doe has elected to contribute to his 401k. Please run the payroll report to include 401k.
    - Going forward Jane's pay rate is $30/hr


  - id: UPDATE_EE_RATE_OF_PAY
    description: Permanently change an employee's hourly wage, salary amount, pay frequency, or pay type #specify that if it's unclear, should assume it's a permanent change
    condition: Customer specifies a new default pay rate or salary for an existing employee. Simply providing a pay rate or salary to be made does not imply a request for permanent change.
    examples:
    - John Doe - *NEW* monthly salary - $15,000.00.
    - Please set the default rate for Jane Done to $40.
    - Please change rate to $18.00/hr


  - id: ADDING_MULTIPLE_CHECKS_TO_AN_EE
    description: Issue more than one check to the same employee within the same pay period (e.g., separate salary and commission) or across multiple pay periods in the same email
    condition: Customer requests a "second check" or "additional check" for the same employee. It needs to be an explicit ask for two or more checks to be issued for the same employee. Sometimes it will be asked for a person seperately, but it's also possible to ask for multiple checks for all employees or a group or employees.
    examples:
    - Pay period 5/02/2025, check date 5/1. John Doe = rate - $20.00 / 40hrs - Gross $800.00 - Super Job. *2nd check - Gross $800 - vacation pay.
    - Check date Thursday, 4/3/2025. Pay all regular salary for employees. COMMISSION CHECKS - separate from regular payroll checks for John Doe $7,000 ($1,000 federal taxes withheld) and Jane Doe $5,000 ($800 federal taxes withheld). Total gross commissions = $12,000.
    - I was supposed to give John Doe a raise but forgot to. He should get an additional $350 for the 4/11 check date. You can just add this to the next payroll for records.


  - id: UPDATE_CLIENT_LEVEL_TAXES
    description: Customer asks to change tax settings for the employer as a whole
    condition: Customer requests permanently adding, removing, or modifying an employer tax account, tax ID (including insurance ID, e.g., SUI), or filing jurisdictions
    examples:
      - "We just registered in Colorado. Add CO state tax for the company."
      - "Close our New York unemployment account effective 06/30/25."


  - id: UPDATE_EE_LEVEL_TAXES
    description: Customer changes an employee's tax jurisdictions or filing status
    condition: Customer provides or references a W-4 or state/local form, or requests adding, removing, or changing an employee's filing status, allowances, or state/local tax codes
    examples:
      - "Attached is John Doe's new W-4; update his federal filing status."
      - "Add North Carolina state withholding for Jane Smith."
      - "Mark Brown is now exempt from local city tax."


  - id: ONE_OFF_TAX_CHANGES
    description: Customer asks for a tax or deduction override that applies to one paycheck only
    condition: Customer explicitly states the change is one-time with wording such as "only for this check", "one time", "this paycheck only", or "no permanent changes"
    examples:
      - "Withhold an extra $500 federal tax for John on this paycheck only. No permanent changes."
      - "Please remove state tax for Jane this one time."
      - "Do not take 401k from Bob's check today; only for this pay."
      - "Add a one-time deduction of $200 for Alice, this pay run only."


  - id: CANCEL_DELETE_PAY_PERIODS
    description: Cancel, skip or remove a scheduled payroll run
    condition: In payroll-entry e-mail, customer instructs that payroll should not run this week, desire to cancel usual payroll for the week, etc.
    examples:
    - No payroll for Company A this week. 
    - No payroll this period.
    -


  - id: UPDATE_EE_DEMO_ADDRESS_PHONE_EMAIL
    description: Update an employee's personal or demographic details such as address, phone number, or email
    condition: Customer asks for an employee's contact information to be updated in the system
    examples:
    - Please remove the S from the last name of Jane McDonald. This is causing her problems.
    - I need to correct John Doe's email address. It was entered incorrectly - correct email <NAME_EMAIL>
    - Please find John's new address below


  - id: UPDATE_EE_POSITION
    description: Change an employee's job title or role (often linked to job-costing or certified-payroll rules)
    condition: Customer instructs to update the position or title for an employee
    examples:
    - AAA has offered another position to Jane Doe, an employee working in dept 900. She will serve as the new team lead in dept. 200. Her rate in this position will be at $25 an hour. All other information will remain the same as in her original position.


  - id: UPDATE_EE_WITHHOLDINGS #updated this to be non duplicative with the update EE taxes
    description: Customer changes an employee's recurring extra withholding amount
    condition: Customer references federal, state, or local withholdings and states a dollar or percentage amount. 
    examples:
      - "Take out an extra $50 federal tax from Jane each pay."
      - "Increase John's additional state withholding to $75 per check."
      - "Remove the extra 2 % federal withholding for Mike going forward."
      - "Federal withholding $1,753 and DC state withholding $571.50"


  - id: MANUAL_CHECKS #need to make sure that instances where the full payroll is sent (with all the line item entries) is also knocked out
    description: Issue a physical or in-house check for payroll processing 
    condition: Customer explicitly mentions that they will be doing the payroll check(s) "in house" or requests a "manual check" for any employee, some employees or the whole company. This is different from requests to do a one-time or off-cycle payment, or requests to "deliver" the checks. Only trigger high confidence when customer explicitly mentions that Paychex does not need to create actual checks because the customer will do it manually, in house, etc.
    examples: 
    - No DD - process as manual check. Jane Doe $400
    - Please remember company BBB manually prints its checks


  - id: GROSS_TO_NET_CHECKS #Updated the language here; the phrase we had on "does not want paychex to enter it" isn't quite right as often times the client does need the check recorded in the paychex system still, just not released
    description: Customer provides the gross amount and wants the net pay (or cash requirement) calculated. May also ask Paychex to record a manual Gross-to-Net check for reporting.
    condition: Customer provides a gross amount and asks for the net (or cash required) calculation, indicating the check will be handled outside a standard payroll run (hand-written today, manual gross-to-net entry, or stub to be included with the next payroll). Should not be triggered by customer just asking for "cash requirements" or if the net amount is already provided.
    examples:
    - Can you please tell me the net pay for Jane Doe (34H at $23/H). We will hand write the check and the stub can be sent with the 7/30 payroll. 
    - Please calculate the Net pay for our Maria with a gross amount - $2000.00
    - $2000 for John. I will move the money, it just needs to be reflected in payroll. Please calculate the amount.


  - id: NET_TO_GROSS_CHECKS 
    description: Calculate the gross amount required to deliver a specific after-tax net amount
    condition: Customer states "net (take-home) should be $X" or requests a "gross-up" to be entered as pay in order to reach desired net pay after taxes, deductions, etc.
    examples:
    - One voucher for John Doe please- net $ 3000.00
    - Can you process my payroll for the month of June with a check date next week and a net deposit of $8000?


  - id: THIRD_PARTY_SICK_PAY_CHECKS 
    description: Record payments issued through third-party insurers for disability or similar sick-pay scenarios
    condition: Customer is adding payroll but the check will be issued by a third party, usually for sick pay or disability pay 
    examples:
    - We received our 3rd party sick pay statement. Can you process it for us?
    - Here is the second disability report that I have received from Hartford. Please confirm receipt. 


  - id: CHECKS_USING_SYSTEM_TEMPLATES
    description: Issue checks using a predefined system template that applies special settings (e.g., bonus, commission templates)
    condition: Customer instructs to "use bonus template for "X taxability" or specified "commission template"
    examples:
    - Please use No taxes bonus template to run the bonuses
    - Please use Manager Performance monthly bonus to run the bonuses


  - id: CHANGES_TO_PACKAGE_AND_DELIVERY_FOR_PAYROLLS #addresses are often included for a check delivery; would need to check CA if it's different to existing address
    description: Update where or how physical payroll checks/reports are delivered (address, contact, shipping method)
    condition: Customer requests a new delivery address for physical payroll checks. This does NOT apply to requests related to payroll reports, electronic report formats (e.g., PDF, encrypted files), or email delivery.
    examples:
    - Change of delivery address for Company AAA - 123 Main St. Anytown, 01010


  - id: CHANGE_CHECK_TEMPLATES_FOR_NEW_CHECKS
    description: Create or modify a check template to apply consistent payroll settings (e.g., tax adjustments for bonuses)
    condition: Customer requests to "set up new check template", "change default check template", or similar
    examples:
    - Can you create a template just for Bonus that mirrors the Bonus, Commission template but does not block extra federal and state taxes? You can name it Bonus.
    - Can you set up a template for the Manager Performance monthly bonus for company 999999? The template should be set up and tax monthly like the template in the Bi-weekly company payroll 111111?


  - id: CHANGES_UPDATES_TO_LABOR_DIST_JOB_COSTING #Updated this to specifically find instances where an employes hours are specifically being allocated to a job costing code
    description: Customer asks to split an employee's hours across one or more job, project, or department codes, or to change the default code(s) assigned
    condition: Message instructs Paychex to allocate employee time or pay to one or more distinct labor-distribution codes, or to add, change, or remove a job, project, phase, location, or department code for that employee.  Messages that list different pay types (regular vs overtime) without different codes should not be tagged as this scenario.
    examples:
      - John Smith - 8 hrs Job 100, 32 hrs Job 200
      - Dept 101:\n  John Smith 4 hrs\n  Alice Green 6 hrs\nDept 202:\n  John Smith 3 hrs\n  Bob Brown 5 hrs
      - Move Jane Doe from Store 05 to Store 10
      - Please allocate John's hours across both the warehouse and delivery departments
      - Jim - 16 "Supervisor" hours @ $40 per hour


  - id: VENDOR_CHECKS 
    description: Customer is asking for a check to be issued to a vendor through the payroll process/service
    condition: Customer mentions of a check for a vendor that is not an employee. This does not apply to individuals who are third party contractors.
    examples:
      - Vendor check is the same as before ($970.83)
      - Please process check for vendor XXX


  - id: CALL_REQUESTED 
    description: Customer requires a phone call about the payroll request
    condition: Customer explicitly asks for a phone call to discuss this, previous, or a future payroll, and the request is not conditional (i.e., do not trigger on phrases such as "if you have questions"). Customer indicating they are available for a call is not sufficient to meet this condition. Customer must request a call or strongly imply that they should be called.
    examples:
      - Please call me before processing the payroll
