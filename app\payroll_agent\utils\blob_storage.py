import json
from typing import Dict, Any, Optional
import os
import tempfile
from app.cli.logging_utils import setup_logger
import httpx
from app.payroll_agent.config.config import settings

logger = setup_logger('blob_storage')


class BlobStorageClient:
    """
    Azure Blob Storage client with automatic authentication detection
    """
    
    def __init__(self,
                 base_url: Optional[str] = None,
                 container: Optional[str] = None,
                 timeout: Optional[float] = None):
        self.base_url = (
            base_url
            or settings.AZURE_STORAGE_ACCOUNT_URL
            or "https://rgemailpayrollautom97ca.blob.core.windows.net"
        )
        self.container = (
            container
            or settings.AZURE_STORAGE_CONTAINER_NAME
            or "payroll-intake"
        )
        self.timeout = timeout or settings.AZURE_STORAGE_TIMEOUT or 30.0
        self.auth_method = None
        self.blob_service = None
        self.sas_token = None
        
        # Auto-detect available authentication method
        self._detect_auth_method()
    
    def _detect_auth_method(self):
        """Automatically detect which authentication method to use"""
        
        # Check environment mode first
        env_mode = os.getenv("ENV_MODE", "").upper()
        if env_mode == "LOCAL_DEV":
            logger.info("ENV_MODE=LOCAL_DEV - forcing SAS token usage")
            self._setup_sas_token()
            return
        
        # Try Managed Identity (production environments)
        try:
            from azure.identity.aio import ManagedIdentityCredential
            from azure.storage.blob.aio import BlobServiceClient

            # Log SDK versions when available (don't log secrets)
            try:
                import azure.identity as _ai
                ver_id = getattr(_ai, "__version__", "unknown")
            except Exception:
                ver_id = "unknown"

            try:
                import azure.storage.blob as _ab
                ver_blob = getattr(_ab, "__version__", "unknown")
            except Exception:
                ver_blob = "unknown"

            logger.info(
                f"Azure SDK detected: azure.identity={ver_id}, "
                f"azure.storage.blob={ver_blob}"
            )

            # Log presence (but not values) of environment variables that
            # influence ManagedIdentityCredential
            relevant_envs = [
                "AZURE_CLIENT_ID",
                "AZURE_TENANT_ID",
                "AZURE_CLIENT_SECRET",
                "AZURE_USERNAME",
                "AZURE_PASSWORD",
                "AZURE_AUTHORITY_HOST",
                "AZURE_STORAGE_SAS_TOKEN",
                "MSI_ENDPOINT",
                "MSI_SECRET",
                "ENV_MODE",
            ]

            env_presence = {}
            for k in relevant_envs:
                env_presence[k] = os.getenv(k) is not None

            logger.info(f"Relevant env vars present: {env_presence}")

            # Instantiate credential but guard errors with detailed logging
            try:
                credential = ManagedIdentityCredential()
                logger.info("ManagedIdentityCredential instantiated")

                # Create blob client using the credential
                self.blob_service = BlobServiceClient(
                    account_url=self.base_url,
                    credential=credential,
                )
                self.auth_method = "managed_identity"
                logger.info("Auto-detected: Using Azure Managed Identity")
                return
            except Exception as e:
                # Provide full traceback and hints (avoid printing secrets)
                logger.warning(
                    "Managed Identity instantiation failed"
                )
                logger.exception(e)

        except ImportError:
            logger.info("Azure SDK not available, falling back to SAS token")
        except Exception as e:
            # Catch any other unexpected import/lookup issues
            logger.warning(
                "Unexpected error configuring Azure Managed Identity"
            )
            logger.exception(e)
        
        # Fallback to SAS token
        logger.info("Fallback: Using SAS Token")
        self._setup_sas_token()

    def _setup_sas_token(self):
        """Setup SAS token authentication"""
        self.sas_token = os.getenv("AZURE_STORAGE_SAS_TOKEN")
        if self.sas_token:
            self.auth_method = "sas_token"
            if len(self.sas_token) > 20:
                token_preview = (
                    f"{self.sas_token[:10]}...{self.sas_token[-10:]}"
                )
            else:
                token_preview = "short_token"

            logger.info(f"Using SAS Token ({token_preview})")
        else:
            self.auth_method = "none"
            logger.warning("No AZURE_STORAGE_SAS_TOKEN found!")
            logger.warning("Set AZURE_STORAGE_SAS_TOKEN for local development")
    
    async def download_json(
        self,
        ingest_id: str,
        filename: str = "payload.json",
    ) -> Dict[str, Any]:
        """
        Download and parse JSON from blob storage using available auth method
        """
        if self.auth_method == "managed_identity":
            return await self._download_with_managed_identity(
                ingest_id, filename
            )
        elif self.auth_method == "sas_token":
            return await self._download_with_sas_token(ingest_id, filename)
        else:
            raise BlobStorageError(
                "No authentication method available for blob storage"
            )
    
    async def _download_with_managed_identity(
        self,
        ingest_id: str,
        filename: str,
    ) -> Dict[str, Any]:
        """Download using Azure SDK with Managed Identity"""
        logger.info(
            f"Downloading {ingest_id}/{filename} using Managed Identity"
        )
        
        try:
            blob_client = self.blob_service.get_blob_client(
                container=self.container,
                blob=f"{ingest_id}/{filename}"
            )
            
            # Download blob content
            blob_data = await blob_client.download_blob()
            content = await blob_data.readall()
            
            # Parse JSON
            data = json.loads(content.decode('utf-8'))
            logger.info(f"Successfully downloaded: {len(str(data))} chars")
            return data
            
        except Exception as e:
            if "BlobNotFound" in str(e) or "404" in str(e):
                msg = f"Blob not found: {ingest_id}/{filename}"
                raise BlobNotFoundError(msg) from e
            else:
                msg = f"Managed Identity download failed: {e}"
                raise BlobStorageError(msg) from e
    
    async def _make_http_request(self, method: str, url: str) -> Any:
        """Make HTTP request with consistent configuration"""
        import httpx
        
        async with httpx.AsyncClient(
            timeout=self.timeout,
            verify=False,
        ) as client:
            if method.upper() == "GET":
                return await client.get(url)
            elif method.upper() == "HEAD":
                return await client.head(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

    async def _download_with_sas_token(
        self,
        ingest_id: str,
        filename: str,
    ) -> Dict[str, Any]:
        """Download using HTTP requests with SAS token"""
        url = self._build_blob_url(ingest_id, filename)
        logger.info(f"Downloading {ingest_id}/{filename} using SAS token")

        try:
            response = await self._make_http_request("GET", url)
            
            logger.info(f"HTTP Response: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 403:
                logger.error("403 Forbidden - SAS token may lack permissions")
                logger.error(f"Response body: {response.text}")

            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"Successfully downloaded: {len(str(data))} chars")
            return data
            
        except Exception as e:
            self._handle_http_error(e, ingest_id, filename)

    async def check_blob_exists(
        self,
        ingest_id: str,
        filename: str = "payload.json",
    ) -> bool:
        """Check if a blob exists using available auth method"""
        
        if self.auth_method == "managed_identity":
            return await self._check_blob_exists_managed_identity(
                ingest_id, filename
            )
        elif self.auth_method == "sas_token":
            return await self._check_blob_exists_sas_token(ingest_id, filename)
        
        return False

    async def _check_blob_exists_managed_identity(
        self,
        ingest_id: str,
        filename: str,
    ) -> bool:
        """Check blob existence using Managed Identity"""
        try:
            blob_client = self.blob_service.get_blob_client(
                container=self.container,
                blob=f"{ingest_id}/{filename}"
            )
            await blob_client.get_blob_properties()
            return True
        except Exception:
            return False

    async def _check_blob_exists_sas_token(
        self,
        ingest_id: str,
        filename: str,
    ) -> bool:
        """Check blob existence using SAS token"""
        try:
            url = self._build_blob_url(ingest_id, filename)
            response = await self._make_http_request("HEAD", url)
            return response.status_code == 200
        except Exception:
            return False

    def _handle_http_error(
        self,
        error: Exception,
        ingest_id: str,
        filename: str,
    ):
        """Handle HTTP errors consistently"""
        import httpx
        
        if isinstance(error, httpx.HTTPStatusError):
            if error.response.status_code == 403:
                logger.error("SAS Token Details Debug:")
                logger.error(f"- Path: {ingest_id}/{filename}")
                logger.error(f"- Base URL: {self.base_url}")
                msg = (
                    "Forbidden - check SAS token permissions: "
                    f"{ingest_id}/{filename}"
                )
                raise BlobStorageError(msg) from error
            else:
                msg = (
                    "HTTP error "
                    f"{error.response.status_code}: {ingest_id}/{filename}"
                )
                raise BlobStorageError(msg) from error
        else:
            msg = f"Download failed: {ingest_id}/{filename}: {error}"
            raise BlobStorageError(msg) from error
    
    def _build_blob_url(
        self,
        ingest_id: str,
        filename: str = "payload.json",
    ) -> str:
        """Build URL with SAS token"""
        url = f"{self.base_url}/{self.container}/{ingest_id}/{filename}"
        
        if self.sas_token:
            token = self.sas_token.lstrip('?')
            url = f"{url}?{token}"
        
        return url
    
    async def check_blob_exists(self, ingest_id: str, filename: str = "payload.json") -> bool:
        """Check if a blob exists using available auth method"""
        
        if self.auth_method == "managed_identity":
            try:
                blob_client = self.blob_service.get_blob_client(
                    container=self.container,
                    blob=f"{ingest_id}/{filename}"
                )
                await blob_client.get_blob_properties()
                return True
            except Exception:
                return False
                
        elif self.auth_method == "sas_token":
            import httpx
            url = self._build_blob_url(ingest_id, filename)
            try:
                async with httpx.AsyncClient(timeout=self.timeout, verify=False) as client:
                    response = await client.head(url)
                    return response.status_code == 200
            except Exception:
                return False
        
        return False


class BlobStorageError(Exception):
    """Base exception for blob storage operations"""
    pass


class BlobNotFoundError(BlobStorageError):
    """Exception raised when blob is not found"""
    pass

async def download_attachment_from_blob(attachment_path: str) -> Optional[tuple[str, str]]:
    """
    Download attachment from blob storage to a temporary file

    Returns:
        Tuple (local temp path, original blob path) or None if download failed
    """
    try:
        # Use the attachment_path as-is - it's already the full blob path
        blob_path = attachment_path

        logger.info(f"Attempting to download blob: {blob_path}")

        # Download blob content using the exact path
        blob_content = await download_blob_content_direct(blob_path)

        if blob_content is None:
            return None

        # Extract filename for temp file creation
        filename = os.path.basename(attachment_path)
        file_extension = os.path.splitext(filename)[1]
        temp_fd, temp_path = tempfile.mkstemp(suffix=file_extension, prefix="attachment_")

        try:
            # Write blob content to temp file
            with os.fdopen(temp_fd, 'wb') as temp_file:
                temp_file.write(blob_content)

            logger.info(f"Downloaded attachment to: {temp_path} (orig: {blob_path})")
            # return both temp path and original blob path so callers can log original name
            return temp_path, blob_path

        except Exception as e:
            # Clean up on error
            try:
                os.close(temp_fd)
                os.unlink(temp_path)
            except:
                pass
            raise e

    except Exception as e:
        logger.error(f"Failed to download attachment {attachment_path}: {e}")
        return None


async def download_blob_content_direct(blob_path: str) -> Optional[bytes]:
    """Download raw blob content using the direct blob path"""
    try:
        logger.info(f"Using auth method {blob_client.auth_method}")

        if blob_client.auth_method == "managed_identity":
            blob_client_instance = blob_client.blob_service.get_blob_client(
                container=blob_client.container,
                blob=blob_path
            )
            
            blob_data = await blob_client_instance.download_blob()
            content = await blob_data.readall()
            return content
            
        elif blob_client.auth_method == "sas_token":
            
            url = f"{blob_client.base_url}/{blob_client.container}/{blob_path}"
            if blob_client.sas_token:
                token = blob_client.sas_token.lstrip('?')
                url = f"{url}?{token}"
            
            logger.info(f"DEBUG: Trying to download from URL: {url}")
            
            async with httpx.AsyncClient(timeout=blob_client.timeout, verify=False) as client:
                response = await client.get(url)
                logger.info(f"DEBUG: HTTP response status: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"DEBUG: HTTP response body: {response.text[:500]}")
                
                response.raise_for_status()
                return response.content
        
        elif blob_client.auth_method == "none":
            logger.error("No authentication method available for blob storage")
            logger.error("Please set AZURE_STORAGE_SAS_TOKEN or configure Managed Identity")
            raise BlobStorageError("No authentication method configured")
        
        return None
        
    except Exception as e:
        logger.error(f"Failed to download blob content from {blob_path}: {type(e).__name__} - {e}")
        return None



async def download_all_attachments_for_ingest(ingest_id: str) -> list[tuple[str, str]]:
    """
    Download all files from the attachments/ folder for a given ingest ID.
    Returns a list of tuples (local temp file path, original blob path).
    """
    try:
        # List all blobs in the attachments/ folder for this ingest
        prefix = f"{ingest_id}/attachments/"
        logger.info(f"Listing blobs with prefix: {prefix}")

        if blob_client.auth_method == "managed_identity":
            container_client = blob_client.blob_service.get_container_client(blob_client.container)
            blob_list = [b async for b in container_client.list_blobs(name_starts_with=prefix)]
            blob_paths = [b.name for b in blob_list]
        elif blob_client.auth_method == "sas_token":
            import httpx
            # Azure REST API for listing blobs
            url = f"{blob_client.base_url}/{blob_client.container}?restype=container&comp=list&prefix={prefix}"
            if blob_client.sas_token:
                token = blob_client.sas_token.lstrip('?')
                url = f"{url}&{token}"
            async with httpx.AsyncClient(timeout=blob_client.timeout, verify=False) as client:
                response = await client.get(url)
                response.raise_for_status()
                # Parse XML response
                import xml.etree.ElementTree as ET
                root = ET.fromstring(response.text)
                blob_paths = [blob.find("Name").text for blob in root.findall(".//Blob")]
        else:
            logger.error("No authentication method available for blob storage")
            return []

        logger.info(f"Found {len(blob_paths)} attachment(s) in {prefix}")

        # Download each blob
        local_paths = []
        for blob_path in blob_paths:
            result = await download_attachment_from_blob(blob_path)
            if result:
                # result is (temp_path, blob_path)
                local_paths.append(result)
        return local_paths

    except Exception as e:
        logger.error(f"Failed to download all attachments for {ingest_id}: {e}", exc_info=True)
        return []


# Singleton instance
blob_client = BlobStorageClient()
