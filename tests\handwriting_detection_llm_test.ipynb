{"cells": [{"cell_type": "markdown", "id": "2b2616e1", "metadata": {}, "source": ["# Handwriting detection: OCR + LLM-assisted decision\n", "\n", "This notebook implements a practical workflow to detect whether an image contains handwritten text.\n", "Approach:\n", "1. Run an OCR engine that has some handwriting support (EasyOCR) to extract text, bounding boxes, and confidences.\n", "2. Compute simple heuristics on OCR output (average confidence, fraction of short fragments, many single-character boxes, etc.) to decide if handwriting is likely.\n", "3. Optionally, send a structured prompt to an LLM (OpenAI) that ingests the OCR summary and returns a final judgement with explanation.\n", "\n", "Notes: Install dependencies below if you don't have them. The test image in this repo is `tests/download.jpeg`."]}, {"cell_type": "code", "execution_count": 1, "id": "3a4a8844", "metadata": {}, "outputs": [], "source": ["# Optional: install required packages (run once). Uncomment to use in a fresh environment.\n", "# !pip install -q easyocr pillow matplotlib pytesseract openai\n", "# If you prefer Tesseract: ensure tesseract binary is installed on your system."]}, {"cell_type": "code", "execution_count": 4, "id": "a3eed41c", "metadata": {}, "outputs": [], "source": ["# Install correct PDF/image rendering packages (run once in this kernel).\n", "# pymupdf provides `fitz`. If that is unavailable we can fallback to pdf2image (requires poppler).\n", "#!pip install -q pymupdf pdf2image pillow\n"]}, {"cell_type": "code", "execution_count": 6, "id": "33a4efb0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pathlib import Path\n", "from pprint import pprint\n", "from typing import Any, Dict\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import base64\n", "\n", "\n", "IMAGE_PATH = Path('test_files/handwritten2.jpeg')\n", "if not IMAGE_PATH.exists():\n", "    IMAGE_PATH = Path('test_files/handwritten2.jpeg')\n", "\n", "def show_image(path: Path, figsize=(8,8)) -> None:\n", "    img = Image.open(path).convert('RGB')\n", "    plt.figure(figsize=figsize)\n", "    plt.imshow(img)\n", "    plt.axis('off')\n", "    plt.title(path.name)\n", "    plt.show()\n", "\n", "show_image(IMAGE_PATH)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d5695835", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from io import BytesIO\n", "import base64\n", "from PIL import Image\n", "\n", "# Try to import pymupdf (fitz). If unavailable, we'll try pdf2image as a fallback at runtime.\n", "_have_pymupdf = False\n", "try:\n", "    import fitz  # pymupdf\n", "    _have_pymupdf = True\n", "except Exception:\n", "    fitz = None\n", "\n", "def images_from_file(path: Path, max_pages: int = 2, max_size: int = 1600, jpeg_quality: int = 80):\n", "    \"\"\"\n", "    Return a list of data URLs (JPEG base64) for the given file.\n", "    - For images, returns single entry.\n", "    - For PDFs, renders up to max_pages (first pages).\n", "    Falls back to pdf2image if pymupdf is not installed. If neither is available, raises ImportError with instructions.\n", "    \"\"\"\n", "    path = Path(path)\n", "    data_urls = []\n", "\n", "    suffix = path.suffix.lower()\n", "    if suffix == '.pdf':\n", "        if _have_pymupdf and fitz is not None:\n", "            doc = fitz.open(str(path))\n", "            pages = min(len(doc), max_pages)\n", "            for i in range(pages):\n", "                page = doc.load_page(i)\n", "                mat = fitz.Matrix(2, 2)  # scale up for better rendering; adjust if large\n", "                pix = page.get_pixmap(matrix=mat, alpha=False)\n", "                img = Image.frombytes(\"RGB\", [pix.width, pix.height], pix.samples)\n", "                img = _resize_and_encode(img, max_size, jpeg_quality)\n", "                data_urls.append(img)\n", "            doc.close()\n", "        else:\n", "            # Try pdf2image fallback (requires poppler on system)\n", "            try:\n", "                from pdf2image import convert_from_path\n", "            except Exception as e:\n", "                raise ImportError(\"Neither pymupdf (fitz) nor pdf2image are available. \",\n", "                                  \"Install one: `pip install pymupdf` or `pip install pdf2image` and on macOS `brew install poppler`.\") from e\n", "            pages = convert_from_path(str(path), first_page=1, last_page=max_pages)\n", "            for pil_img in pages:\n", "                data_urls.append(_resize_and_encode(pil_img, max_size, jpeg_quality))\n", "    else:\n", "        img = Image.open(path).convert('RGB')\n", "        data_urls.append(_resize_and_encode(img, max_size, jpeg_quality))\n", "\n", "    return data_urls\n", "\n", "def _resize_and_encode(pil_img, max_size, quality):\n", "    # Resize preserving aspect ratio so width/height <= max_size\n", "    w, h = pil_img.size\n", "    if max(w, h) > max_size:\n", "        scale = max_size / float(max(w, h))\n", "        pil_img = pil_img.resize((int(w*scale), int(h*scale)), Image.LANCZOS)\n", "    buf = BytesIO()\n", "    pil_img.save(buf, format='JPEG', quality=quality, optimize=True)\n", "    b64 = base64.b64encode(buf.getvalue()).decode('ascii')\n", "    return f\"data:image/jpeg;base64,{b64}\""]}, {"cell_type": "code", "execution_count": 8, "id": "e8cb1e3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 326 models (showing up to 40): ['gpt-4-0613', 'gpt-4', 'gpt-3.5-turbo', 'gpt-4-0314', 'gpt-audio', 'gpt-5-nano', 'gpt-audio-2025-08-28', 'gpt-realtime', 'gpt-realtime-2025-08-28', 'davinci-002', 'babbage-002', 'gpt-3.5-turbo-instruct', 'gpt-3.5-turbo-instruct-0914', 'dall-e-3', 'dall-e-2', 'gpt-4-1106-preview', 'gpt-3.5-turbo-1106', 'tts-1-hd', 'tts-1-1106', 'tts-1-hd-1106', 'text-embedding-3-small', 'text-embedding-3-large', 'gpt-4-0125-preview', 'gpt-4-turbo-preview', 'gpt-3.5-turbo-0125', 'gpt-4-turbo', 'gpt-4-turbo-2024-04-09', 'gpt-4o', 'gpt-4o-2024-05-13', 'gpt-4o-mini-2024-07-18', 'gpt-4o-mini', 'gpt-4o-2024-08-06', 'chatgpt-4o-latest', 'o1-mini-2024-09-12', 'o1-mini', 'gpt-4o-realtime-preview-2024-10-01', 'gpt-4o-audio-preview-2024-10-01', 'gpt-4o-audio-preview', 'gpt-4o-realtime-preview', 'omni-moderation-latest']\n", "Using hard-coded model: o4-mini\n", "Model raw output (truncated): {\"has_handwriting\": true, \"confidence\": 0.87, \"reason\": \"Table cells contain irregular, non-uniform strokes (e.g. cursive names and notes) distinct from the printed text.\"}\n", "\n", "Parsed judgment:\n", "{\n", "  \"has_handwriting\": true,\n", "  \"confidence\": 0.87,\n", "  \"reason\": \"Table cells contain irregular, non-uniform strokes (e.g. cursive names and notes) distinct from the printed text.\"\n", "}\n", "Wrote judgment to tests/handwriting_detection_result.json\n", "Model raw output (truncated): {\"has_handwriting\": true, \"confidence\": 0.87, \"reason\": \"Table cells contain irregular, non-uniform strokes (e.g. cursive names and notes) distinct from the printed text.\"}\n", "\n", "Parsed judgment:\n", "{\n", "  \"has_handwriting\": true,\n", "  \"confidence\": 0.87,\n", "  \"reason\": \"Table cells contain irregular, non-uniform strokes (e.g. cursive names and notes) distinct from the printed text.\"\n", "}\n", "Wrote judgment to tests/handwriting_detection_result.json\n"]}, {"data": {"text/plain": ["{'has_handwriting': True,\n", " 'confidence': 0.87,\n", " 'reason': 'Table cells contain irregular, non-uniform strokes (e.g. cursive names and notes) distinct from the printed text.'}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# LLM-only multimodal approach with SDK compatibility: support openai>=1.0.0 and older versions\n", "import os\n", "import json\n", "import base64\n", "import re\n", "import traceback\n", "\n", "img_path = IMAGE_PATH\n", "img_b64 = base64.b64encode(img_path.read_bytes()).decode('ascii')\n", "\n", "prompt = (\n", "    \"You will be given an image. Visually inspect the image and determine whether it contains handwritten text.\\n\"\n", "    \"Answer ONLY valid JSON with these keys: {\\\"has_handwriting\\\": boolean, \\\"confidence\\\": number (0-1), \\\"reason\\\": string}.\\n\"\n", "    \"Be concise in the reason and base your judgement only on visual inspection.\\n\"\n", ")\n", "\n", "# Base preferred model names from env (kept for compatibility)\n", "preferred = os.getenv('LLM_MODEL_REASONING') or os.getenv('LLM_MODEL_NON_REASONING') or 'gpt-4.1'\n", "OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')\n", "\n", "# Prepare output container\n", "judgment = None\n", "output_path = Path('tests/handwriting_detection_result.json')\n", "\n", "if not OPENAI_API_KEY:\n", "    print('OPENAI_API_KEY not set in environment; cannot call vision model. Set OPENAI_API_KEY and retry.')\n", "else:\n", "    client = None\n", "    model_ids = []\n", "    # Try new OpenAI client (openai>=1.0.0)\n", "    try:\n", "        from openai import OpenAI\n", "        client = OpenAI(api_key=OPENAI_API_KEY)\n", "        models_resp = client.models.list()\n", "        try:\n", "            model_ids = [m.id for m in models_resp.data]\n", "        except Exception:\n", "            model_ids = [ (m.get('id') if isinstance(m, dict) else getattr(m,'id',None)) for m in (getattr(models_resp,'data',[]) or []) ]\n", "    except Exception as new_exc:\n", "        # Fallback to legacy openai package (pre-1.0)\n", "        try:\n", "            import openai as legacy_openai\n", "            legacy_openai.api_key = OPENAI_API_KEY\n", "            models_resp = legacy_openai.Model.list()\n", "            try:\n", "                model_ids = [m['id'] for m in models_resp['data']]\n", "            except Exception:\n", "                model_ids = [ (m.get('id') if isinstance(m, dict) else getattr(m,'id',None)) for m in (getattr(models_resp,'data',[]) or []) ]\n", "            client = None\n", "        except Exception as legacy_exc:\n", "            print('Failed to list models with both new and legacy openai clients:')\n", "            traceback.print_exc()\n", "            print('new_exc:', new_exc)\n", "            print('legacy_exc:', legacy_exc)\n", "            model_ids = []\n", "\n", "    print(f'Found {len(model_ids)} models (showing up to 40):', model_ids[:40])\n", "\n", "    # Heuristic candidate selection (kept for reference but we'll hard-code the model)\n", "    candidates = []\n", "    pref_vision = f\"{preferred}-vision\"\n", "    candidates.extend([preferred, pref_vision])\n", "    for m in model_ids:\n", "        if not m:\n", "            continue\n", "        low = str(m).lower()\n", "        if 'vision' in low or 'image' in low or 'multimodal' in low:\n", "            candidates.append(m)\n", "        if preferred.lower() in low:\n", "            candidates.append(m)\n", "    # Deduplicate while preserving order\n", "    seen = set()\n", "    candidates = [c for c in candidates if c and not (c in seen or seen.add(c))]\n", "\n", "    # Hard-code the working model per user request\n", "    selected = 'o4-mini'\n", "    if model_ids and selected not in model_ids:\n", "        print(f\"Warning: hard-coded model '{selected}' not present in detected models (may still work if available to your account). Sample detected models: {model_ids[:40]}\")\n", "    else:\n", "        print(f\"Using hard-coded model: {selected}\")\n", "\n", "    try:\n", "        # Build content using supported types for Responses API\n", "        content_items = [\n", "            {\"type\": \"input_text\", \"text\": prompt},\n", "            {\"type\": \"input_image\", \"image_url\": \"data:image/jpeg;base64,\" + img_b64},\n", "        ]\n", "        # Call Responses API using the appropriate client\n", "        if client is not None:\n", "            resp = client.responses.create(\n", "                model=selected,\n", "                input=[{\"role\": \"user\", \"content\": content_items}],\n", "                #temperature=0.0,\n", "            )\n", "        else:\n", "            import openai as legacy_openai\n", "            legacy_openai.api_key = OPENAI_API_KEY\n", "            resp = legacy_openai.responses.create(\n", "                model=selected,\n", "                input=[{\"role\": \"user\", \"content\": content_items}],\n", "                #temperature=0.0,\n", "            )\n", "\n", "        # Extract text: prefer convenience fields then fallback to walking output\n", "        text_out = getattr(resp, 'output_text', None) or (resp.get('output_text') if isinstance(resp, dict) else None) or ''\n", "        if not text_out:\n", "            outputs = getattr(resp, 'output', None) or (resp.get('output') if isinstance(resp, dict) else [])\n", "            parts = []\n", "            if isinstance(outputs, list):\n", "                for item in outputs:\n", "                    if isinstance(item, dict) and 'content' in item:\n", "                        for c in item['content']:\n", "                            if isinstance(c, dict) and 'text' in c:\n", "                                parts.append(c['text'])\n", "                            elif isinstance(c, str):\n", "                                parts.append(c)\n", "                    elif isinstance(item, str):\n", "                        parts.append(item)\n", "            text_out = ''.join(parts)\n", "        if not text_out:\n", "            text_out = str(resp)\n", "\n", "        print('Model raw output (truncated):', text_out[:1000])\n", "\n", "        # Try parse JSON from output\n", "        m = re.search(r\"\\{.*\\}\", text_out, re.S)\n", "        if m:\n", "            try:\n", "                parsed = json.loads(m.group(0))\n", "                # Normalize keys and types\n", "                judgment = {\n", "                    'has_handwriting': bool(parsed.get('has_handwriting')),\n", "                    'confidence': float(parsed.get('confidence') or 0.0),\n", "                    'reason': str(parsed.get('reason') or '')\n", "                }\n", "                print('\\nParsed judgment:')\n", "                print(json.dumps(judgment, indent=2))\n", "\n", "                # Persist to file for downstream tests or CI\n", "                try:\n", "                    output_path.parent.mkdir(parents=True, exist_ok=True)\n", "                    with open(output_path, 'w') as f:\n", "                        json.dump(judgment, f, indent=2)\n", "                    print(f'Wrote judgment to {output_path}')\n", "                except Exception as write_exc:\n", "                    print('Failed to write judgment file:', write_exc)\n", "\n", "            except Exception as e:\n", "                print('JSON parse failed:', e)\n", "                print('Full text output:\\n', text_out)\n", "        else:\n", "            print('No JSON found in model output. Full output:\\n', text_out)\n", "\n", "    except Exception:\n", "        traceback.print_exc()\n", "        print('OpenAI responses call failed; check model access and SDK compatibility.')\n", "\n", "# judgment variable contains the parsed result (or None if parsing failed)\n", "judgment"]}, {"cell_type": "code", "execution_count": 9, "id": "05c8e578", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import os\n", "import json\n", "import traceback\n", "\n", "# Reuse the prompt above\n", "_prompt = (\n", "    \"You will be given an image. Visually inspect the image and determine whether it contains handwritten text.\\n\"\n", "    \"Answer ONLY valid JSON with these keys: {\\\"has_handwriting\\\": boolean, \\\"confidence\\\": number (0-1), \\\"reason\\\": string}.\\n\"\n", "    \"Be concise in the reason and base your judgement only on visual inspection.\\n\"\n", ")\n", "\n", "\n", "def analyze_image_with_llm(image_data_url: str, model: str = 'o4-mini', api_key: str | None = None) -> dict:\n", "    \"\"\"Send a single image (data URL) to the Responses API and return parsed JSON or raw text.\n", "    Returns a dict: { 'parsed': dict|None, 'text': str }\n", "    \"\"\"\n", "    api_key = api_key or os.getenv('OPENAI_API_KEY')\n", "    if not api_key:\n", "        raise RuntimeError('OPENAI_API_KEY not set in environment')\n", "\n", "    try:\n", "        import openai\n", "        openai.api_key = api_key\n", "        content_items = [\n", "            {\"type\": \"input_text\", \"text\": _prompt},\n", "            {\"type\": \"input_image\", \"image_url\": image_data_url},\n", "        ]\n", "        resp = openai.responses.create(\n", "            model=model,\n", "            input=[{\"role\": \"user\", \"content\": content_items}],\n", "        )\n", "\n", "        # Assemble textual output robustly\n", "        text_out = getattr(resp, 'output_text', None) or (resp.get('output_text') if isinstance(resp, dict) else None) or ''\n", "        if not text_out:\n", "            outputs = getattr(resp, 'output', None) or (resp.get('output') if isinstance(resp, dict) else [])\n", "            parts = []\n", "            if isinstance(outputs, list):\n", "                for item in outputs:\n", "                    if isinstance(item, dict) and 'content' in item:\n", "                        for c in item['content']:\n", "                            if isinstance(c, dict) and 'text' in c:\n", "                                parts.append(c['text'])\n", "                            elif isinstance(c, str):\n", "                                parts.append(c)\n", "                    elif isinstance(item, str):\n", "                        parts.append(item)\n", "            text_out = ''.join(parts)\n", "        if not text_out:\n", "            text_out = str(resp)\n", "\n", "        # Try parse JSON object from text\n", "        import re\n", "        m = re.search(r\"\\{.*\\}\", text_out, re.S)\n", "        parsed = None\n", "        if m:\n", "            try:\n", "                parsed = json.loads(m.group(0))\n", "            except Exception:\n", "                parsed = None\n", "        return { 'parsed': parsed, 'text': text_out }\n", "\n", "    except Exception:\n", "        traceback.print_exc()\n", "        return { 'parsed': None, 'text': '' }\n", "\n", "\n", "def batch_analyze_folder(folder_path: str | Path, out_dir: str | Path = 'tests/handwriting_detection_results', model: str = 'o4-mini', max_pages: int = 1):\n", "    \"\"\"Process all files in folder_path. For each file, render pages/images via images_from_file(), call the LLM for each page,\n", "    and write a single JSON file per source with results.\n", "\n", "    Output structure per file:\n", "      {\n", "        \"source\": \"filename.pdf\",\n", "        \"pages\": [ {\"page\": 1, \"parsed\": {...} | null, \"text\": \"...\" }, ... ]\n", "      }\n", "    \"\"\"\n", "    folder = Path(folder_path)\n", "    out_dir = Path(out_dir)\n", "    out_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    files = [p for p in sorted(folder.iterdir()) if p.is_file()]\n", "    results = []\n", "    for f in files:\n", "        try:\n", "            data_urls = images_from_file(f, max_pages=max_pages)\n", "        except Exception as e:\n", "            # If rendering failed, skip and record error\n", "            results.append({ 'source': str(f), 'error': str(e) })\n", "            continue\n", "\n", "        file_result = { 'source': f.name, 'pages': [] }\n", "        for i, img_data in enumerate(data_urls, start=1):\n", "            print(f'Processing {f.name} page {i}...')\n", "            out = analyze_image_with_llm(img_data, model=model)\n", "            file_result['pages'].append({ 'page': i, 'parsed': out.get('parsed'), 'text': out.get('text') })\n", "\n", "        # Persist per-file JSON\n", "        dst = out_dir / (f.stem + '.json')\n", "        with open(dst, 'w') as fh:\n", "            json.dump(file_result, fh, indent=2)\n", "        print(f'Wrote results for {f.name} -> {dst}')\n", "        results.append(file_result)\n", "\n", "    return results\n", "\n", "\n", "# Example usage (uncomment and run):\n", "# res = batch_analyze_folder('/Users/<USER>/Library/CloudStorage/OneDrive-Bain/Projects/paychex/payroll-email-agent/tests/tests/test_files', max_pages=1)\n", "# pprint(res)\n"]}, {"cell_type": "code", "execution_count": 10, "id": "6339f98d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing handwritten1.pdf page 1...\n", "Wrote results for handwritten1.pdf -> tests/handwriting_detection_results/handwritten1.json\n", "Processing handwritten2.jpeg page 1...\n", "Wrote results for handwritten1.pdf -> tests/handwriting_detection_results/handwritten1.json\n", "Processing handwritten2.jpeg page 1...\n", "Wrote results for handwritten2.jpeg -> tests/handwriting_detection_results/handwritten2.json\n", "Processing handwritten3.png page 1...\n", "Wrote results for handwritten2.jpeg -> tests/handwriting_detection_results/handwritten2.json\n", "Processing handwritten3.png page 1...\n", "Wrote results for handwritten3.png -> tests/handwriting_detection_results/handwritten3.json\n", "[{'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.95,\n", "                        'has_handwriting': True,\n", "                        'reason': 'Presence of a cursive signature with '\n", "                                  'irregular, freeform strokes distinct from '\n", "                                  'printed text indicates handwriting.'},\n", "             'text': '{\"has_handwriting\": true, \"confidence\": 0.95, \"reason\": '\n", "                     '\"Presence of a cursive signature with irregular, '\n", "                     'freeform strokes distinct from printed text indicates '\n", "                     'handwriting.\"}'}],\n", "  'source': 'handwritten1.pdf'},\n", " {'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.95,\n", "                        'has_handwriting': True,\n", "                        'reason': 'Several table entries show irregular, '\n", "                                  'cursive strokes and variable line thickness '\n", "                                  'consistent with handwriting.'},\n", "             'text': '{\"has_handwriting\": true, \"confidence\": 0.95, \"reason\": '\n", "                     '\"Several table entries show irregular, cursive strokes '\n", "                     'and variable line thickness consistent with '\n", "                     'handwriting.\"}'}],\n", "  'source': 'handwritten2.jpeg'},\n", " {'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.96,\n", "                        'has_handwriting': <PERSON><PERSON><PERSON>,\n", "                        'reason': 'All text appears in a uniform, typeset font '\n", "                                  'with consistent spacing and no '\n", "                                  'irregularities characteristic of '\n", "                                  'handwriting.'},\n", "             'text': '{\"has_handwriting\": false, \"confidence\": 0.96, \"reason\": '\n", "                     '\"All text appears in a uniform, typeset font with '\n", "                     'consistent spacing and no irregularities characteristic '\n", "                     'of handwriting.\"}'}],\n", "  'source': 'handwritten3.png'}]\n", "Wrote results for handwritten3.png -> tests/handwriting_detection_results/handwritten3.json\n", "[{'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.95,\n", "                        'has_handwriting': True,\n", "                        'reason': 'Presence of a cursive signature with '\n", "                                  'irregular, freeform strokes distinct from '\n", "                                  'printed text indicates handwriting.'},\n", "             'text': '{\"has_handwriting\": true, \"confidence\": 0.95, \"reason\": '\n", "                     '\"Presence of a cursive signature with irregular, '\n", "                     'freeform strokes distinct from printed text indicates '\n", "                     'handwriting.\"}'}],\n", "  'source': 'handwritten1.pdf'},\n", " {'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.95,\n", "                        'has_handwriting': True,\n", "                        'reason': 'Several table entries show irregular, '\n", "                                  'cursive strokes and variable line thickness '\n", "                                  'consistent with handwriting.'},\n", "             'text': '{\"has_handwriting\": true, \"confidence\": 0.95, \"reason\": '\n", "                     '\"Several table entries show irregular, cursive strokes '\n", "                     'and variable line thickness consistent with '\n", "                     'handwriting.\"}'}],\n", "  'source': 'handwritten2.jpeg'},\n", " {'pages': [{'page': 1,\n", "             'parsed': {'confidence': 0.96,\n", "                        'has_handwriting': <PERSON><PERSON><PERSON>,\n", "                        'reason': 'All text appears in a uniform, typeset font '\n", "                                  'with consistent spacing and no '\n", "                                  'irregularities characteristic of '\n", "                                  'handwriting.'},\n", "             'text': '{\"has_handwriting\": false, \"confidence\": 0.96, \"reason\": '\n", "                     '\"All text appears in a uniform, typeset font with '\n", "                     'consistent spacing and no irregularities characteristic '\n", "                     'of handwriting.\"}'}],\n", "  'source': 'handwritten3.png'}]\n"]}], "source": ["res = batch_analyze_folder('/Users/<USER>/Library/CloudStorage/OneDrive-Bain/Projects/paychex/payroll-email-agent/tests/test_files', max_pages=1)\n", "pprint(res)"]}, {"cell_type": "code", "execution_count": null, "id": "59af1f6e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8570335e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "aagplat<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}