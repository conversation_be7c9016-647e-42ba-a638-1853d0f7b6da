#!/usr/bin/env python3
"""
Test script for Paychex Model-as-a-Service (LLM) API

HOW TO RUN:
    python tests/test_llm_api.py

SETUP:
    1. Set environment variables:
       export OPENAI_API_KEY="your_llm_api_key"

    2. OR create/update .env file in project root:
       OPENAI_API_KEY=your_llm_api_key

    3. See .env.example for full configuration options

WHAT IT TESTS:
    - LLM API connectivity and health checks
    - Endpoint discovery for LLM services
    - Different payload formats (OpenAI-style, simple prompt, etc.)
    - Token introspection capabilities
    - Model-as-a-Service API functionality
"""
import requests
import json
import os
from typing import Dict, Any, Optional


def test_llm_api_health() -> Dict[str, Any]:
    """
    Test basic connectivity to the LLM API
    """
    # Get API key from environment variable
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        return {
            "success": False,
            "error": "Missing environment variable: OPENAI_API_KEY"
        }

    base_url = "https://service-internal-n2a.paychex.com"
    endpoint = "/eps/shared"
    url = f"{base_url}{endpoint}"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        print("🚀 Testing LLM API connectivity...")
        print(f"URL: {url}")
        print(f"Headers: {dict(headers)}")
        print("-" * 50)
        
        # Try a simple GET request first
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code in [200, 201]:
            print("✅ SUCCESS! API is accessible")
            try:
                data = response.json()
                print("Response JSON:")
                print(json.dumps(data, indent=2))
            except json.JSONDecodeError:
                print("Response Text:")
                print(response.text)
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
        return {
            "success": response.status_code in [200, 201, 404],  # 404 might be expected for GET
            "status_code": response.status_code,
            "response": response.text
        }
            
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def test_llm_api_completion(prompt: str = "Hello, how are you?") -> Dict[str, Any]:
    """
    Test LLM API with a completion request
    """
    # Get API key from environment variable
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        return {
            "success": False,
            "error": "Missing environment variable: OPENAI_API_KEY"
        }

    base_url = "https://service-internal-n2a.paychex.com"
    endpoint = "/eps/shared"
    url = f"{base_url}{endpoint}"
    
    headers = {
        "Authorization": f"Bearer 80f278cd03d544d586d6a90569c6a408",
        "Content-Type": "application/json"
    }
    
    # Common LLM API payload formats to try
    payloads_to_try = [
        # OpenAI-style format
        {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 100
        },
        # Alternative format
        {
            "prompt": prompt,
            "max_tokens": 100
        },
        # Simple format
        {
            "input": prompt
        }
    ]
    
    for i, payload in enumerate(payloads_to_try, 1):
        try:
            print(f"\n🧪 Testing LLM API completion (format {i})...")
            print(f"URL: {url}")
            print(f"Payload: {json.dumps(payload, indent=2)}")
            print("-" * 50)
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print("-" * 50)
            
            if response.status_code in [200, 201]:
                print("✅ SUCCESS! LLM API responded")
                try:
                    data = response.json()
                    print("Response JSON:")
                    print(json.dumps(data, indent=2))
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "response": data,
                        "payload_format": i
                    }
                except json.JSONDecodeError:
                    print("Response Text:")
                    print(response.text)
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "response": response.text,
                        "payload_format": i
                    }
            else:
                print(f"⚠️  Status code: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ REQUEST ERROR for format {i}: {e}")
            continue
    
    return {
        "success": False,
        "error": "All payload formats failed"
    }


def discover_api_endpoints() -> Dict[str, Any]:
    """
    Try to discover available endpoints
    """
    base_url = "https://service-internal-n2a.paychex.com"
    
    headers = {
        "Authorization": f"Bearer 80f278cd03d544d586d6a90569c6a408",
        "Content-Type": "application/json"
    }
    
    # Common endpoints to try
    endpoints_to_try = [
        "/eps/shared",
        "/eps/shared/health",
        "/eps/shared/models",
        "/eps/shared/completions",
        "/eps/shared/chat/completions",
        "/health",
        "/models",
        "/completions",
        "/chat/completions",
        "/v1/completions",
        "/v1/chat/completions",
        "/api/v1/completions"
    ]
    
    results = {}
    
    for endpoint in endpoints_to_try:
        url = f"{base_url}{endpoint}"
        try:
            print(f"🔍 Trying endpoint: {endpoint}")
            response = requests.get(url, headers=headers, timeout=10)
            results[endpoint] = {
                "status_code": response.status_code,
                "accessible": response.status_code in [200, 201, 405],  # 405 = Method Not Allowed (but endpoint exists)
                "response_preview": response.text[:200] if response.text else ""
            }
            if response.status_code in [200, 201]:
                print(f"  ✅ {response.status_code}")
            elif response.status_code == 405:
                print(f"  ⚠️  {response.status_code} (Method Not Allowed - try POST)")
            else:
                print(f"  ❌ {response.status_code}")
        except requests.exceptions.RequestException as e:
            results[endpoint] = {
                "status_code": None,
                "accessible": False,
                "error": str(e)
            }
            print(f"  ❌ Error: {e}")
    
    return results


if __name__ == "__main__":
    print("🔬 Testing Paychex LLM API")
    print("=" * 60)
    
    # Test 1: Basic connectivity
    print("\n1️⃣ Testing basic connectivity...")
    health_result = test_llm_api_health()
    
    # Test 2: Discover endpoints
    print("\n2️⃣ Discovering available endpoints...")
    discovery_result = discover_api_endpoints()
    
    accessible_endpoints = [ep for ep, data in discovery_result.items() if data.get("accessible")]
    if accessible_endpoints:
        print(f"\n✅ Found accessible endpoints: {accessible_endpoints}")
    
    # Test 3: Try LLM completion
    print("\n3️⃣ Testing LLM completion...")
    completion_result = test_llm_api_completion("What is 2+2?")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if health_result["success"]:
        print("✅ Basic connectivity: PASS")
    else:
        print("❌ Basic connectivity: FAIL")
    
    if accessible_endpoints:
        print(f"✅ Accessible endpoints found: {len(accessible_endpoints)}")
    else:
        print("❌ No accessible endpoints found")
    
    if completion_result["success"]:
        print("✅ LLM completion: PASS")
    else:
        print("❌ LLM completion: FAIL")
