from typing import Optional, List, Dict
from datetime import datetime
import uuid
from pydantic import BaseModel, Field

from app.api.models.input import UpstreamModel
from app.payroll_agent.models.classification import (
    IntentTriage, RequestTypeTags, ComplexityTags,
    BusinessKnockouts as BusinessKnockouts,
    TechnicalKnockouts as TechnicalKnockouts
)
from app.payroll_agent.config.config import settings


class InputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    AgentVersion: str = Field(default=settings.AGENT_VERSION, description="Agent version")
    AgentReleaseDate: str = Field(default=settings.AGENT_RELEASE_DATE, description="Agent release date")
    ingestId: Optional[str] = Field(None, description="Ingest ID")
    displayId: str | list[str] = Field(..., description="display id")
    companyID: Optional[str] = Field(None, description="Company ID")
    uid: Optional[str] = Field(None, description="uid")
    id: Optional[str] = Field(None, description="id")
    timestamp: str = Field(..., description="timestamp")
    x_payx_sid: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Paychex session ID for MCP calls")
    CustomerDomain: Optional[str] = Field(None, description="Domain to look up in the database")
    Attachments: Optional[List[Dict]] = None
    EmailContent: str = Field(..., description="The email content containing payroll information")
    SourceAddress: Optional[str] = Field(None, description="Email sender address")
    DestinationAddress: Optional[List[str]] = Field(None,description="Email destination address")
    Subject: Optional[str] = Field(None, description="Email subject line")
    received_at: str = Field(default_factory=lambda: datetime.utcnow().strftime('%Y-%m-%d %T'), description="Timestamp of when the email was received")


class ClassificationOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    intent_triage_metadata: IntentTriage = Field(None, description="Is intent triage and inital filtering metadata")
    intent_triage_passes_flag: bool = Field(None, description="Is intent triage metadata")
    intent_triage_passes_fail_reasons: Optional[list[str]] = Field(None, description="Optional: log which ones failed")
    intent_triage_content_policy_violation: bool = Field(False, description="True if content policy violation detected, else False")
    intent_triage_content_filter_results: Optional[list[str]] = Field(None, description="Content filter failed category results from OpenAI if present, else None")
    business_knockout_metadata: BusinessKnockouts = Field(None, description="Business knockout metadata")
    business_knockout_passes_flag: bool = Field(None, description="Business knockout pass/fail")
    business_knockout_passes_fail_reasons: Optional[list[str]] = Field(None, description="Business knockout fail reasons")
    technical_knockout_metadata: TechnicalKnockouts = Field(None, description="Technical knockout metadata")
    technical_knockout_passes_flag: bool = Field(None, description="Technical knockout pass/fail")
    technical_knockout_passes_fail_reasons: Optional[list[str]] = Field(None, description="Technical knockout fail reasons")
    branches_router: bool = Field(False, exclude=True)
    request_type_metadata: Optional[RequestTypeTags] = Field(None, description="Structured metadata describing how the request is formatted and structured.")
    complexity_metadata: Optional[ComplexityTags] = Field(None, description="Final complexity classification based on request type metadata (easy, medium, hard).")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(True, description="Whether the payroll app should continue to the next sub graph")
    run_time: Optional[dict] = Field(None, description="Time taken to run the classification graph")
    llm_usage: Optional[dict] = Field(default_factory=dict, description="LLM usage info: model, input tokens, output tokens")
