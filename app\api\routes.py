import uuid
import asyncio

from datetime import datetime
from fastapi import APIRouter, HTTPException, Request, Query
from fastapi.responses import JSONResponse
from concurrent.futures import ThreadPoolExecutor
from app.orchestrator import orchestrator
from app.api.models.input import UpstreamModel
from app.api.models.output import EmailResponse, OrchestrationResponse
from app.payroll_agent.graph.states.classification import InputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.graph_builder import graph
from app.payroll_agent.utils.funcs import format_payroll_state_response, parse_upstream_model
from app.payroll_agent.utils.dashboard import dashboard_integration
from app.cli.logging_utils import setup_logger

logger = setup_logger('api.routes')
router = APIRouter()


@router.post("/process-email", response_model=EmailResponse)
async def process_email(request: UpstreamModel):
    logger.info(f"Received process_email request")

    started_at = datetime.utcnow()

    try:
        # parse the request model for the graph
        logger.debug(f"Request model: {request.model_dump()}")
        input_dict = parse_upstream_model(request)

        input_state = InputState(**input_dict)
        logger.info(f"Generated session ID for API Call tracing: {input_state.x_payx_sid}")

        # Initialize the graph state with Pydantic model
        input_state = PayrollState(input_state=input_state)

        logger.debug(f"Initialized InputState: {input_state!r}")
        logger.info(f"Invoking graph.invoke")
        result_state = await graph.ainvoke(input_state, config={"recursion_limit": 120})
        logger.debug(f"account_lookup.invoke result: {result_state}")
        finished_at = datetime.utcnow()

        try:
            def run_dashboard_logging():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(
                            dashboard_integration.log_transaction(result_state, started_at, finished_at)
                        )
                    finally:
                        loop.close()
                except Exception as e:
                    logger.error(f"Dashboard logging failed: {e}")
                    return False

            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_dashboard_logging)
                dashboard_success = future.result(timeout=10)

            if dashboard_success:
                logger.info("Dashboard logging completed successfully")
            else:
                logger.warning("Dashboard logging failed")

        except Exception as e:
            logger.error(f"Dashboard logging error: {e}")

        plain_dict = format_payroll_state_response(result_state)

        plain_dict["started_at"] = started_at.isoformat()
        plain_dict["finished_at"] = finished_at.isoformat()
        plain_dict["run_time"] = round((finished_at - started_at).total_seconds(), 2)

        response = EmailResponse(response=plain_dict)
        logger.debug(f"process_email completed successfully, returning response: {response}")
        return response

    except Exception as e:
        logger.error(f"Failed to process payroll email, error: {type(e).__name__} - {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "stage": "process_email",
                "message": "Failed to process payroll email"
            }
        )


@router.get("/process-email-orchestrator", response_model=OrchestrationResponse)
async def process_email_orchestrator(
    ingest_id: str = Query(..., description="Ingest ID to process"),
    background: bool = Query(True, description="Return immediate 200 and process in background")
):
    """
    Agentic Engine Orchestrator endpoint

    This endpoint:
    0. Send immediate 200 confirmation (if background=True)
    1. Fetches payload from blob storage using ingest_id
    2. Prepares payload for processing
    3. Triggers remote LangGraph endpoint
    4. Sends results to dashboard
    5. Returns status and metadata

    Parameters:
    - ingest_id: ID to process
    - background: If True, return 200 immediately and process in background

    Error codes:
    - 200: Processing completed successfully OR job queued (background=True)
    - 400: Invalid ingest_id or missing data
    - 404: Ingest_id not found in blob storage
    - 500: Server error during processing
    """
    logger.info(f"Received process_email_orchestrator request for ingest_id: {ingest_id}, background: {background}")

    # BACKGROUND MODE: Return immediate 200 confirmation
    if background:
        logger.info(f"Background mode enabled - returning immediate 200 for ingest_id: {ingest_id}")
        
        # Prepare immediate confirmation response
        confirmation_response = {
            "ingest_id": ingest_id,
            "status": "received_and_queued",
            "status_code": 200,
            "message": "Job received and queued for background processing",
            "started_at": datetime.utcnow().isoformat(),
            "background_processing": True
        }
        
        # Start background orchestration task
        asyncio.create_task(process_orchestration_background(ingest_id))
        
        # Return immediate 200 confirmation
        logger.info(f"Sending immediate 200 confirmation for ingest_id: {ingest_id}")
        return JSONResponse(status_code=200, content=confirmation_response)
    
    # SYNCHRONOUS MODE: Original behavior (wait for completion)
    else:
        logger.info(f"Synchronous mode - waiting for completion for ingest_id: {ingest_id}")
        
        try:
            try:
                response: OrchestrationResponse = await asyncio.wait_for(
                    orchestrator.orchestrate_payroll_processing(ingest_id),
                    timeout=300
                )
            except asyncio.TimeoutError:
                logger.error(f"Orchestration timed out for ingest_id: {ingest_id}")
                raise HTTPException(
                    status_code=504,
                    detail={
                        "error": "Timeout",
                        "stage": "orchestration",
                        "message": f"Payroll processing timed out for ingest_id: {ingest_id}"
                    }
                )

            if response.status_code == 200:
                logger.info(f"Orchestration completed successfully for ingest_id: {ingest_id}")
            else:
                logger.error(f"Orchestration failed for ingest_id: {ingest_id}, status: {response.status}")
                logger.error(f"Error details: {response.error_details}")

            return response

        except Exception as e:
            logger.error(f"Unexpected error in orchestration for ingest_id: {ingest_id}, error: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail={
                    "error": str(e),
                    "stage": "orchestration",
                    "message": f"Failed to orchestrate payroll processing for ingest_id: {ingest_id}"
                }
            )

async def process_orchestration_background(ingest_id: str):
    """Background task that runs the full orchestration after immediate 200 response"""
    try:
        logger.info(f"Starting background orchestration for ingest_id: {ingest_id}")
        
        # Run the full orchestration process with timeout
        try:
            result: OrchestrationResponse = await asyncio.wait_for(
                orchestrator.orchestrate_payroll_processing(ingest_id),
                timeout=300  # 5 minutes timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"Background orchestration timed out for ingest_id: {ingest_id}")
            return
        
        logger.info(f"Background orchestration completed for ingest_id: {ingest_id}")
        logger.info(f"Result status: {result.status_code} - {result.status}")
        logger.info(f"Background job complete for ingest_id: {ingest_id}")
                
    except Exception as e:
        logger.error(f"Background orchestration failed for ingest_id: {ingest_id}: {e}", exc_info=True)
