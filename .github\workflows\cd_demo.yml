name: CD - Payroll Email Agent (demo)

on:
  push:
    branches: [demo]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to demo HEAD)'
        required: false
        default: demo

jobs:
  build-and-deploy:
    permissions:
      id-token: write
      contents: read
    runs-on:
      group: paychex-ai-platform-arc-sandbox
    environment:
      name: dev
      url: ${{ steps.deploy.outputs.container-app-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Deploy to Azure Container App
        id: deploy
        uses: ./.github/actions/deploy-container-app
        with:
          environment: dev
          azure-subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          azure-registry-password: ${{ secrets.AZURE_REGISTRY_PASSWORD }}
          azure-credentials: ${{ secrets.AZURE_CREDENTIALS }}
          git-sha: ${{ github.sha }}
        env:
          # Secrets
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          LANGCHAIN_API_KEY: ${{ secrets.LANGCHAIN_API_KEY }}
          LANGCHAIN_ENDPOINT: ${{ secrets.LANGCHAIN_ENDPOINT }}
          LANGGRAPH_API_URL: ${{ secrets.LANGGRAPH_API_URL }}
          MCP_SERVERS__PAYCHEX__CLIENT_ID: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_ID }}
          MCP_SERVERS__PAYCHEX__CLIENT_SECRET: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_SECRET }}
          # Non-secret vars
          LANGCHAIN_PROJECT: ${{ vars.LANGCHAIN_PROJECT }}
          LANGCHAIN_TRACING_V2: ${{ vars.LANGCHAIN_TRACING_V2 }}
          MCP_SERVERS__PAYCHEX__TRANSPORT: ${{ vars.MCP_SERVERS__PAYCHEX__TRANSPORT }}
          MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL: ${{ vars.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL }}
          MCP_SERVERS__PAYCHEX__URL: ${{ vars.MCP_SERVERS__PAYCHEX__URL }}
          LLM_TYPE: ${{ vars.LLM_TYPE }}
          AZURE_ENDPOINT: ${{ vars.AZURE_ENDPOINT }}
          API_VERSION: ${{ vars.API_VERSION }}
