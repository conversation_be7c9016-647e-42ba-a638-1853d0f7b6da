import json
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
import pytest

from app.payroll_agent.graph.states.classification import InputState
from app.payroll_agent.utils.mcp import MCPService
from app.payroll_agent.graph.states.common import PayrollState

@pytest.mark.asyncio
async def test_mcp_service_adds_session_id():
    """Test that MCPService adds the session ID to tool input"""
    # Create a mock session ID
    test_sid = "test-session-id-456"
    
    # Create mock MCP service
    mcp_service = MCPService()
    
    # Mock get_tools_from_server and the tool
    mock_tool = AsyncMock()
    mock_tool.name = "test_tool"
    mock_tool.arun = AsyncMock(return_value='{"status": "success"}')
    
    mcp_service.get_tools_from_server = AsyncMock(return_value=[mock_tool])
    
    # Call the tool with a session ID
    tool_input = {"param1": "value1"}
    await mcp_service.call_mcp_tool(
        server_name="test_server",
        tool_name="test_tool", 
        tool_input=tool_input,
        session_id=test_sid
    )
    
    # Verify the session ID was added to the tool input
    mock_tool.arun.assert_called_once()
    actual_tool_input = mock_tool.arun.call_args[0][0]
    assert "x_payx_sid" in actual_tool_input
    assert actual_tool_input["x_payx_sid"] == test_sid