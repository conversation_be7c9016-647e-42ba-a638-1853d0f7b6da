version: 1.0
description: Payroll handling rules.
last_updated: 2025-08-20

rateType_rules:
  - rate_type: HOURLY_RATE
    instructions:
      - If no explicit rate is mentioned in the `email_text`, set `rate_overwrite` to false and omit `rate` from the WorkerCommand.
      - If a explicit rate is mentioned in the `email_text` but different to the rate in `context["workers_info"][name]["pay_rate"]["rate"]`, set `rate_overwrite` to true and fill `rate` with the value from the `email_text`.
      - If a explicit rate is mentioned in the `email_text` and equal to the rate in `context["workers_info"][name]["pay_rate"]["rate"]`, set `rate_overwrite` to false and omit `rate` from the WorkerCommand.
  - rate_type: ANNUAL_SALARY
    instruction:
      - Set memo to true if hours are mentioned to highlight these hours are only for information.
      - Set amount to the value from the `contex["workers_info"][name]["pay_standard"]["calculatedPayPeriod"]`
  - rate_type: DAILY_RATE
    instruction:
      - Set memo to true if hours are mentioned to highlight these hours are only for information.
      - Set amount to the value from the `contex["workers_info"][name]["pay_standard"]["calculatedPayPeriod"]`
  - rate_type: PIECEWORK_RATE
    instruction:
      - Set memo to true if hours are mentioned to highlight these hours are only for information.
      - Set amount to the value from the `contex["workers_info"][name]["pay_standard"]["calculatedPayPeriod"]`
  - rate_type: PER_PAY_PERIOD_SALARY
    instruction:
      - Set memo to true if hours are mentioned to highlight these hours are only for information.
      - Set amount to the value from the `contex["workers_info"][name]["pay_standard"]["calculatedPayPeriod"]`

guardrails:
  do_not:
    - Perform any arithmetic operation.
    - Use classificationType or earning_name values that are not present in the provided context options.
    - Output commentary, markdown, or extra keys beyond the schema.
    - Using values from context that are irrelevant. Only use context entries that directly apply to the payroll instruction.
    - Guess "regular salary" amounts — only use context.workers_info[name].pay_standard.calculatedPayPeriod when the email states "regular salary" (or similar).
    - Assume or infer the identity behind “me” or “myself,” unless a name is explicitly stated in the e-mail as a signature or is spelled out as part of the from e-mail text.

common_pitfalls:
  - Over-filling with defaults (e.g., auto-populating fields unnecessarily instead of leaving them blank when appropriate), only use default values from the context when applicable to the WorkerCommand.
  - Appending any text outside the JSON object.
  - Calling paychex_company_checks tool without a clear reference to a previous pay period.
  - Failing to split multiple payment types per person (e.g., "Carlos 30h@$30 and 20h@$15" must yield two entries).
  - Not extracting default amount from context.workers_info[name].pay_standard.calculatedPayPeriod when "regular salary" is mentioned without an amount.
  - Populating rate when amount is provided or when the payment is a fixed salary.
  - Failing to resolve "me"/"myself" to the sender’s name from the signature when available.
  - Using incorrect or partial names when resolving self-references.

payment_guide:
    - classificationType: Regular
      earning_name: Hourly or similar.
      description: Standard hourly pay.
      instructions:
        - Do not populate rate when amount is explicitly provided.
        - If no rate is mentioned, set rate_overwrite to false.
        - If a explicit rate is mentioned in the email, set rate_overwrite to true and return hours and rate.

    - classificationType: Regular
      earning_name: Salary or similar.
      description: Standard regular salary.
      instructions:
        - Do not fill rate.
        - Set amount from context.workers_info[name].pay_standard.calculatedPayPeriod.
        - For salaried workers set memo to true if hours are mentioned to highlight these hours are only for information

    - classificationType: Supplemental
      earning_name: Any Overtime, Vacation, PTO, or similar..
      description: Use for any normal employee overtime, vacation, PTO, or similar.
      instructions:
        - Set rate from the default rate available in context (do not compute multipliers in-model).

    - classificationType: SICK_PAY
      earning_name: Sick or similar.
      description: Use for sickness.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: DEDUCTION
      earning_name: deduction or similar.c
      description: Use for any normal employee deduction.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: TIPS
      earning_name: Tips or similar.
      description: Use for any normal employee tips.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: [MOVING_EXPENSES, REIMBURSEMENT]
      earning_name: Moving Expenses, Reimbursement or similar.
      description: Use for any normal employee moving expenses or reimbursements
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: [EDUCATIONAL_ASSISTANCE, FRINGE_BENEFITS, TRANSPORTATION_BENEFIT]
      earning_name: Any education, fringe benefits, or transportation benefits.
      description: Use for any normal employee education, fringe or transportation benefits.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: [_401K, _403_B_, _408K_SEP, _457_PLAN_CONTRIBUTIONS, _457_PLAN_DISTRIBUTIONS, IRA]
      earning_name: Any 401K, 403B, 408K, 457, IRA, Retirement Plan, or similar.
      description: Use for any normal employee 401K, 403B, 408K, 457, IRA, Retirement Plan, or similar.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: UNION_DUES
      earning_name: Dues, Fees, Fringe or similar.
      description: Use for any normal employee union dues, fees, fringe, or similar.
      instructions:
        - Do not do any special processing for this type of pay.

    - classificationType: [IRC_10, IRC_106, IRC_129_CAFETERIA, IRC_129_FRINGE, IRC_79_GROUP_TERM_LIFE_INSURANCE]
      earning_name: Any IRC, Med, Insurance, or similar.
      description: Use for any normal employee IRC, Med, Insurance, or similar.
      instructions:
        - Do not do any special processing for this type of pay.
