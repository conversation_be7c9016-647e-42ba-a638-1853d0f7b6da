{"dependencies": ["."], "graphs": {"payroll_email_agent": "./app/payroll_agent/graph/graph_builder.py:graph", "account_lookup": "./app/payroll_agent/graph/graph_builder.py:account_lookup", "classification": "./app/payroll_agent/graph/graph_builder.py:classification", "payroll_extraction": "./app/payroll_agent/graph/graph_builder.py:payroll_extraction", "validation": "./app/payroll_agent/graph/graph_builder.py:validation", "execution": "./app/payroll_agent/graph/graph_builder.py:execution", "release": "./app/payroll_agent/graph/graph_builder.py:release"}, "pip_installer": "uv", "python_version": "3.12", "env": ".env"}