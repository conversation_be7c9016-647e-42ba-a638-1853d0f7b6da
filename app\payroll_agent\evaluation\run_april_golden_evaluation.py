import re
import json
import pandas as pd
import requests
import time
from datetime import datetime
from pathlib import Path
from tqdm import tqdm
from copy import copy
from concurrent.futures import ThreadPoolExecutor, as_completed

from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.cell.cell import MergedCell

# Config
HOST = "http://localhost:8000"
API_PREFIX = "/api/v1"
ROUTE = "/process-email"
ENDPOINT_URL = f"{HOST}{API_PREFIX}{ROUTE}"
#APRIL_GOLDEN_TEST_FILE = "250618_SME_emails_100_prepared_valid_case.json"
APRIL_GOLDEN_TEST_FILE = "250618_SME_emails_100_prepared.json"
#APRIL_GOLDEN_TEST_FILE="tmp.json"
# EVALUATION_FILE_NAME = '250630 April Golden test set batch 2 vSME.xlsx'
EVALUATION_FILE_NAME = '250710 April Golden test set 100 v2.xlsx'

# EVAL_READ_PARAMS
EVAL_READ_PARAMS =  dict(
                        sheet_name='Batch 2 test set - employee',
                        skiprows=2
                        )

# 100 emails full info params
FULL_RESULTS_PARAMS = dict(
                        sheet_name='100 Emails - full info',
                        skiprows=1
                        )

EVAL_COLS = [
        'Email content',
        'Email Index No',
        'Client Account Number',
        'Worker Name (PII) ',
        'System Pay Component Name',
        'Calculated Payroll Amount',
        'Salary or Rate Number',
        'Amount',
        'Salary Override Indicator',
        'Hours',
        'Should the email process?',
        'If not, reason why the email should be knocked out',
        'IS_VALID_TEST_CASE', 
        'WAS_AGENT_CORRECT',
        'SUSPECTED_ROOT_CAUSE',
        'COMMENTS',
        ]

FULL_RESULTS_COLS = [
        'Email content',
        'Email Index No',
        'Client Account Number',
        'Worker Name (PII) ',
        'System Pay Component Name',
        'Calculated Payroll Amount',
        'Salary or Rate Number',
        'Amount',
        'Salary Override Indicator',
        'Hours'
        ]

# File paths
base_dir = Path(__file__).parent
APRIL_GOLDEN_INPUT_FILE = base_dir /  "ground_truth" / APRIL_GOLDEN_TEST_FILE
EVALUATION_INPUT_FILE = base_dir / "ground_truth" / EVALUATION_FILE_NAME


timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
OUTPUT_FILE_NAME = f"April_golden_set_evaluation_results_{timestamp}.json"
OUTPUT_FILE = base_dir / "evaluation_results" / OUTPUT_FILE_NAME
OUTPUT_FILE.parent.mkdir(parents=True, exist_ok=True)


# Remove illegal Excel characters
_illegal_chars_re = re.compile(r"[\x00-\x08\x0B\x0C\x0E-\x1F]")

def remove_illegal_chars(val):
    if isinstance(val, str):
        return _illegal_chars_re.sub("", val)
    return val

def load_test_cases(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

def call_api(case, max_retries=3, delay=3):
    last_exception = None
    for attempt in range(max_retries):
        try:
            response = requests.post(ENDPOINT_URL, json=case, timeout=120)
            response.raise_for_status()
            return response.json().get("response", "{}")
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                print(f"Retry attempt: {attempt}")
                time.sleep(delay)
    return {"error": f"API call failed after {max_retries} attempts: {str(last_exception)}"}

def flatten_dict(d, parent_key='', sep='.'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list) and all(isinstance(i, dict) for i in v):
            for i, subdict in enumerate(v):
                items.extend(flatten_dict(subdict, f"{new_key}[{i}]", sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def format_output(excel_path, reason_col, graph_col, summary, termination_graph, termination_reason, sheet_prefix):
    """
    """
    # constants
    table_startrow       = 4                # zero-based → Excel row 5 for DataFrame header
    table_header_row     = table_startrow + 1  # Excel row 5
    section_header_row   = table_header_row - 2  # Excel row 3

    wb = load_workbook(excel_path)
    ws = wb[f"{sheet_prefix}-Summary"]

    # 1) Big “Results” banner in row 1
    last_col = reason_col + termination_reason.shape[1]
    ws.merge_cells(start_row=1, start_column=1,
                   end_row=1, end_column=last_col)
    b = ws.cell(row=1, column=1)
    b.value     = f"{sheet_prefix} - Results"
    b.font      = Font(bold=True, size=14, color="FFFFFF")
    b.fill      = PatternFill("solid", fgColor="4F81BD")
    b.alignment = Alignment(horizontal="center", vertical="center")

    # 2) Section headers in row 3
    sections = [
        ("Successful",        1,                       summary.shape[1]),
        ("Termination graph", graph_col + 1,          termination_graph.shape[1]),
        ("Termination Reason", reason_col + 1,        termination_reason.shape[1]),
    ]
    for title, sc, width in sections:
        ws.merge_cells(start_row=section_header_row,
                       start_column=sc,
                       end_row=section_header_row,
                       end_column=sc + width - 1)
        cell = ws.cell(row=section_header_row, column=sc)
        cell.value     = title
        cell.font      = Font(bold=True, color="FFFFFF")
        cell.fill      = PatternFill("solid", fgColor="808080")
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # 3) Style the pandas header row on row 5
    header_fill = PatternFill("solid", fgColor="D9D9D9")
    header_font = Font(bold=True)
    for df, base_col in [
        (summary, 1),
        (termination_graph, graph_col + 1),
        (termination_reason, reason_col + 1),
    ]:
        for i in range(df.shape[1]):
            c = ws.cell(row=table_header_row, column=base_col + i)
            c.fill      = header_fill
            c.font      = header_font
            c.alignment = Alignment(horizontal="center", vertical="center")

    # 4) Bold + thick bottom border on the “Grand Total” row
    thin  = Side(style="thin")
    thick = Side(style="thick")
    for df, base_col in [
        (summary, 1),
        (termination_graph, graph_col + 1),
        (termination_reason, reason_col + 1),
    ]:
        gt_row = table_header_row + df.shape[0]
        for i in range(df.shape[1]):
            c = ws.cell(row=gt_row, column=base_col + i)
            c.font   = Font(bold=True)
            c.border = Border(top=thin, bottom=thick)

    # 5) Auto‐fit all columns
    for col_cells in ws.columns:
        non_blank = [c for c in col_cells if c.value not in (None, "")]
        if not non_blank:
            continue
        max_len = max(len(str(c.value)) for c in non_blank)
        col_letter = get_column_letter(col_cells[0].column)
        ws.column_dimensions[col_letter].width = max_len + 2

    # walk every cell in the Summary sheet and force horizontal='left'
    for row_cells in ws.iter_rows():
        for cell in row_cells:
            # preserve whatever vertical alignment you already had
            va = cell.alignment.vertical if cell.alignment else 'center'
            cell.alignment = Alignment(horizontal='left', vertical=va)

    wb.save(excel_path)


def evaluation_parser(resp):
    parsed_rows = []
    pay_extraction = resp.get("payroll_processing_output_state", {}).get("extracted_payrolls", [])
    if not pay_extraction:
        row = {
                "EmailContent": resp.get("input_state", {}).get("EmailContent", ""),
                "companyID": resp.get("input_state", {}).get("companyID", ""),
                "Worker Name": '',
                "payAmount" : pd.NA,
                "payHours": pd.NA,
                "payUnits": pd.NA,
                "payRate": pd.NA,
                "payRateId": pd.NA,
                "Workers in email": 0,
                "run_time": resp.get("run_time", pd.NA),

                "successful_flag" : resp.get("successful_flag", False),
                "termination_graph": resp.get("termination_dict", {}).get("termination_graph", ""),
                "termination_reason": resp.get("termination_dict", {}).get("termination_reason", ""),
            }
        parsed_rows.append(row)
    for payroll in pay_extraction:
        cmd = payroll.get("command", {})
        row = {
                "EmailContent": resp.get("input_state", {}).get("EmailContent", ""),
                "companyID": resp.get("input_state", {}).get("companyID", ""),
                "Worker Name": payroll.get("name", ''),
                "payAmount" : cmd.get("payAmount", pd.NA),
                "payHours": cmd.get("payHours", pd.NA),
                "payUnits": cmd.get("payUnits", pd.NA),
                "payRate": cmd.get("payRate", pd.NA),
                "payRateId": cmd.get("payRateId", pd.NA),
                "Workers in email": len(pay_extraction),
                "run_time": resp.get("run_time", pd.NA),

                "successful_flag" : resp.get("successful_flag", False),
                "termination_graph": resp.get("termination_dict", {}).get("termination_graph", ""),
                "termination_reason": resp.get("termination_dict", {}).get("termination_reason", ""),
            }
        parsed_rows.append(row)
    return parsed_rows


def read_evaluation_file():
    print("Reading evaluation file...")
    # clean up the names
    df_true = pd.read_excel(EVALUATION_INPUT_FILE, **EVAL_READ_PARAMS)
    df_true = df_true[EVAL_COLS]
    existing_emails = df_true['Email Index No'].unique() # get unique emails

    # read the full results
    df_full_results = pd.read_excel(EVALUATION_INPUT_FILE, **FULL_RESULTS_PARAMS)[FULL_RESULTS_COLS]
    # filter out existing emails
    df_full_results = df_full_results.loc[~df_full_results['Email Index No'].isin(existing_emails)]
    df_full_results['Should the email process?'] = "Should not process"
    # add the full results to the true values
    df_true = pd.concat([df_true, df_full_results], ignore_index=True, sort=False, axis=0)

    ## Preprocessing
    df_true['Client Account Number'] = df_true['Client Account Number'].astype(str).str.strip()
    df_true['Worker Name (PII) '] = df_true['Worker Name (PII) '].str.strip().fillna('')
    df_true['knock_out'] = df_true['Should the email process?'].map(lambda x: "should not process" in x.lower() if pd.notna(x) else False)

    # change to group by transform
    df_true['Workers in payroll'] = (
        df_true
        .groupby(['Client Account Number', 'Email Index No'])['Email Index No']
        .transform('size')
    )
    df_true['process'] =  ~ df_true['knock_out']
    df_true['knock_out'] = df_true['knock_out'].astype(int)
    df_true['process'] = df_true['process'].astype(int)


    return df_true


def preprocess_agent_results(eval_results):
    # df_eval
    df_eval = pd.DataFrame(eval_results)
    df_eval = df_eval.map(remove_illegal_chars) # clean illegal characters
    df_eval['companyID'] = df_eval['companyID'].astype(str).str.strip()
    df_eval['Worker Name'] = df_eval['Worker Name'].str.strip() # clean up the names
    df_eval['knock_out'] = df_eval['termination_graph'].map(lambda x: "classification_output_state" in x if pd.notna(x) else False)

    # Convert boolean columns to int
    df_eval['knock_out'] = df_eval['knock_out'].astype(int)
    df_eval['successful_flag'] = df_eval['successful_flag'].astype(int)
    df_eval['process'] = df_eval['successful_flag']
    df_eval['payRate'] = df_eval['payRate'].map(lambda x: "TBD" if pd.notna(x) else "rate 1")
    return df_eval


def merge_results(df_true, df_eval):
    ## Copies for column renaming
    df_true_copy = df_true.copy()
    df_eval_copy = df_eval.copy()

    # Rename columns for consistency
    df_true_copy.columns = ['GT_' + col for col in df_true_copy.columns]
    df_eval_copy.columns = ['Agent_' + col for col in df_eval_copy.columns]

    ## Preprocessing for merging
    # 1) split on flag
    df_flag_true  = df_eval_copy[df_eval_copy['Agent_knock_out'] == 1]
    df_flag_false = df_eval_copy[df_eval_copy['Agent_knock_out'] == 0]

    # 2a) for flag==True, merge ONLY on account number
    merged_true = df_true_copy.merge(
        df_flag_true,
        left_on='GT_Client Account Number',
        right_on='Agent_companyID',
        how='left',            # or 'left' if you only want to keep rows from df_flag_true
    )

    index = merged_true['Agent_EmailContent'].isna()

    # 2b) for flag==False, merge on both account and worker-name
    merged_false = df_true_copy.loc[index].merge(
        df_flag_false,
        left_on=['GT_Client Account Number','GT_Worker Name (PII) '],
        right_on=['Agent_companyID','Agent_Worker Name'],
       how='outer',            # same as your original
    )

    # 3) stitch them back together
    merged_df = pd.concat([merged_true.dropna(subset=["Agent_EmailContent"]), merged_false], ignore_index=True)

    merged_df = merged_df.drop_duplicates().reset_index(drop=True)
    return merged_df


def create_flag_columns(merged_df):
     ## Add flag columns for comparison
    merged_df['flag_GT_Agent_Worker found in email'] = (merged_df['GT_Worker Name (PII) '].str.strip() == merged_df['Agent_Worker Name'].str.strip()).astype(int)
    merged_df['flag_GT_Agent_Salary or Rate Number'] = ((merged_df['GT_Salary or Rate Number'].str.strip() == merged_df['Agent_payRate']) |
                                               (merged_df['GT_Salary or Rate Number'].isna())).astype(int)
#    merged_df['flag_GT_Agent_Amount'] = ((merged_df['GT_Amount'] - merged_df['Agent_payAmount']) == 0).astype(int)
    merged_df['flag_GT_Agent_Salary Override Indicator'] = (merged_df['GT_Salary Override Indicator'].str.strip() == \
                                                  merged_df['Agent_payRate'].map(lambda x: "Y" if x=="TBD" else "N" )).astype(int)
    merged_df['flag_GT_Agent_Hours'] = ((merged_df['GT_Hours'] - merged_df['Agent_payHours'] ) == 0).astype(int)
    merged_df['flag_GT_process'] = merged_df['GT_process']
    merged_df['flag_GT_knock_out'] = merged_df['GT_knock_out']
    merged_df['flag_Agent_process'] = merged_df['Agent_process']
    merged_df['flag_Agent_process'] = merged_df.groupby('GT_Email Index No')['flag_Agent_process'].ffill()
    merged_df['flag_Agent_knock_out'] = merged_df['Agent_knock_out']
    merged_df['flag_Agent_knock_out'] = merged_df.groupby('GT_Email Index No')['Agent_knock_out'].ffill()
    merged_df['flag_GT_Agent_both_process'] = (merged_df['GT_process'].astype(bool) & merged_df['Agent_process'].astype(bool)).astype(int)
    merged_df['flag_GT_Agent_both_knock_out'] = (merged_df['GT_knock_out'].astype(bool) & merged_df['Agent_knock_out'].map(lambda x: False if pd.isna(x) else x).astype(bool)).astype(int)
    merged_df['flag_GT_Agent_knock_out_FN'] = ((merged_df['GT_knock_out'] == 1) & (merged_df['flag_Agent_knock_out']== 0)).astype(int)
 #   merged_df['flag_GT_Agent_hours_amount'] = merged_df['flag_GT_Agent_Hours'] | merged_df['flag_GT_Agent_Amount']
    merged_df['flag_GT_Agent_worker_processed_correctly'] = (merged_df['flag_GT_Agent_both_knock_out'] | (merged_df['flag_GT_Agent_Worker found in email'] & merged_df['flag_GT_Agent_Salary or Rate Number'] &
                                                                                                         (merged_df['flag_GT_Agent_knock_out_FN'] == 0))).astype('Int64')

    # 2) Now derive each flag in order, using simple boolean logic
    #    and cast to the nullable Int64 dtype so you get 1/0 (and no errors).
    grp = merged_df.groupby('GT_Email Index No')['flag_GT_Agent_worker_processed_correctly']
    flag_all = grp.transform('all').astype('boolean')
    flag_any = grp.transform('any').astype('boolean')

    # (optional) inspect if flag_any is ever True when flag_all is False:
    # stats = pd.DataFrame({'all': flag_all, 'any': flag_any})
    # print(stats.groupby(level=0).first())

    # 2) build each of the three flags so they can’t overlap
    merged_df['flag_GT_Agent_email_processed_correctly'] =  flag_all.astype('Int64')

    merged_df['flag_GT_Agent_email_partially_processed_correctly'] = (
        (~flag_all & flag_any)      # “some but not all”
    ).astype('Int64')

    merged_df['flag_GT_Agent_email_processed_incorrectly'] = (
        (~flag_all & ~flag_any)     # “none correct”
    ).astype('Int64')

    merged_df['flag_GT_hours'] = merged_df['GT_Hours'].map(lambda x: 1 if pd.notna(x) else 0)
    merged_df['flag_GT_amount'] = merged_df['GT_Amount'].map(lambda x: 1 if pd.notna(x) else 0)


    merged_df['flag_workers in email'] = merged_df['Agent_Workers in email']
    merged_df['flag_workers in payroll'] = merged_df['GT_Workers in payroll']
    merged_df.sort_values(by=['GT_Email Index No', 'GT_Worker Name (PII) '], inplace=True)
    return merged_df


def create_summary_pivot_tables(merged_df):
    # Get unique values for 'GT_Email Index No'
    value = merged_df['GT_Email Index No'].nunique()
    table1 = pd.DataFrame({'                                    ': 'Email Index No',
              'Value': value}, index=[0])

    rows=[
        {
            '                                    ': 'GT_process',
            'Value': merged_df.drop_duplicates(subset='GT_Email Index No')['GT_process'].sum()
        },
        {
            '                                    ': 'GT_knock_out',
            'Value': merged_df.drop_duplicates(subset='GT_Email Index No')['GT_knock_out'].sum()
        },
        {
            '                                    ': 'GT_hours',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No', 'flag_GT_hours'])['flag_GT_hours'].sum()
        },
        {
            '                                    ': 'GT_amount',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No', 'flag_GT_amount'])['flag_GT_amount'].sum()
        },
        {
            '                                    ': 'Agent_process',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No','flag_Agent_process'])['flag_Agent_process'].sum()
        },
        {
            '                                    ': 'Agent_knock_out',
            'Value':  merged_df.drop_duplicates(subset=['GT_Email Index No','flag_Agent_knock_out'])['flag_Agent_knock_out'].sum()
        },
        {
            '                                    ': 'GT_Agent_both_process',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_both_process'])['flag_GT_Agent_both_process'].sum()
        },
        {
            '                                    ': 'GT_Agent_both_knock_out',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_both_knock_out'])['flag_GT_Agent_both_knock_out'].sum()
        },
        {
            '                                    ': 'GT_Agent_knock_out_FN',
            'Value':  merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_knock_out_FN'])['flag_GT_Agent_knock_out_FN'].sum()
        },
        {
            '                                    ': 'GT_Agent_processed_correctly',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_email_processed_correctly'])['flag_GT_Agent_email_processed_correctly'].sum()
        },
        {
            '                                    ': 'GT_Agent_processed_partially_correct',
            'Value': merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_email_partially_processed_correctly'])['flag_GT_Agent_email_partially_processed_correctly'].sum()
        },
        {
            '                                    ': 'GT_Agent_processed_incorrectly',
            'Value':  merged_df.drop_duplicates(subset=['GT_Email Index No','flag_GT_Agent_email_processed_incorrectly'])['flag_GT_Agent_email_processed_incorrectly'].sum()
        }
    ]

    table1 = pd.concat([table1, pd.DataFrame(rows)], ignore_index=True, axis=0)


    # filter out workers extracted that do not have  match
    aux = merged_df.loc[merged_df['GT_Email Index No'].notna()]

    cols = ['flag_GT_process',
                'flag_GT_knock_out',
                'flag_Agent_process',
                'flag_Agent_knock_out',
                'flag_GT_Agent_both_process',
                'flag_GT_Agent_both_knock_out',
                'flag_GT_Agent_knock_out_FN',
                'flag_GT_Agent_email_processed_correctly',
                'flag_GT_Agent_email_partially_processed_correctly',
                'flag_GT_Agent_email_processed_incorrectly',
                'flag_GT_hours',
                'flag_GT_amount',
                ]
    table2 = aux.pivot_table(
        index=['GT_Email Index No'],
        values=cols,
        aggfunc='max',
    )[cols].reset_index()

    aux2 = aux.loc[(aux['GT_knock_out'] == 0) & (aux['GT_System Pay Component Name'] == 'Regular')]
    table3 = aux2.pivot_table(
        index=[
            'GT_Email Index No',
            'GT_Worker Name (PII) ',
            'GT_Salary Override Indicator',
            'GT_Salary or Rate Number'
        ],
        values=[
            'GT_Calculated Payroll Amount',
            'Agent_payHours',
            'GT_Hours',
            'GT_Amount',
            'Agent_payAmount',
            'Agent_payUnits',
            'Agent_payRate',
            'Agent_payRateId',
            #'flag_GT_Agent_hours_amount',
            'flag_GT_Agent_worker_processed_correctly',
            'flag_GT_Agent_email_processed_correctly',
            'flag_GT_Agent_email_partially_processed_correctly',
            'flag_GT_Agent_email_processed_incorrectly',
            'flag_GT_hours',
            'flag_GT_amount'
        ],
        aggfunc={
            'GT_Calculated Payroll Amount': 'sum',
            'Agent_payHours': 'max' ,
            'GT_Hours': 'max',
            'GT_Amount': 'max',
            'Agent_payAmount': 'max',
            'Agent_payUnits': 'max',
            'Agent_payRate': 'max',
            'Agent_payRateId': 'max',
            #'flag_GT_Agent_hours_amount': 'max',
            'flag_GT_Agent_worker_processed_correctly': 'max',
            'flag_GT_Agent_email_processed_correctly': 'max',
            'flag_GT_Agent_email_partially_processed_correctly': 'max',
            'flag_GT_Agent_email_processed_incorrectly': 'max',
            'flag_GT_hours': 'max',
            'flag_GT_amount': 'max'
        }
    ).reset_index()


    return table1, table2, table3


def write_formatted_table(writer, sheet_name, df,
                          title=None,
                          startrow=2, startcol=1,
                          header_bg="D9D9D9"):
    """
    Write `df` to Excel & format it using openpyxl styles.
    Optionally adds `title` one row above the table as a merged cell
    (white bold text on dark blue fill), disables Excel gridlines,
    adds a thin black bottom border only on the last row of the table,
    left-aligns all text, and leaves one blank row after each table.
    Returns the next startrow (after df + 1 blank line).
    """
    # if title is provided, shift the DataFrame down one row
    data_start = startrow + 1 if title else startrow

    # 1) Dump the DataFrame
    df.to_excel(writer,
                sheet_name=sheet_name,
                index=False,
                startrow=data_start,
                startcol=startcol)

    # 2) Grab the openpyxl workbook & worksheet
    wb = writer.book
    ws = wb[sheet_name]

    # 3) Disable Excel gridlines so only our borders show
    ws.sheet_view.showGridLines = False

    # 4) Define styles
    header_fill = PatternFill(fill_type="solid", fgColor=header_bg)
    header_font = Font(bold=True)
    bottom_side = Side(border_style="thin", color="000000")
    bottom_border = Border(bottom=bottom_side)
    align_left = Alignment(horizontal="left", vertical="center")

    n_rows, n_cols = df.shape

    # 5) If title given, merge cells above header and style it
    if title:
        title_row = data_start - 1
        first_col = startcol + 1
        last_col  = startcol + n_cols
        ws.merge_cells(start_row=title_row, start_column=first_col,
                       end_row=title_row,   end_column=last_col)
        tc = ws.cell(row=title_row, column=first_col)
        tc.value = title
        tc.font = Font(bold=True, color="FFFFFF")
        tc.fill = PatternFill(fill_type="solid", fgColor="4F81BD")
        tc.alignment = Alignment(horizontal="left", vertical="center")

    # Calculate header & data row indices
    header_row     = data_start + 1
    first_data_row = data_start + 2
    last_data_row  = header_row + n_rows

    # 6) Format header row: fill, bold font, left alignment (no border)
    for idx, col_name in enumerate(df.columns, start=startcol+1):
        letter = get_column_letter(idx)
        cell = ws[f"{letter}{header_row}"]
        cell.fill      = header_fill
        cell.font      = header_font
        cell.alignment = align_left

        # adjust width only if desired > current
        desired = len(col_name) + 2
        dim     = ws.column_dimensions[letter]
        if (dim.width or 0) < desired:
            dim.width = desired

    # 7) Format data rows: left alignment, bottom border only on last data row
    for row in range(first_data_row, first_data_row + n_rows):
        for col in range(startcol+1, startcol+1 + n_cols):
            cell = ws.cell(row=row, column=col)
            cell.alignment = align_left
            if row == last_data_row:
                cell.border = bottom_border

    # 8) Return next startrow (last row + one blank)
    wb.save(OUTPUT_FILE.with_suffix(".xlsx"))
    return data_start + n_rows + 5


def combine_summary_sheets(excel_path, prefixes, target_name="Summary"):
    """
    Combine multiple per-prefix Summary sheets into one, preserving all formatting,
    merged cells, column widths, and row heights.
    """

    wb = load_workbook(excel_path)
    combined = wb.create_sheet(title=target_name)
    cur_row = 1

    for pref in prefixes:
        src = wb[f"{pref}-Summary"]
        max_row = src.max_row
        max_col = src.max_column
        row_offset = cur_row - 1

        # 1) replicate merged cell ranges with offset
        for merged in list(src.merged_cells.ranges):
            combined.merge_cells(
                start_row=merged.min_row + row_offset,
                start_column=merged.min_col,
                end_row=merged.max_row + row_offset,
                end_column=merged.max_col
            )

        # 2) copy column widths
        for col_letter, dim in src.column_dimensions.items():
            if dim.width:
                combined.column_dimensions[col_letter].width = dim.width

        # 3) copy row heights
        for row_idx, dim in src.row_dimensions.items():
            if dim.height:
                combined.row_dimensions[row_idx + row_offset].height = dim.height

        # 4) copy cell values and styles (skip interior merged cells)
        for r in range(1, max_row + 1):
            for c in range(1, max_col + 1):
                old = src.cell(row=r, column=c)
                # skip merged-cell proxies
                if isinstance(old, MergedCell):
                    continue

                new = combined.cell(row=r + row_offset, column=c)
                new.value = old.value
                if old.has_style:
                    new.font           = copy(old.font)
                    new.fill           = copy(old.fill)
                    new.border         = copy(old.border)
                    new.alignment      = copy(old.alignment)
                    new.number_format  = old.number_format
                    new.protection     = copy(old.protection)

        # leave one blank row before next block
        cur_row += max_row + 1

    # remove old per-prefix summary sheets
    for pref in prefixes:
        del wb[f"{pref}-Summary"]

    wb.save(excel_path)


def style_sheets(excel_path, sheet_prefix):
    """
    Combine multiple per-prefix FullResults sheets into one, preserving formatting
    and adding a `test_set` column. Copies header only once, then data rows.
    Now also auto-sizes header columns to fit their content (skipping column A).
    """
    wb = load_workbook(excel_path)
    sheet_suffixes = ["AgentResults", "GroundTruth"]
    sheets = [f"{sheet_prefix}-{suffix}" for suffix in sheet_suffixes]

    for sheet in sheets:
        fr_ws = wb[sheet]
        # header styles
        fr_header_fill   = PatternFill("solid", fgColor="8ed1ff")
        fr_header_font   = Font(bold=True)
        fr_alignment     = Alignment(horizontal="center", vertical="center")

        # 1) Style every cell in row 1
        for cell in fr_ws[1]:
            cell.fill      = fr_header_fill
            cell.font      = fr_header_font
            cell.alignment = fr_alignment

        # 2) Auto-size columns based on header text, skipping column A
        #    (i.e. skip the first header cell)
        skip_first = 1
        for cell in fr_ws[1][skip_first:]:
            text = str(cell.value) if cell.value is not None else ""
            # +2 for a little padding
            new_width = len(text) + 2
            col_letter = get_column_letter(cell.column)
            fr_ws.column_dimensions[col_letter].width = new_width

    wb.save(excel_path)
    print("Styled sheets with headers and auto-sized header columns (skipping column A).")


def style_sheets_color_headers(excel_path, sheet_prefix):
    """
    Styles sheets <sheet_prefix>-AgentResults and <sheet_prefix>-GroundTruth:
      1) Inserts a new row 1 for “group headers”
      2) Merges over each block of True_/Agent_/flag_ columns,
         writing “Evaluation File” / “Agent Output” / “Exact Match”
      3) Colors group headers & original headers:
         - True_*  → light green
         - Agent_* → light blue
         - flag_*  → light yellow
         - others  → light gray
      4) Auto-sizes columns except those containing “Email content” or “EmailContent”
    """
    wb = load_workbook(excel_path)
    suffixes = ["MergedResults"]
    sheets   = [f"{sheet_prefix}-{suf}" for suf in suffixes]

    # group definitions: prefix → (label, fill)
    groups = {
        "GT_":  ("Evaluation File", PatternFill("solid", fgColor="C6EFCE")),
        "Agent_": ("Agent Output",     PatternFill("solid", fgColor="BDD7EE")),
        "flag_":  ("Exact Match",      PatternFill("solid", fgColor="FFF2CC")),
    }
    default_fill = PatternFill("solid", fgColor="D9D9D9")
    header_font  = Font(bold=True)
    alignment    = Alignment(horizontal="center", vertical="center")

    for name in sheets:
        ws = wb[name]
        # 1) insert blank row at top for group headers
        ws.insert_rows(1)

        # after insertion, original headers are on row 2
        header_row = 2
        cols = list(ws[header_row])

        # 2) find contiguous blocks for each prefix
        col_count = len(cols)
        i = 0
        while i < col_count:
            cell = cols[i]
            val = cell.value or ""
            # see if this cell begins any known group
            for prefix, (label, group_fill) in groups.items():
                if isinstance(val, str) and val.startswith(prefix):
                    # start of a group
                    start_idx = i
                    # extend until prefix no longer matches
                    j = i + 1
                    while j < col_count and isinstance(cols[j].value, str) and cols[j].value.startswith(prefix):
                        j += 1
                    end_idx = j - 1

                    # merge cells in row 1 from start_idx to end_idx
                    start_col = cols[start_idx].column
                    end_col   = cols[end_idx].column
                    ws.merge_cells(
                        start_row=1, start_column=start_col,
                        end_row=1,   end_column=end_col,
                    )
                    top_cell = ws.cell(row=1, column=start_col, value=label)
                    top_cell.fill      = group_fill
                    top_cell.font      = header_font
                    top_cell.alignment = alignment

                    # advance i past this block
                    i = j
                    break
            else:
                # no matching prefix here → leave cell(1, col) blank
                i += 1

        # 3) style the original header row (row 2)
        for cell in ws[header_row]:
            val = cell.value or ""
            # pick fill based on prefix
            if isinstance(val, str):
                if val.startswith("GT_"):
                    cell.fill = groups["GT_"][1]
                elif val.startswith("Agent_"):
                    cell.fill = groups["Agent_"][1]
                elif val.startswith("flag_"):
                    cell.fill = groups["flag_"][1]
                else:
                    cell.fill = default_fill
            else:
                cell.fill = default_fill

            cell.font      = header_font
            cell.alignment = alignment

        # 4) auto-size columns except those with Email content
        for cell in ws[header_row]:
            header = str(cell.value or "")
            if "Email content" in header or "EmailContent" in header:
                continue
            col_letter = get_column_letter(cell.column)
            ws.column_dimensions[col_letter].width = len(header) + 2

    wb.save(excel_path)
    print(f"Styled sheets for prefix '{sheet_prefix}' with grouped & colored headers.")


def flatten_one_level(response_data, expand_cols):
    """
    Flattens a dict one level deeper for keys in expand_cols.
    Returns a flat dict suitable for DataFrame row.
    """
    row = {}
    for k, v in response_data.items():
        if k in expand_cols and isinstance(v, dict):
            for subk, subv in v.items():
                # Stringify dict/list for display
                if isinstance(subv, (dict, list)):
                    row[f"{k}.{subk}"] = json.dumps(subv, indent=2, ensure_ascii=False)
                else:
                    row[f"{k}.{subk}"] = subv
        else:
            row[k] = json.dumps(v, indent=2, ensure_ascii=False) if isinstance(v, (dict, list)) else v
    return row

def run_evaluation(input_file=None, output_file=None, sheet_prefix=None, test_cases=None,  max_workers=10):
    if test_cases is None and input_file is not None:
        test_cases = load_test_cases(input_file)
    results = []

    # Read the evaluation file and extract relevant columns
    df_true = read_evaluation_file()

    print(f"Running {len(test_cases)} test cases against {ENDPOINT_URL}...\n")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_case = {executor.submit(call_api, case): case for case in test_cases}
        for future in tqdm(as_completed(future_to_case), total=len(test_cases), desc="Evaluating"):
            time.sleep(5)
            case = future_to_case[future]
            try:
                result = future.result()
            except Exception as e:
                result = {"error": str(e)}
            results.append({
                "IMSEPCID": case.get("IMSEPCID"),
                "input": case,
                "output": result
            })

    # Flatten output for inspection
    flat_rows = []
    eval_results = []
    for row in results:
        flat = {"companyID": row.get("companyID")}
        output =  row.get("output", {})
        flat.update(flatten_dict(output))
        eval_results += evaluation_parser(output)
        flat_rows.append(flat)

    df_flat = pd.DataFrame(flat_rows)
    df_flat = df_flat.map(remove_illegal_chars) # clean illegal characters

    df_eval = preprocess_agent_results(eval_results)

    # Merge the true values with the evaluation results
    merged_df = merge_results(df_true, df_eval)

    # Create flag columns for comparison
    merged_df = create_flag_columns(merged_df)

    # get the summary tables
    #table1, table2, table3 = create_summary_pivot_tables(merged_df)



    # Create pivot summary for successful_flag + Grand Total
    summary = df_flat.pivot_table(
        index="successful_flag",
        aggfunc="size"
    ).reset_index(name="count")
    total = summary["count"].sum()
    summary = pd.concat([
        summary,
        pd.DataFrame([{"successful_flag": "Grand Total", "count": total}])
    ], ignore_index=True)

    # Termination graph + Grand Total
    termination_graph = df_flat.pivot_table(
        index="termination_dict.termination_graph",
        aggfunc="size"
    ).reset_index(name="count")
    termination_graph.rename(
        columns={"termination_dict.termination_graph": "termination_graph"},
        inplace=True
    )
    tg_total = termination_graph["count"].sum()
    termination_graph = pd.concat([
        termination_graph,
        pd.DataFrame([{"termination_graph": "Grand Total", "count": tg_total}])
    ], ignore_index=True)

    # Termination Reason + Grand Total
    termination_reason = df_flat.pivot_table(
        index=["termination_dict.termination_graph", "termination_dict.termination_reason"],
        aggfunc="size"
    ).reset_index(name="count")
    termination_reason.rename(
        columns={
            "termination_dict.termination_graph": "termination_graph",
            "termination_dict.termination_reason": "termination_reason"
        },
        inplace=True
    )
    tr_total = termination_reason["count"].sum()
    termination_reason = pd.concat([
        termination_reason,
        pd.DataFrame([{"termination_graph": "Grand Total", "termination_reason": "", "count": tr_total}])
    ], ignore_index=True)

    # Write all three tables side-by-side starting at Excel row 5
    excel_path = output_file.with_suffix(".xlsx")
    mode = "w" if not excel_path.exists() else "a"
    sheet_exists = None if mode == "w" else "overlay"
    with pd.ExcelWriter(excel_path, engine="openpyxl", mode=mode, if_sheet_exists=sheet_exists ) as writer:
        table_startrow  = 4  # zero-based → writes header at Excel row 5
        summary.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=0
        )
        graph_col = summary.shape[1] + 2
        termination_graph.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=graph_col,
        )
        reason_col = graph_col + termination_graph.shape[1] + 2
        termination_reason.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=reason_col
        )

        expand_cols = [
            'input_state',
            'classification_output_state',
            'company_worker_lookup_output_state',
            'payroll_processing_output_state',
            'validation_output_state',
            'execution_output_state',
            'release_output_state'
        ]
        one_level_rows = []
        for row in results:
            output = row.get("output", {})
            one_level_rows.append(flatten_one_level(output, expand_cols))
        df_one_level = pd.DataFrame(one_level_rows)

        # also dump full results
        df_flat.to_excel(writer, sheet_name=f"{sheet_prefix}-FullResults", index=False)

        # dump evaluation results
        df_eval.to_excel(writer, sheet_name=f"{sheet_prefix}-AgentResults", index=False)

        # Agent results one level deep
        df_one_level.to_excel(writer, sheet_name=f"{sheet_prefix}-AgentResultsV2", index=False)

        # Write the true values for comparison
        df_true.to_excel(writer, sheet_name=f"{sheet_prefix}-GroundTruth", index=False)

        # Save the merged DataFrame to a new sheet
        merged_df.to_excel(writer, sheet_name=f"{sheet_prefix}-MergedResults", index=False)

        ## Save the summary tables
        #startrow = 2
        #startrow = write_formatted_table(writer, f"{sheet_prefix}-SummaryTables", table1, "Highlevel performance overview", startrow, startcol=1)
        #startrow = write_formatted_table(writer, f"{sheet_prefix}-SummaryTables", table2, "Detailed performance view (Email level)", startrow, startcol=1)
        #write_formatted_table(writer, f"{sheet_prefix}-SummaryTables", table3, "Detailed performance view (Worker in Email level)", startrow, startcol=1)

    # apply the styling + blank rows
    format_output(excel_path, reason_col, graph_col,
                  summary, termination_graph, termination_reason, sheet_prefix)

    print(f"Evaluation completed. Results saved to {output_file} and {excel_path}")
    print(f"Summary saved to {excel_path} under the 'Summary' sheet.")
    print(f"Full results saved to {excel_path} under the 'FullResults' sheet.")
    
if __name__ == "__main__":

    print("Running April Golden Tests...")
    run_evaluation(APRIL_GOLDEN_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Golden_Test")

    excel_path = OUTPUT_FILE.with_suffix(".xlsx")

    # pass additional formatting
    style_sheets(excel_path, sheet_prefix="Golden_Test")
    style_sheets_color_headers(excel_path, sheet_prefix="Golden_Test")

