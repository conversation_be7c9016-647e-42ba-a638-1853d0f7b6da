name: CD - Payroll Email Agent (prod-shadow)

on:
  push:
    branches: [ prod-shadow ]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to main HEAD)'
        required: false
        default: main

jobs:
  build-and-deploy:
    runs-on: [self-hosted, rhel9, er, prod]
    permissions:
      id-token: write
      contents: read
    environment:
      name: prod-shadow
      url: ${{ steps.deploy.outputs.container-app-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Azure login (OIDC)
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Deploy to Azure Container App (prod)
        id: deploy
        uses: ./.github/actions/deploy-container-app-prod-shadow
        with:
          azure-subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          git-sha: ${{ github.sha }}
        env:
          # Secrets
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          LANGCHAIN_API_KEY: ${{ secrets.LANGCHAIN_API_KEY }}
          MCP_SERVERS__PAYCHEX__CLIENT_ID: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_ID }}
          MCP_SERVERS__PAYCHEX__CLIENT_SECRET: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_SECRET }}
          # Non-secret vars
          LANGCHAIN_PROJECT: ${{ vars.LANGCHAIN_PROJECT }}
          LANGCHAIN_TRACING_V2: ${{ vars.LANGCHAIN_TRACING_V2 }}
          MCP_SERVERS__PAYCHEX__TRANSPORT: ${{ vars.MCP_SERVERS__PAYCHEX__TRANSPORT }}
          MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL: ${{ vars.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL }}
          MCP_SERVERS__PAYCHEX__URL: ${{ vars.MCP_SERVERS__PAYCHEX__URL }}
          LANGCHAIN_ENDPOINT: ${{ vars.LANGCHAIN_ENDPOINT }}
          LLM_TYPE: ${{ vars.LLM_TYPE }}
          AZURE_ENDPOINT: ${{ vars.AZURE_ENDPOINT }}
          API_VERSION: ${{ vars.API_VERSION }}
          AZURE_STORAGE_ACCOUNT_URL: ${{ vars.AZURE_STORAGE_ACCOUNT_URL }}
          DASHBOARD_API_URL: ${{ vars.DASHBOARD_API_URL }}
          ENABLE_DASHBOARD_INTEGRATION: ${{ vars.ENABLE_DASHBOARD_INTEGRATION }}
          LANGGRAPH_API_URL: ${{ vars.LANGGRAPH_API_URL }}
          EASY_AUTH_ENABLED: ${{ vars.EASY_AUTH_ENABLED }}
          AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
