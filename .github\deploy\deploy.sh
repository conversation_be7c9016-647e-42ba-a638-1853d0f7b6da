# Initial setup for Azure resources
# Run this script once to create the initial resources for subscription
# Do not run this block per repo, only once
SUB="sub-paychexai-sandbox-002"
RG="rg-emailpayrollautomation-eastus-sb-001"
LOC="eastus"
ACR="conpayrollsb001"
ACA_ENV_AGENT_MCP="cenv-payrollemailagenteastus-sandbox-001"
KV="kv-payrollai-sb-001"
APPCONFIG_NAME="appcfg-payrollai-sb-001"
EVENTHUB_NAMESPACE="evh-payrollai-shared-eastus-sandbox-001"
EH_HUB="applogs-conapp-payrollai-logging"


# network
VNET_NAME="vnet-paychexai-eastus-sandbox-001"
ACA_SUBNET_NAME_AGENT_MCP="snet-payrollai-conapps-sandbox-001"
ACA_SUBNET_PREFIX_AGENT_MCP="************/26"

# Network Security Groups
NSG_CONAPPS="nsg-payrollai-conapps-sandbox"



# Select the subscription
az account set --subscription $SUB

az network vnet subnet create \
  --vnet-name "$VNET_NAME" \
  --resource-group "$RG" \
  --name $ACA_SUBNET_NAME_AGENT_MCP \
  --address-prefixes $ACA_SUBNET_PREFIX_AGENT_MCP \
  --delegations Microsoft.App/environments

# Associate NSGs with subnets
az network vnet subnet update \
    --resource-group "$RG" --vnet-name "$VNET_NAME" \
    --name "$ACA_SUBNET_NAME_AGENT_MCP" --network-security-group "$NSG_CONAPPS"

# Create the ACA Environments
# Get the ACA subnet IDs
#ACA_SUBNET_ID_AGENT_MCP=$(az network vnet subnet show \
#    -g "$RG" -n "$ACA_SUBNET_NAME_AGENT_MCP" \
#    --vnet-name "$VNET_NAME" --query id -o tsv)

# Use the supplied subnet ID
ACA_SUBNET_ID_AGENT_MCP=/subscriptions/3fe57120-1694-4533-ac29-a445c6e071ff/resourceGroups/rg-paychexai-shared-eastus-sandbox-001/providers/Microsoft.Network/virtualNetworks/vnet-paychexai-eastus-sandbox-001/subnets/snet-payrollai-conapps-sandbox-001

# Create the ACA Environments
az containerapp env create \
  --name "$ACA_ENV_AGENT_MCP" \
  --resource-group "$RG" \
  --location "$LOC" \
  --infrastructure-subnet-resource-id "$ACA_SUBNET_ID_AGENT_MCP" \
  --internal-only false


# Create diagnostic settings for ACA Environments
# Get Container App Environment IDs
ACA_ENV_AGENT_ID=$(az containerapp env show -g "$RG" -n "$ACA_ENV_AGENT_MCP" --query id -o tsv)

# Get Event Hub ID
SUB_ID=$(az account show --query id -o tsv)
EH_AUTH_RULE_ID=$(az eventhubs namespace authorization-rule show \
  --resource-group "$RG" \
  --namespace-name "$EVENTHUB_NAMESPACE" \
  --name "RootManageSharedAccessKey" \
  --query id -o tsv)

# Create diagnostic settings
az monitor diagnostic-settings create \
  --name "diag-cenv-agent-mcp-sandbox-001" \
  --resource "$ACA_ENV_AGENT_ID" \
  --event-hub "$EH_HUB" \
  --event-hub-rule "$EH_AUTH_RULE_ID" \
  --logs '[{"category":"ContainerAppConsoleLogs","enabled":true},{"category":"ContainerAppSystemLogs","enabled":true}]'


# Event-Hub (shared) connection string → secret
EVH_CONN=$(az eventhubs namespace authorization-rule keys list \
           -g "$RG" --namespace-name "$EVENTHUB_NAMESPACE" \
           --name "RootManageSharedAccessKey" \
           --query primaryConnectionString -o tsv)
SECRETS="evh-conn=$EVH_CONN"

# Commands specific to Payroll Email Agent
APP_AGENT="conagenticengineeastussb001"

IMAGE_REPO="paychex/payroll-ai-agent"

# Get the ACR access token
ACR_PASSWORD=$(az acr login --name "$ACR" --expose-token --output tsv --query accessToken)

# Run this after the image is built and pushed to ACR
az containerapp create \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --environment "$ACA_ENV_AGENT_MCP" \
  --image "$ACR.azurecr.io/$IMAGE_REPO:dev.latest" \
  --registry-server "$ACR.azurecr.io" \
  --registry-username "$ACR" \
  --registry-password $(az acr credential show --name $ACR --query "passwords[0].value" -o tsv) \
  --ingress external \
  --target-port 8000 \
  --cpu 2 \
  --memory 4Gi \
  --min-replicas 0 \
  --max-replicas 3 \
  --secrets "openai-key=$OPENAI_API_KEY" "evh-conn=$EVH_CONN" \
  --env-vars OPENAI_API_KEY=secretref:openai-key EH_CONN=secretref:evh-conn


az keyvault secret set --vault-name $KV  --name "OPENAI-API-KEY" --value ""
az keyvault secret set --vault-name $KV  --name "LANGCHAIN-API-KEY" --value ""
az keyvault secret set --vault-name $KV  --name "MCP-CLIENT-ID" --value "$(uuidgen)"
az keyvault secret set --vault-name $KV  --name "MCP-CLIENT-SECRET" --value "$(openssl rand -base64 32)"

az appconfig kv set --name $APPCONFIG_NAME --key MCP_SERVERS__PAYCHEX__URL --value "http://conmcpeastussb001/mcp/"
az appconfig kv set --name $APPCONFIG_NAME --key MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL --value "http://conmcpeastussb001/auth/token"
az appconfig kv set --name $APPCONFIG_NAME --key MCP_SERVERS__PAYCHEX__TRANSPORT --value "streamable_http"
az appconfig kv set --name $APPCONFIG_NAME --key LANGCHAIN_TRACING_V2 --value "true"
az appconfig kv set --name $APPCONFIG_NAME --key LANGCHAIN_ENDPOINT --value "https://langchain.paychexai.sandbox.azure.payx"
az appconfig kv set --name $APPCONFIG_NAME --key LANGCHAIN_PROJECT --value "payroll-email-agent"
az appconfig kv set --name $APPCONFIG_NAME --key LLM_TYPE --value "AZURE"
az appconfig kv set --name $APPCONFIG_NAME --key AZURE_ENDPOINT --value "https://service-internal-n2a.paychex.com"
az appconfig kv set --name $APPCONFIG_NAME --key API_VERSION --value "2024-12-01-preview"



MCP_CLIENT_ID=$(az keyvault secret show --vault-name "$KV" --name "MCP-CLIENT-ID" --query "value" -o tsv)
MCP_CLIENT_SECRET=$(az keyvault secret show --vault-name "$KV" --name "MCP-CLIENT-SECRET" --query "value" -o tsv)
OPENAI_API_KEY=$(az keyvault secret show --vault-name "$KV" --name "OPENAI-API-KEY" --query "value" -o tsv)
LANGCHAIN_API_KEY=$(az keyvault secret show --vault-name "$KV" --name "LANGCHAIN-API-KEY" --query "value" -o tsv)

az containerapp secret set \
  -g "$RG" \
  -n "$APP_AGENT" \
  --secrets \
    mcp-client-id="$MCP_CLIENT_ID" \
    mcp-client-secret="$MCP_CLIENT_SECRET" \
    openai-key="$OPENAI_API_KEY" \
    langchain-api-key="$LANGCHAIN_API_KEY"


export MCP_URL=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__URL" --query "value" -o tsv)
export MCP_AUTH_URL=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL" --query "value" -o tsv)
export MCP_TRANSPORT=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__TRANSPORT" --query "value" -o tsv)
export LANGCHAIN_TRACING=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_TRACING_V2" --query "value" -o tsv)
export LANGCHAIN_EP=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_ENDPOINT" --query "value" -o tsv)
export LANGCHAIN_PROJ=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_PROJECT" --query "value" -o tsv)
export LLM_TYPE=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LLM_TYPE" --query "value" -o tsv)
export AZURE_ENDPOINT=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "AZURE_ENDPOINT" --query "value" -o tsv)
export API_VERSION=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "API_VERSION" --query "value" -o tsv)

az containerapp secret set \
  -g "$RG" -n "$APP_AGENT" \
  --secrets \
    mcp-client-id="$MCP_CLIENT_ID" \
    mcp-client-secret="$MCP_CLIENT_SECRET" \
    openai-key="$OPENAI_API_KEY" \
    langchain-api-key="$LANGCHAIN_API_KEY"

az containerapp update \
  -g "$RG" -n "$APP_AGENT" \
  --replace-env-vars \
    OPENAI_API_KEY=secretref:openai-key \
    LANGCHAIN_API_KEY=secretref:langchain-api-key \
    MCP_SERVERS__PAYCHEX__CLIENT_ID=secretref:mcp-client-id \
    MCP_SERVERS__PAYCHEX__CLIENT_SECRET=secretref:mcp-client-secret \
    LANGCHAIN_TRACING_V2="$LANGCHAIN_TRACING" \
    LANGCHAIN_ENDPOINT="$LANGCHAIN_EP" \
    LANGCHAIN_PROJECT="$LANGCHAIN_PROJ" \
    MCP_SERVERS__PAYCHEX__URL="$MCP_URL" \
    MCP_SERVERS__PAYCHEX__TRANSPORT="$MCP_TRANSPORT" \
    MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="$MCP_AUTH_URL" \
    LLM_TYPE="$LLM_TYPE" \
    AZURE_ENDPOINT="$AZURE_ENDPOINT" \
    API_VERSION="$API_VERSION"


az containerapp create \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --environment "$ACA_ENV_AGENT_MCP" \
  --image "$ACR.azurecr.io/$IMAGE_REPO:dev.latest" \
  --registry-server "$ACR.azurecr.io" \
  --registry-username "conpayrollsb001" \
  --registry-password "za/I5S0noGL4d0JMN/9zOvb3HZTpF+KGjw0LLhLa6K+ACRASLJH4" \
  --ingress external \
  --target-port 8000 \
  --cpu 2 \
  --memory 4Gi \
  --min-replicas 0 \
  --max-replicas 3 \
  --secrets \
    mcp-client-id="$MCP_CLIENT_ID" \
    mcp-client-secret="$MCP_CLIENT_SECRET" \
    openai-key="$OPENAI_API_KEY" \
    langchain-api-key="$LANGCHAIN_API_KEY" \
    evh-conn="$EVH_CONN" \
  --env-vars \
    MCP_CLIENT_ID=secretref:mcp-client-id \
    MCP_CLIENT_SECRET=secretref:mcp-client-secret \
    OPENAI_API_KEY=secretref:openai-key \
    LANGCHAIN_API_KEY=secretref:langchain-api-key \
    EH_CONN=secretref:evh-conn \
    MCP_SERVERS__PAYCHEX__URL="$MCP_URL" \
    MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="$MCP_AUTH_URL" \
    MCP_SERVERS__PAYCHEX__TRANSPORT="$MCP_TRANSPORT" \
    LANGCHAIN_TRACING_V2="$LANGCHAIN_TRACING" \
    LANGCHAIN_ENDPOINT="$LANGCHAIN_EP" \
    LANGCHAIN_PROJECT="$LANGCHAIN_PROJ" \
    LLM_TYPE="$LLM_TYPE" \
    AZURE_ENDPOINT="$AZURE_ENDPOINT" \
    API_VERSION="$API_VERSION"
