APP_AUTH_NAME="app-agenticai-payroll-sandbox"
AD_GROUP_NAME="azr_sg_sub_payroll_sandbox_001_bain_contrib"
RG="rg-emailpayrollautomation-eastus-sb-001"

APP_ID=$(az ad app list --display-name "$APP_AUTH_NAME" --query "[0].appId" -o tsv)
GROUP_ID=$(az ad group show --group "$AD_GROUP_NAME" --query "id" -o tsv)

CLIENT_SECRET=$(az ad app credential reset --id $APP_ID --years 2 --query "password" -o tsv)
APP_AGENT="conagenticengineeastussb001"

# Enable Microsoft Authentication on your Container App
az containerapp auth microsoft update \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --client-id "$APP_ID" \
  --client-secret "$CLIENT_SECRET" \
  --tenant-id "$(az account show --query tenantId -o tsv)"

# Configure authentication to redirect to login page
az containerapp auth update \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --unauthenticated-client-action RedirectToLoginPage

# Get app URL
APP_URL=$(az containerapp show \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --query "properties.configuration.ingress.fqdn" -o tsv)

# Update app registration redirect URIs
az ad app update --id "$APP_ID" \
  --web-redirect-uris "https://$APP_URL/docs"

# Set environment variables for group-based access
az containerapp update \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --set-env-vars \
    AUTHORIZED_GROUP_ID="$GROUP_ID" \
    AZURE_CLIENT_ID="$APP_ID"

#  Configure post-login redirect to /docs
az ad app update --id "$APP_ID" \
  --web-redirect-uris "https://$APP_URL/.auth/login/aad/callback" "https://$APP_URL/docs" "https://$APP_URL/"