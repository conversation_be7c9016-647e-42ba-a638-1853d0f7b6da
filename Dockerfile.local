FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# 1) Put the file in place **with a .crt extension**
COPY certs/paychex-root.pem /usr/local/share/ca-certificates/zscaler-root-ca.crt

# Install make and other build tools needed for CI
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && update-ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 2) Tell Python/httpx to trust the *new* bundle
ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

COPY pyproject.toml uv.lock README.md /app/

RUN uv sync \
      --locked \
      --no-dev \
      --no-cache-dir

COPY app/ /app/


ENV PATH="/app/.venv/bin:$PATH" \
    PYTHONUNBUFFERED=1

EXPOSE 8000

ENTRYPOINT ["uvicorn"]
CMD ["app.main:app", "--host=0.0.0.0", "--port", "8000", "--reload", "--timeout-keep-alive", "40", "--workers", "5"]