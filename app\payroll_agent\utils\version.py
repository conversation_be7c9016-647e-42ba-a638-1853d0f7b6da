import yaml
from pathlib import Path
from typing import Dict

def load_version_config() -> Dict[str, str]:
    """Load version information from YAML file"""
    version_file = Path(__file__).parent.parent / "config" / "version.yml"
    
    try:
        with open(version_file, 'r', encoding='utf-8') as f:
            docs = list(yaml.safe_load_all(f))
            if docs:
                return docs[0]
            else:
                return {"version": "Unknown", "release_date": "Unknown"}
    except Exception as e:
        print(f"Warning: Could not load version file: {e}")
        return {"version": "Unknown", "release_date": "Unknown"}