import json
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
from tqdm import tqdm
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import yaml
import asyncio

# Add project root to path
project_root = Path(__file__).resolve().parents[3]
sys.path.append(str(project_root))

from app.payroll_agent.graph.graph_builder import create_graph
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.utils.funcs import parse_upstream_model
from app.api.models.input import UpstreamModel

def make_json_serializable(obj):
    if hasattr(obj, "model_dump"):
        return obj.model_dump()
    elif hasattr(obj, "dict"):
        return obj.dict()
    elif isinstance(obj, list):
        return [make_json_serializable(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    else:
        return obj

async def run_full_graph_on_case(test_case, case_index):
    """Run full graph and return raw result."""
    try:
        upstream_model = UpstreamModel.model_validate(test_case)
        input_dict = parse_upstream_model(upstream_model)
        initial_state = PayrollState(input_state=input_dict)

        full_graph = create_graph()
        result = await full_graph.ainvoke(initial_state, config={"recursion_limit": 120} )

        result_json = make_json_serializable(result)

        return {
            "case_index": case_index,
            "company_id": test_case.get("companyID", ""),
            "success": True,
            "raw_result": result_json
        }
    except Exception as e:
        return {
            "case_index": case_index,
            "company_id": test_case.get("companyID", ""),
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }

def run_cases_parallel(test_cases, max_workers=4):
    results = [None] * len(test_cases)
    success_count = 0
    error_count = 0
    counter_lock = threading.Lock()

    def update_counters(success):
        nonlocal success_count, error_count
        with counter_lock:
            if success:
                success_count += 1
            else:
                error_count += 1
            return success_count, error_count

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_index = {
            executor.submit(run_full_graph_on_case, test_case, i): i
            for i, test_case in enumerate(test_cases)
        }
        with tqdm(total=len(test_cases), desc="Processing cases", unit="case") as pbar:
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                result = future.result()
                results[index] = result
                current_success, current_error = update_counters(result['success'])
                completed = current_success + current_error
                pbar.set_description(f"Case {completed}/{len(test_cases)}: {result['company_id']}")
                pbar.set_postfix({
                    'Success': current_success,
                    'Errors': current_error,
                    'Success Rate': f"{current_success/completed*100:.1f}%" if completed > 0 else "0.0%"
                })
                pbar.update(1)
    return results, success_count, error_count


async def run_cases_sequential(test_cases):
    results = []
    success_count = 0
    error_count = 0
    with tqdm(total=len(test_cases), desc="Processing cases", unit="case") as pbar:
        for i, test_case in enumerate(test_cases):
            company_id = test_case.get('companyID', 'Unknown')
            pbar.set_description(f"Case {i+1}/{len(test_cases)}: {company_id}")
            result = await run_full_graph_on_case(test_case, i)
            results.append(result)
            if result['success']:
                success_count += 1
            else:
                error_count += 1
            pbar.set_postfix({
                'Success': success_count,
                'Errors': error_count,
                'Success Rate': f"{success_count/(i+1)*100:.1f}%"
            })
            pbar.update(1)
    return results, success_count, error_count

async def main():
    test_file = "app/payroll_agent/evaluation/evaluation_cases/mock_ca_case_single.json"
    print(f"Loading test cases from: {test_file}")
    with open(test_file, 'r', encoding='utf-8') as f:
        test_cases = json.load(f)

    PARALLEL_PROCESSING = False  # Start with sequential for async

    print(f"📋 Running full graph on {len(test_cases)} test cases...")
    print(f"🐌 Using sequential processing")

    start_time = time.time()
    results, success_count, error_count = await run_cases_sequential(test_cases)

    total_time = time.time() - start_time
    avg_time_per_case = total_time / len(test_cases)

    print(f"\n⏱️  Processing completed in {total_time:.2f} seconds")
    print(f"⏱️  Average time per case: {avg_time_per_case:.2f} seconds")
    print(f"✅ {success_count}/{len(test_cases)} successful ({success_count/len(test_cases)*100:.1f}%)")

    timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(__file__).parent / "evaluation_results"
    output_dir.mkdir(parents=True, exist_ok=True)
    output_file = output_dir / f"full_graph_evaluation_results_{timestamp}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(make_json_serializable(results), f, indent=2, ensure_ascii=False)
    print(f"💾 Results saved to: {output_file}")

    return results

if __name__ == "__main__":
    asyncio.run(main())