tmp

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
*.log
.coverage
htmlcov/
.pytest_cache/
.ruff_cache/
.langgraph_api

# Data and Pickle files from langgraph
*.pkl
*.pckl
*.pickle
data/
tests/test_files/*.xlsx
tests/test_files/intake_test_payloads/


#Path specific
app/payroll_agent/evaluation/evaluation_results
app/payroll_agent/evaluation/evaluation_batch_results

# experiments folder
experiments/
app/payroll_agent/evaluation/ground_truth/*
app/payroll_agent/evaluation/ground_truth/250710 April Golden test set 100 v2.xlsx
