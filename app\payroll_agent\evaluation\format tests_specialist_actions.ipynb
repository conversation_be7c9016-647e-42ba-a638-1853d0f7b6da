{"cells": [{"cell_type": "code", "execution_count": 16, "id": "8abdea39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting fsspec\n", "  Downloading fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)\n", "Downloading fsspec-2025.5.1-py3-none-any.whl (199 kB)\n", "Installing collected packages: fsspec\n", "Successfully installed fsspec-2025.5.1\n"]}], "source": ["!pip install fsspec"]}, {"cell_type": "code", "execution_count": 17, "id": "84f0ec792a75a827", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "\n", "import email"]}, {"cell_type": "code", "id": "d14965e4d58ee68", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:09.611150Z", "start_time": "2025-07-21T04:51:09.601215Z"}}, "source": ["def html_to_plain_text(raw_email_string):\n", "    try:\n", "        # Step 1: Convert the raw string to bytes\n", "        raw_bytes = raw_email_string.encode('utf-8', errors='ignore')\n", "        # Step 2: Parse email message\n", "        msg = email.message_from_bytes(raw_bytes)\n", "        # Step 3: Walk through parts and extract the plain or HTML content\n", "        plain_parts = []\n", "        html_parts = []\n", "        for part in msg.walk():\n", "            content_type = part.get_content_type()\n", "            content_disposition = str(part.get('Content-Disposition'))\n", "            if 'attachment' in content_disposition:\n", "                continue # skip attachments\n", "            payload = part.get_payload(decode=True)\n", "            charset = part.get_content_charset() or 'utf-8'\n", "            if content_type == 'text/plain':\n", "                text = payload.decode(charset, errors='replace')\n", "                plain_parts.append(text)\n", "            elif content_type == 'text/html':\n", "                html = payload.decode(charset, errors='replace')\n", "                html_parts.append(html)\n", "        if plain_parts:\n", "            return \"\\n\".join(plain_parts).strip()\n", "        elif html_parts:\n", "            # If no plain text, fall back to BeautifulSoup HTML parsing\n", "            combined_html = \"\\n\".join(html_parts)\n", "            soup = BeautifulSoup(combined_html, 'html.parser')\n", "            return soup.get_text().strip()\n", "        else:\n", "            return \"\"\n", "    except Exception as e:\n", "        print(f\"[ERROR] Failed to parse email: {e}\")\n", "        return \"\"\n", "\n", "def extract_from_email_regex(raw_email):\n", "    # 1) Capture the entire 'From:' header (up to the next newline).\n", "    # We assume there's only one From: line and it's on a single line.\n", "    from_header_pattern = re.compile(\n", "        r'^From:\\s*(.*?)\\r?\\n'\n", "        , # Capture everything on the From: line\n", "        re.IGNORECASE | re.MULTILINE\n", "        )\n", "\n", "    match = from_header_pattern.search(raw_email)\n", "    if not match:\n", "        print(\"No 'From:' header found.\")\n", "        return None\n", "    else:\n", "        from_line = match.group(1).strip()\n", "        # 2) Extract the address from the from_line.\n", "        # We allow two forms:\n", "        # <AUTHOR> <EMAIL> or Name <email@domain>\n", "        # (B) Bare email: email@domain\n", "        from_email_pattern = re.compile(\n", "            r'(?:\"[^\"]*\"|[^\"<]+)?<([^>]+)>' # bracketed email\n", "            r'|([a-zA-Z0-9_.+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9.\\-]+)'\n", "            , # bare email\n", "            re.IGNORECASE\n", "            )\n", "        email_match = from_email_pattern.search(from_line)\n", "        if email_match:\n", "            # Exactly one of group(1) or group(2) will be non-empty\n", "            email_addr = email_match.group(1) if email_match.group(1) else email_match.group(2)\n", "            # print(\"From email is:\", email_addr)\n", "            return email_addr\n", "        else:\n", "            print(\"No email address found in From: header.\")\n", "        return None\n", "\n", "\n", "def extract_to_email_regex(raw_email):\n", "    # 1) Capture the entire 'To:' header. This regex:\n", "    # - Starts at a line beginning with 'To:'\n", "    # - Grabs everything until the next header line or end of string\n", "    # - Uses DOTALL so we can capture multiple lines.\n", "    to_header_pattern = re.compile(\n", "        r'^To:\\s*(.*?)\\n(?=[A-Za-z-]+?:|$)'\n", "        ,\n", "        re.IGNORECASE | re.DOTALL | re.MULTILINE\n", "        )\n", "    # 2) Extract all addresses. We have two possible forms:\n", "    # (A) Display name + < email >\n", "    # (B) Bare email (no < >).\n", "    # We'll use alternation in the same regex to handle both patterns.\n", "    # - The capture groups are (1) for the bracketed email, (2) for the bare email.\n", "    addresses_pattern = re.compile(\n", "        r'(?:\"[^\"]*\"|[^\"<,]+)?<([^>]+)>' # <AUTHOR> <EMAIL>\n", "        r'|([a-zA-Z0-9_.+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9.\\-]+)', # or abare email\n", "        re.IGNORECASE\n", "        )\n", "    match = to_header_pattern.search(raw_email)\n", "    if match:\n", "        to_line = match.group(1)\n", "        # 3) Find all possible addresses:\n", "        addresses = []\n", "        for m in addresses_pattern.finditer(to_line):\n", "            # One (and only one) group in each match will be non-empty\n", "            bracketed_email = m.group(1)\n", "            bare_email = m.group(2)\n", "            # Whichever one is not None is our address\n", "            email = bracketed_email if bracketed_email else bare_email\n", "            addresses.append(email)\n", "        if not addresses:\n", "            print(\"No to addresses found.\")\n", "        else:\n", "            # 4) Return the *first* address that ends in @acme.com.\n", "            # If none match, return the very first address found.\n", "            px_address = next((addr for addr in addresses if\n", "            addr.lower().endswith('@paychex.com')), None)\n", "            if px_address:\n", "                # print(\"Selected address:\", px_address)\n", "                return px_address\n", "            else:\n", "                # print(\"Selected address:\", addresses[0])\n", "                return addresses[0]\n", "    else:\n", "        print(\"No 'To:' line found.\")\n", "    return None\n", "\n", "# Now apply to your DataFrame\n", "\n", "def process_emails(df):\n", "    # Convert HTML to plain text\n", "    #df['plain_text_email'] = df['email_raw_format'].apply(html_to_plain_text)\n", "    df['to_email'] = df['EmailContent'].apply(lambda text: pd.Series(extract_to_email_regex(text)))\n", "    df['from_email'] = df['EmailContent'].apply(lambda text: pd.Series(extract_from_email_regex(text)))\n", "    df['SystemStartDateTime'] = pd.to_datetime(df['SystemStartDateTime'])\n", "    return df"], "outputs": [], "execution_count": 20}, {"cell_type": "code", "id": "23fb8fa4b19724cf", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:10.539326Z", "start_time": "2025-07-21T04:51:10.487085Z"}}, "source": ["data_path = \"C://Users/<USER>/payroll-email-agent/data/250618_SME_emails_100.xlsx\"\n", "df = pd.read_excel(data_path)\n", "df.drop(columns=['Unnamed: 0', 'Unnamed: 0.1'], inplace=True)"], "outputs": [], "execution_count": 21}, {"cell_type": "code", "id": "29d54742f0a82011", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:10.855768Z", "start_time": "2025-07-21T04:51:10.849729Z"}}, "source": ["df.head()"], "outputs": [{"data": {"text/plain": ["                                        EmailContent  \\\n", "0  Received: from exmbprw2.paychex.com (10.80.13....   \n", "1  Received: from exmbpro1.paychex.com (10.45.13....   \n", "2  Received: from exmbprw2.paychex.com (10.80.13....   \n", "3  Received: from exmbpro1.paychex.com (10.45.13....   \n", "4  MIME-Version: 1.0\\nDate: Thu, 03 Apr 2025 09:2...   \n", "\n", "                                    plain_text_email CC_ACCT_NBR  \\\n", "0  Hi\\n\\n\\nPlease release the payroll for 04/04.\\...    ********   \n", "1  You can go ahead and process it (Total Reclaim...    70182233   \n", "2  <PERSON>,\\nBelow is the payroll info for DRS...    13092755   \n", "3  <PERSON> <PERSON>,\\n\\nPlease process payroll for Mexic...    VTA04089   \n", "4  Hello Kwame,\\n \\nGreetings for the day!\\n \\nUp...    70190795   \n", "\n", "       SystemStartDateTime CustomerID  \n", "0  2025-04-02 11:09:00.967   ********  \n", "1  2025-04-02 15:31:33.210   70182233  \n", "2  2025-04-02 18:05:12.963   13092755  \n", "3  2025-04-02 18:29:24.500   VTA04089  \n", "4  2025-04-03 13:17:54.390   70190795  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EmailContent</th>\n", "      <th>plain_text_email</th>\n", "      <th>CC_ACCT_NBR</th>\n", "      <th>SystemStartDateTime</th>\n", "      <th>CustomerID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Received: from exmbprw2.paychex.com (10.80.13....</td>\n", "      <td>Hi\\n\\n\\nPlease release the payroll for 04/04.\\...</td>\n", "      <td>********</td>\n", "      <td>2025-04-02 11:09:00.967</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Received: from exmbpro1.paychex.com (10.45.13....</td>\n", "      <td>You can go ahead and process it (Total Reclaim...</td>\n", "      <td>70182233</td>\n", "      <td>2025-04-02 15:31:33.210</td>\n", "      <td>70182233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Received: from exmbprw2.paychex.com (10.80.13....</td>\n", "      <td><PERSON>,\\nBelow is the payroll info for DRS...</td>\n", "      <td>13092755</td>\n", "      <td>2025-04-02 18:05:12.963</td>\n", "      <td>13092755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Received: from exmbpro1.paychex.com (10.45.13....</td>\n", "      <td><PERSON>,\\n\\nPlease process payroll for Mexic...</td>\n", "      <td>VTA04089</td>\n", "      <td>2025-04-02 18:29:24.500</td>\n", "      <td>VTA04089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MIME-Version: 1.0\\nDate: Thu, 03 Apr 2025 09:2...</td>\n", "      <td>Hello Kwa<PERSON>,\\n \\nGreetings for the day!\\n \\nUp...</td>\n", "      <td>70190795</td>\n", "      <td>2025-04-03 13:17:54.390</td>\n", "      <td>70190795</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "execution_count": 22}, {"cell_type": "code", "id": "536ac667503cc964", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:11.363625Z", "start_time": "2025-07-21T04:51:11.349498Z"}}, "source": ["df = process_emails(df)\n", "df.head()"], "outputs": [{"data": {"text/plain": ["                                        EmailContent  \\\n", "0  Received: from exmbprw2.paychex.com (10.80.13....   \n", "1  Received: from exmbpro1.paychex.com (10.45.13....   \n", "2  Received: from exmbprw2.paychex.com (10.80.13....   \n", "3  Received: from exmbpro1.paychex.com (10.45.13....   \n", "4  MIME-Version: 1.0\\nDate: Thu, 03 Apr 2025 09:2...   \n", "\n", "                                    plain_text_email CC_ACCT_NBR  \\\n", "0  Hi\\n\\n\\nPlease release the payroll for 04/04.\\...    ********   \n", "1  You can go ahead and process it (Total Reclaim...    70182233   \n", "2  <PERSON>,\\nBelow is the payroll info for DRS...    13092755   \n", "3  <PERSON> <PERSON>,\\n\\nPlease process payroll for Mexic...    VTA04089   \n", "4  Hello Kwame,\\n \\nGreetings for the day!\\n \\nUp...    70190795   \n", "\n", "      SystemStartDateTime CustomerID                    to_email  \\\n", "0 2025-04-02 11:09:00.967   ********  <EMAIL>   \n", "1 2025-04-02 15:31:33.210   70182233  <EMAIL>   \n", "2 2025-04-02 18:05:12.963   13092755    <EMAIL>   \n", "3 2025-04-02 18:29:24.500   VTA04089        <EMAIL>   \n", "4 2025-04-03 13:17:54.390   70190795      <EMAIL>   \n", "\n", "                      from_email  \n", "0  <EMAIL>  \n", "1        <EMAIL>  \n", "2     <EMAIL>  \n", "3          <EMAIL>  \n", "4     <EMAIL>  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EmailContent</th>\n", "      <th>plain_text_email</th>\n", "      <th>CC_ACCT_NBR</th>\n", "      <th>SystemStartDateTime</th>\n", "      <th>CustomerID</th>\n", "      <th>to_email</th>\n", "      <th>from_email</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Received: from exmbprw2.paychex.com (10.80.13....</td>\n", "      <td>Hi\\n\\n\\nPlease release the payroll for 04/04.\\...</td>\n", "      <td>********</td>\n", "      <td>2025-04-02 11:09:00.967</td>\n", "      <td>********</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Received: from exmbpro1.paychex.com (10.45.13....</td>\n", "      <td>You can go ahead and process it (Total Reclaim...</td>\n", "      <td>70182233</td>\n", "      <td>2025-04-02 15:31:33.210</td>\n", "      <td>70182233</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Received: from exmbprw2.paychex.com (10.80.13....</td>\n", "      <td><PERSON>,\\nBelow is the payroll info for DRS...</td>\n", "      <td>13092755</td>\n", "      <td>2025-04-02 18:05:12.963</td>\n", "      <td>13092755</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Received: from exmbpro1.paychex.com (10.45.13....</td>\n", "      <td><PERSON>,\\n\\nPlease process payroll for Mexic...</td>\n", "      <td>VTA04089</td>\n", "      <td>2025-04-02 18:29:24.500</td>\n", "      <td>VTA04089</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MIME-Version: 1.0\\nDate: Thu, 03 Apr 2025 09:2...</td>\n", "      <td>Hello Kwa<PERSON>,\\n \\nGreetings for the day!\\n \\nUp...</td>\n", "      <td>70190795</td>\n", "      <td>2025-04-03 13:17:54.390</td>\n", "      <td>70190795</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "execution_count": 23}, {"cell_type": "code", "id": "31581eff1cf60f16", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:43.791881Z", "start_time": "2025-07-21T04:51:43.788445Z"}}, "source": ["def map_case_to_placeholder(row):\n", "    \"\"\"Maps a test case to the placeholder structure.\"\"\"\n", "    return {\n", "      \"companyID\": row['CustomerID'],\n", "      \"comment\": {\n", "        \"uid\": row['CC_ACCT_NBR'],\n", "        \"id\":  row['CC_ACCT_NBR'],\n", "        \"timestamp\": row['SystemStartDateTime'].isoformat(),\n", "        \"user_properties\": {\n", "          \"string:Sender\": row['from_email'],\n", "          \"string:Sender Domain\": row['from_email'].split('@')[-1],\n", "        },\n", "        \"messages\": [\n", "          {\n", "            \"from\": row['from_email'],\n", "            \"to\": row['to_email'] if type(row['to_email']) == list else [row['to_email']] ,\n", "            \"body\": {\n", "              \"text\": row['plain_text_email'],\n", "            },\n", "            \"subject\": {\n", "              \"text\":  \"Subject\",\n", "            }\n", "          }\n", "        ]\n", "      }\n", "    }\n"], "outputs": [], "execution_count": 24}, {"cell_type": "code", "id": "e4379a112a5d933b", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:51:45.518295Z", "start_time": "2025-07-21T04:51:45.514491Z"}}, "source": ["def write_upstream_test_file(test_path, df):\n", "    path = os.path.join('evaluation_cases', test_path)\n", "    js = [map_case_to_placeholder(case) for i, case in df.iterrows()]\n", "\n", "    output_path = os.path.join('evaluation_cases', test_path)\n", "    with open(output_path, 'w', encoding='utf-8') as f:\n", "      json.dump(js, f, indent=2, ensure_ascii=False)"], "outputs": [], "execution_count": 25}, {"cell_type": "code", "id": "cef00193ecaf9508", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T04:53:18.274399Z", "start_time": "2025-07-21T04:53:18.270813Z"}}, "source": "write_upstream_test_file('C://Users/<USER>/payroll-email-agent/data/250618_SME_emails_100_prepared.json', df)", "outputs": [], "execution_count": 28}, {"cell_type": "code", "execution_count": null, "id": "11eb58e8351369c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}