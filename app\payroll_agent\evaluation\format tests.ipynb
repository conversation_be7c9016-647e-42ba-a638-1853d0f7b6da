{"cells": [{"cell_type": "code", "execution_count": 76, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-06-11T14:41:58.898032Z", "start_time": "2025-06-11T14:41:58.894310Z"}, "collapsed": true}, "outputs": [], "source": ["import os\n", "import json"]}, {"cell_type": "code", "execution_count": 85, "id": "4565245cfa6e0729", "metadata": {"ExecuteTime": {"end_time": "2025-06-11T14:54:32.739051Z", "start_time": "2025-06-11T14:54:32.731665Z"}}, "outputs": [], "source": ["\n", "def map_case_to_placeholder(case, ):\n", "    \"\"\"Maps a test case to the placeholder structure.\"\"\"\n", "    return {\n", "      \"comment\": {\n", "        \"uid\": case.get(\"IMSEPCID\"),\n", "        \"id\":  case.get(\"IMSEPCID\"),\n", "        \"user_properties\": {\n", "          \"string:Sender\": case.get('SourceAddress'),\n", "          \"string:Sender Domain\": case.get('CustomerID'),\n", "        },\n", "        \"messages\": [\n", "          {\n", "            \"from\": case.get('SourceAddress'),\n", "            \"to\": [case.get('DestinationAddress'),],\n", "            \"body\": {\n", "              \"text\": case.get('EmailContent'),\n", "            },\n", "            \"subject\": {\n", "              \"text\":  case.get('Subject'),\n", "            }\n", "          }\n", "        ]\n", "      }\n", "    }"]}, {"cell_type": "code", "execution_count": 21, "id": "88f7c81003b20424", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T19:44:55.722484Z", "start_time": "2025-06-12T19:44:55.718152Z"}}, "outputs": [], "source": ["\n", "def map_case_to_placeholder(case, ):\n", "    \"\"\"Maps a test case to the placeholder structure.\"\"\"\n", "    return {\n", "      \"companyID\": \"A1\",\n", "      \"comment\": {\n", "        \"uid\": case[\"comment\"][\"uid\"],\n", "        \"id\":  case[\"comment\"][\"uid\"],\n", "        \"user_properties\": {\n", "          \"string:Sender\": case[\"comment\"][\"user_properties\"][\"string:Sender\"],\n", "          \"string:Sender Domain\": case[\"comment\"][\"user_properties\"][\"string:Sender Domain\"],\n", "        },\n", "        \"messages\": [\n", "          {\n", "            \"from\": case[\"comment\"][\"messages\"][0][\"from\"],\n", "            \"to\": case[\"comment\"][\"messages\"][0][\"to\"],\n", "            \"body\": {\n", "              \"text\": case[\"comment\"][\"messages\"][0][\"body\"][\"text\"],\n", "            },\n", "            \"subject\": {\n", "              \"text\":  case[\"comment\"][\"messages\"][0][\"subject\"][\"text\"],\n", "            }\n", "          }\n", "        ]\n", "      }\n", "    }\n"]}, {"cell_type": "code", "execution_count": 22, "id": "216b9447349914d8", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T19:44:56.989828Z", "start_time": "2025-06-12T19:44:56.985621Z"}}, "outputs": [{"data": {"text/plain": ["{'comment': {'uid': 'TEST_005',\n", "  'id': 'TEST_005',\n", "  'user_properties': {'string:Sender': '<EMAIL>',\n", "   'string:Sender Domain': 'ACC003'},\n", "  'messages': [{'from': '<EMAIL>',\n", "    'to': ['<EMAIL>'],\n", "    'body': {'text': 'Good Morning Chris & Briania, Payroll for Name Electrical & Construction for week ending Friday November 22, 2024\\nRon Shorter    8 hours.\\nThanks\\nRegena'},\n", "    'subject': {'text': 'Payroll - Name Electrical & Construction 11/22/24'}}]}}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["{\n", "    \"comment\": {\n", "      \"uid\": \"TEST_005\",\n", "      \"id\": \"TEST_005\",\n", "      \"user_properties\": {\n", "        \"string:Sender\": \"<EMAIL>\",\n", "        \"string:Sender Domain\": \"ACC003\"\n", "      },\n", "      \"messages\": [\n", "        {\n", "          \"from\": \"<EMAIL>\",\n", "          \"to\": [\n", "            \"<EMAIL>\"\n", "          ],\n", "          \"body\": {\n", "            \"text\": \"Good Morning Chris & Briania, Payroll for Name Electrical & Construction for week ending Friday November 22, 2024\\nRon Shorter    8 hours.\\nThanks\\nRegena\"\n", "          },\n", "          \"subject\": {\n", "            \"text\": \"Payroll - Name Electrical & Construction 11/22/24\"\n", "          }\n", "        }\n", "      ]\n", "    }\n", "  }"]}, {"cell_type": "code", "execution_count": 25, "id": "7201ff2373aea10a", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T19:45:17.001454Z", "start_time": "2025-06-12T19:45:16.997246Z"}}, "outputs": [], "source": ["def write_upstream_test_file(test_path):\n", "    path = os.path.join('evaluation_cases', test_path)\n", "    with open(path, 'r', encoding='utf-8') as f:\n", "      tests_examples = json.load(f)\n", "    js = [map_case_to_placeholder(case) for case in tests_examples]\n", "\n", "    output_path = os.path.join('evaluation_cases', test_path)\n", "    with open(output_path, 'w', encoding='utf-8') as f:\n", "      json.dump(js, f, indent=2, ensure_ascii=False)"]}, {"cell_type": "code", "execution_count": 26, "id": "c339991e3018b7be", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T19:45:17.504324Z", "start_time": "2025-06-12T19:45:17.485715Z"}}, "outputs": [{"data": {"text/plain": ["[None, None, None, None, None, None, None, None, None, None]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["paths = [x for x in os.listdir('evaluation_cases') if x.endswith('.json')]\n", "list(map(write_upstream_test_file, paths))"]}, {"cell_type": "code", "execution_count": 27, "id": "607b68b49fd468c7", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T19:45:18.885563Z", "start_time": "2025-06-12T19:45:18.882894Z"}}, "outputs": [{"data": {"text/plain": ["['salary_pay_tests.json',\n", " 'hourly_pay_tests.json',\n", " 'complex_tests.json',\n", " 'non_payroll_tests.json',\n", " 'multiple_pay_types_tests.json',\n", " 'tips_pay_tests.json',\n", " 'hundred_test_cases.json',\n", " 'bonus_pay_tests.json',\n", " 'easy_tests.json',\n", " 'gross_pay_tests.json']"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["paths"]}, {"cell_type": "code", "execution_count": null, "id": "6e33b241e153e079", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}