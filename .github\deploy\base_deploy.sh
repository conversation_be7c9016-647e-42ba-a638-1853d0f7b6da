# Initial setup for Azure resources
# Run this script once to create the initial resources for subscription
# Do not run this block per repo, only once
SUB="sub-paychexai-sandbox-002"
RG="rg-emailpayrollautomation-eastus-sb-001"
LOC="eastus"
ACR="acrpayrollaiagentsb001"
ACR_PRIVATE_ENDPOINT_NAME="pept-snet1-eastus-acr-payrollai-sb-001"
KV="kv-payrollai-sb-001"
APPCONFIG_NAME="appcfg-payrollai-sb-001"
EVENTHUB_NAMESPACE="evh-payrollai-shared-eastus-sandbox-001"
EH_HUB="applogs-conapp-payrollai-logging"
POSTGRES_SERVER_NAME="psql-payrollaiagent-sb-001"


# network
VNET_NAME="vnet-paychexai-eastus-sandbox-001"
VNET_ADDRESS_PREFIX="**********/21"
DNS_SERVER="*************"
SHARED_SUBNET_NAME="snet-payrollai-shared-sandbox-001"
SHARED_SUBNET_PREFIX="**********/26"
PRIVATE_ENDPOINT_SUBNET_NAME="snet-payrollai-pe-sandbox-001"
PRIVATE_ENDPOINT_SUBNET_PREFIX="***********/26"
KV_PRIVATE_ENDPOINT_NAME="pept-snet1-eastus-kv-payrollai-sb-001"
POSTGRES_PE_NAME="pept-snet1-eastus-postgres-payrollai-sb-001"

# Network Security Groups
NSG_CONAPPS="nsg-payrollai-conapps-sandbox"
NSG_PRIVATE_ENDPOINT="nsg-payrollai-pe-sandbox"
NSG_SHARED="nsg-payrollai-shared-sandbox"



# Select the subscription
az account set --subscription $SUB


# artifacts
# Create Azure App Configuration service
az appconfig create --name $APPCONFIG_NAME --resource-group $RG --location $LOC

# Create VNet and Subnet for ACA Environment
az network vnet create \
  --name $VNET_NAME \
  --resource-group $RG \
  --location $LOC \
  --address-prefix $VNET_ADDRESS_PREFIX \
  --dns-servers $DNS_SERVER

az network vnet subnet create \
  --vnet-name $VNET_NAME \
  --resource-group $RG \
  --name $SHARED_SUBNET_NAME \
  --address-prefixes $SHARED_SUBNET_PREFIX

az network vnet subnet create \
  --vnet-name $VNET_NAME \
  --resource-group $RG \
  --name $PRIVATE_ENDPOINT_SUBNET_NAME \
  --address-prefixes $PRIVATE_ENDPOINT_SUBNET_PREFIX


# Create Network Security Groups
az network nsg create -g "$RG" -n "$NSG_CONAPPS" --location "$LOC"
az network nsg create -g "$RG" -n "$NSG_PRIVATE_ENDPOINT" --location "$LOC"
az network nsg create -g "$RG" -n "$NSG_SHARED" --location "$LOC"


# Add rules to NSGs

# a) ACA → SQL Private Endpoint (outbound 1433)
az network nsg rule create -g "$RG" -n "allow-aca-postgres-pe-5432-out" \
  --nsg-name "$NSG_CONAPPS" --priority 100 \
  --direction Outbound --access Allow --protocol Tcp \
  --destination-address-prefixes "$PRIVATE_ENDPOINT_SUBNET_PREFIX" --destination-port-ranges 5432 \
  --source-address-prefixes VirtualNetwork

# b) ACA → Paychex external API (outbound 443 to ***********/16)
az network nsg rule create -g "$RG" -n "allow-aca-paychex-ext-443-out" \
  --nsg-name "$NSG_CONAPPS" --priority 110 \
  --direction Outbound --access Allow --protocol Tcp \
  --destination-address-prefixes ***********/16 --destination-port-ranges 443 \
  --source-address-prefixes VirtualNetwork

# c) On-prem → ACA ingress (inbound 443 from 10.0.0.0/8)
az network nsg rule create -g "$RG" -n "allow-onprem-aca-443-in" \
  --nsg-name "$NSG_CONAPPS" --priority 120 \
  --direction Inbound --access Allow --protocol Tcp \
  --source-address-prefixes 10.0.0.0/8 --destination-port-ranges 443 \
  --destination-address-prefixes VirtualNetwork

# d) ACA → internal Paychex (outbound 443 to 10.0.0.0/8)
az network nsg rule create -g "$RG" -n "allow-aca-onprem-443-out" \
  --nsg-name "$NSG_CONAPPS" --priority 130 \
  --direction Outbound --access Allow --protocol Tcp \
  --destination-address-prefixes 10.0.0.0/8 --destination-port-ranges 443 \
  --source-address-prefixes VirtualNetwork

# e) On-prem → Private Endpoints (inbound 443 from 10.0.0.0/8)
az network nsg rule create -g "$RG" -n "allow-onprem-pe-443-in" \
  --nsg-name "$NSG_PRIVATE_ENDPOINT" --priority 100 \
  --direction Inbound --access Allow --protocol Tcp \
  --source-address-prefixes 10.0.0.0/8 --destination-port-ranges 443 \
  --destination-address-prefixes VirtualNetwork

# f) KEY VAULT SUBNET INBOUND RULE
az network nsg rule create -g "$RG" -n "allow-shared-outbound-443" \
  --nsg-name "$NSG_SHARED" --priority 100 \
  --direction Outbound --access Allow --protocol Tcp \
  --destination-address-prefixes VirtualNetwork --destination-port-ranges 443 \
  --source-address-prefixes VirtualNetwork


# Associate NSGs with subnets
az network vnet subnet update \
    --resource-group "$RG" --vnet-name "$VNET_NAME" \
    --name "$PRIVATE_ENDPOINT_SUBNET_NAME" --network-security-group "$NSG_PRIVATE_ENDPOINT"

az network vnet subnet update \
    --resource-group "$RG" --vnet-name "$VNET_NAME" \
    --name "$SHARED_SUBNET_NAME" --network-security-group "$NSG_SHARED"

# Create the Key Vault

az keyvault create \
  --name "$KV" \
  --resource-group "$RG" \
  --location "$LOC" \
  --sku standard \
  --enable-rbac-authorization true \
  --public-network-access Disabled

# Add service endpoint for Key Vault
az network vnet subnet update \
  --resource-group "$RG" \
  --vnet-name "$VNET_NAME" \
  --name "$SHARED_SUBNET_NAME" \
  --service-endpoints Microsoft.KeyVault

# Allow the KV subnet
az keyvault network-rule add \
  --name "$KV" \
  --resource-group "$RG" \
  --vnet-name "$VNET_NAME" \
  --subnet "$SHARED_SUBNET_NAME"

# Create Keyvault Private Endpoint
KV_ID=$(az keyvault show -g "$RG" -n "$KV" --query id -o tsv)

az network private-endpoint create \
  --name "$KV_PRIVATE_ENDPOINT_NAME" \
  --resource-group "$RG" \
  --vnet-name "$VNET_NAME" \
  --subnet "$PRIVATE_ENDPOINT_SUBNET_NAME" \
  --private-connection-resource-id "$KV_ID" \
  --group-ids vault \
  --connection-name "${KV_PRIVATE_ENDPOINT_NAME}-conn"


# Create the ACR
az acr create \
  --name "$ACR" \
  --resource-group "$RG" \
  --location "$LOC" \
  --sku Premium

# Disable public access (forces private link)
az acr update \
  --name "$ACR" \
  --public-network-enabled false

# Create the Private Endpoint
ACR_ID=$(az acr show -g "$RG" -n "$ACR" --query id -o tsv)

az network private-endpoint create \
  --name "$ACR_PRIVATE_ENDPOINT_NAME" \
  --resource-group "$RG" \
  --vnet-name "$VNET_NAME" \
  --subnet "$PRIVATE_ENDPOINT_SUBNET_NAME" \
  --private-connection-resource-id "$ACR_ID" \
  --group-id registry \
  --connection-name "${ACR_PRIVATE_ENDPOINT_NAME}-conn"


# Create Event Hub Namespace
az eventhubs namespace create \
  --name "$EVENTHUB_NAMESPACE" \
  --resource-group "$RG" \
  --location "$LOC" \
  --sku Standard \
  --enable-auto-inflate false

# Create the Event Hub
az eventhubs eventhub create \
  --name "$EH_HUB" \
  --namespace-name "$EVENTHUB_NAMESPACE" \
  --resource-group "$RG" \
  --retention-time-in-hours 24 \
  --partition-count 2 \
  --cleanup-policy Delete


# Create private DNS zones and link to VNet
az network private-dns zone create -g "$RG" -n "privatelink.vaultcore.azure.net"
az network private-dns link vnet create -g "$RG" -n "kv-dns-link" -z "privatelink.vaultcore.azure.net" -v "$VNET_NAME" -e false

KV_PRIVATE_IP=$(az network private-endpoint show \
  --name "$KV_PRIVATE_ENDPOINT_NAME" \
  --resource-group "$RG" \
  --query "customDnsConfigs[0].ipAddresses[0]" -o tsv)


az network private-dns record-set a create \
  -g "$RG" \
  -z "privatelink.vaultcore.azure.net" \
  -n "$KV"

az network private-dns record-set a add-record \
  -g "$RG" \
  -z "privatelink.vaultcore.azure.net" \
  -n "$KV" \
  -a "$KV_PRIVATE_IP"


# Create ACR Private DNS Zone
az network private-dns zone create -g "$RG" -n "privatelink.azurecr.io"
az network private-dns link vnet create -g "$RG" -n "acr-dns-link" -z "privatelink.azurecr.io" -v "$VNET_NAME" -e false

ACR_PRIVATE_IP=$(az network private-endpoint show \
  --name "$ACR_PRIVATE_ENDPOINT_NAME" \
  --resource-group "$RG" \
  --query "customDnsConfigs[0].ipAddresses[0]" -o tsv)


az network private-dns record-set a create \
  -g "$RG" \
  -z "privatelink.azurecr.io" \
  -n "$ACR"

az network private-dns record-set a add-record \
  -g "$RG" \
  -z "privatelink.azurecr.io" \
  -n "$ACR" \
  -a "$ACR_PRIVATE_IP"

az network private-endpoint dns-zone-group create \
  --resource-group "$RG" \
  --endpoint-name "$ACR_PRIVATE_ENDPOINT_NAME" \
  --name "acr-dns" \
  --private-dns-zone "privatelink.azurecr.io" \
  --zone-name "privatelink.azurecr.io"


# Create POSTGRES Private DNS Zone
az network private-dns zone create -g "$RG" -n "privatelink.postgres.database.azure.com"
az network private-dns link vnet create -g "$RG" -n "postgres-dns-link" -z "privatelink.postgres.database.azure.com" -v "$VNET_NAME" -e false


# END INITIAL SETUP
