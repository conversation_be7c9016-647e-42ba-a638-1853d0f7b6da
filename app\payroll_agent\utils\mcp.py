from functools import lru_cache
from typing import Dict, Any, Optional
import threading
import time
import httpx

from langchain_mcp_adapters.client import MultiServerMCPClient
from app.payroll_agent.config.config import settings
from app.cli.logging_utils import setup_logger


_TOKEN_CACHE: Dict[str, Dict[str, Any]] = {}
_TOKEN_LOCKS: Dict[str, threading.Lock] = {}
_DEFAULT_TTL_SECONDS = 55 * 60  # 55 minutes


def _cache_key(server_name: str, client_id: Optional[str], auth_url: Optional[str]) -> str:
    return f"{server_name}:{client_id or ''}:{auth_url or ''}"


def _get_lock(key: str) -> threading.Lock:
    if key not in _TOKEN_LOCKS:
        _TOKEN_LOCKS[key] = threading.Lock()
    return _TOKEN_LOCKS[key]


def get_mcp_token(server_name: str) -> Optional[str]:
    """Get a cached token for a server or fetch a new one.

    Token is cached in-memory with TTL based on `expires_in` if present,
    otherwise a default TTL is used.
    """
    try:
        conn = settings.mcp_servers.get(server_name)
        if not conn:
            return None

        auth_url = getattr(conn, 'authentication_url', None)
        client_id = getattr(conn, 'client_id', None)
        client_secret = getattr(conn, 'client_secret', None)
        if not (auth_url and client_id and client_secret):
            return None
        # Fetch new token
        with httpx.Client(timeout=10.0) as client:
            resp = client.post(
                auth_url,
                data={
                    "username": client_id,
                    "password": client_secret,
                    "scope": "",
                    },
                )
            resp.raise_for_status()
            payload = resp.json()
            token = payload.get("access_token")
            return token
    except Exception as e:
        print("*"*100)
        print(f"Failed to get MCP token: {e}")


def refresh_mcp_token(server_name: str) -> Optional[str]:
    """Force refresh a token for the given server and update cache."""
    conn = settings.mcp_servers.get(server_name)
    if not conn:
        return None
    key = _cache_key(server_name, getattr(conn, 'client_id', None), getattr(conn, 'authentication_url', None))
    # Invalidate
    _TOKEN_CACHE.pop(key, None)
    return get_mcp_token(server_name)


def get_mcp_auth_header(server_name: str) -> Dict[str, str]:
    token = get_mcp_token(server_name)
    return {"Authorization": f"Bearer {token}"} if token else {}


class MCPService:
    def __init__(self):
        self.logger = setup_logger(__name__)
        try:
            self.logger.info("Initializing MCP client")
            self._rebuild_client_with_tokens()
            self.logger.debug("MCP client initialized")
            # cache: { server_name: [StructuredTool, …], … }
            self.tools: dict[str, list] = {}
        except ConnectionError as e:
            self.logger.error(f"Failed to connect to MCP server: {type(e).__name__} - {e}", exc_info=True)
            raise

    def _rebuild_client_with_tokens(self) -> None:
        # Start from a dict representation to pass into the adapter
        servers = settings.model_dump()['mcp_servers']
        # Inject Authorization headers from cache (or fetch if missing)
        for name, cfg in servers.items():
            try:
                header = get_mcp_auth_header(name)
                if header:
                    cfg.setdefault('headers', {})
                    cfg['headers'] = header
            except Exception as e:
                # Don't fail init if one token fetch fails; continue
                self.logger.warning(f"Unable to prepare token for server '{name}': {e}")
        self.client = MultiServerMCPClient(servers)

    async def get_tools_from_server(self, server_name: str):

        # otherwise fetch, cache, and return
        try:
            self.logger.debug(f"Fetching tools from MCP server '{server_name}'")
            tools = await self.client.get_tools(server_name=server_name)
            self.logger.debug(f"Cached {len(tools)} tools for server '{server_name}'")
            return tools
        except Exception as e:
            # Attempt refresh on 401-like errors
            self.logger.info(f"401 received fetching tools. Refreshing token for '{server_name}' and retrying...")
            try:
                #refresh_mcp_token(server_name)
                self._rebuild_client_with_tokens()
                tools = await self.client.get_tools(server_name=server_name)
                return tools
            except Exception as e2:
                self.logger.error(
                    f"Retry after token refresh failed for server '{server_name}': {type(e2).__name__} - {e2}",
                    exc_info=True,
                )
                raise


    async def call_mcp_tool(self, server_name: str, tool_name: str, tool_input: dict, session_id: Optional[str] = None):
        try:
            self.logger.debug(f"Calling MCP tool '{tool_name}' on server '{server_name}' with session ID: {session_id}")
            tools = await self.get_tools_from_server(server_name)
            tool = next(t for t in tools if t.name == tool_name)
            
            # Add session ID to tool input if provided
            if session_id:
                if isinstance(tool_input, dict):
                    tool_input["x_payx_sid"] = session_id
                else:
                    self.logger.warning(f"Cannot add session ID to non-dict tool_input: {type(tool_input)}")
                    
            result = await tool.arun(tool_input)
            self.logger.debug(f"Tool '{tool_name}' result: {result}")
            return result
        except StopIteration:
            msg = f"Tool '{tool_name}' not found on server '{server_name}'"
            self.logger.error(msg)
            raise ValueError(msg)
        except Exception as e:
            # Handle 401 during tool execution as well
            self.logger.info(f"401 received calling tool. Refreshing token for '{server_name}' and retrying '{tool_name}'...")
            try:
                self._rebuild_client_with_tokens()
                tools = await self.get_tools_from_server(server_name)
                tool = next(t for t in tools if t.name == tool_name)
                return await tool.arun(tool_input)
            except Exception as e2:
                self.logger.error(
                    f"Retry after token refresh failed for '{server_name}.{tool_name}': {type(e2).__name__} - {e2}",
                    exc_info=True,
                )
                raise


    async def get_tool(self, server_name: str, tool_name: str):
        try:
            self.logger.debug(f"Retrieving tool '{tool_name}' from server '{server_name}'")
            tools = await self.get_tools_from_server(server_name)
            tool = next(t for t in tools if t.name == tool_name)
            return tool
        except StopIteration as e:
            msg = f"Tool '{tool_name}' not found on server '{server_name}' : {type(e).__name__} - {e}"
            self.logger.error(msg, exc_info=True)
            raise ValueError(msg)
        except Exception as e:
            self.logger.error(f"Failed to retrieve MCP tool '{tool_name}': {type(e).__name__} - {e}", exc_info=True)
            raise


@lru_cache()
def get_mcp_service():
    return MCPService()