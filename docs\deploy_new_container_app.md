# Payroll Email Agent - Multi-Branch Deployment Guide

This guide explains how to set up automated deployments for multiple branches using the DRY (Don't Repeat Yourself) GitHub Actions pipeline. The system supports automated deployment to `dev`, `demo`, and `test_paychex` environments.

## 🚀 Automated Pipeline Deployment (Recommended)

### Overview

The project now uses a **reusable GitHub Action** that eliminates code duplication and provides consistent deployments across all environments. The pipeline automatically:

- Builds Docker images with environment-specific tags
- Pushes to Azure Container Registry
- Deploys to Azure Container Apps
- Configures environment variables from App Config and Key Vault

### Supported Branches & Environments

| Branch | Environment | Image Tag | Container App |
|--------|-------------|-----------|---------------|
| `dev` | dev | `dev.latest` / `dev.{sha}` | `conagenticengineeastussb001` |
| `demo` | demo | `demo.latest` / `demo.{sha}` | `conagenticengineeastussb001-demo` |
| `test_paychex` | test_paychex | `test_paychex.latest` / `test_paychex.{sha}` | `conagenticengineeastussb001-test` |

### How to Deploy

1. **Push to target branch**: Simply push your code to `dev`, `demo`, or `test_paychex`
2. **Automatic trigger**: GitHub Actions workflow automatically starts
3. **Environment-specific deployment**: Pipeline uses correct resources for each environment

```bash
# Deploy to dev environment
git checkout dev
git push origin dev

# Deploy to demo environment  
git checkout demo
git push origin demo

# Deploy to test_paychex environment
git checkout test_paychex
git push origin test_paychex
```

### Manual Trigger

You can also trigger deployments manually via GitHub Actions UI:

1. Go to **Actions** tab in GitHub repository
2. Select the appropriate workflow (CD - Payroll Email Agent)
3. Click **Run workflow**
4. Select the branch and click **Run workflow**

## 🔧 Pipeline Configuration

### Reusable Action Location
```
.github/actions/deploy-container-app/action.yml
```

### Workflow Files
```
.github/workflows/cd_dev.yml          # Dev environment
.github/workflows/cd_demo.yml         # Demo environment  
.github/workflows/cd_test_paychex.yml # Test_paychex environment
```

### Environment-Specific Resources

The pipeline automatically configures resources based on the target environment:

#### Dev Environment
- **Resource Group**: `rg-emailpayrollautomation-eastus-sb-001`
- **Container App**: `conagenticengineeastussb001`
- **App Config**: `appcfg-payrollai-sb-001`
- **Key Vault**: `kv-payrollai-sb-001`

#### Demo Environment
- **Resource Group**: `rg-emailpayrollautomation-eastus-sb-001`
- **Container App**: `conagenticengineeastussb001-demo`
- **App Config**: `appcfg-payrollai-sb-001`
- **Key Vault**: `kv-payrollai-sb-001`

#### Test_Paychex Environment
- **Resource Group**: `rg-emailpayrollautomation-eastus-sb-001`
- **Container App**: `conagenticengineeastussb001-test`
- **App Config**: `appcfg-payrollai-sb-001`
- **Key Vault**: `kv-payrollai-sb-001`

## 📋 Prerequisites for New Environments

Before deploying to a new environment, ensure:

### 1. GitHub Environment Setup
- Create GitHub environment in repository settings
- Configure required secrets:
  - `AZURE_SUBSCRIPTION_ID`
  - `AZURE_REGISTRY_PASSWORD`

### 2. Azure Resources
- Resource Group exists
- Container App exists (can be created manually first time)
- App Configuration service exists with required keys
- Key Vault exists with required secrets

### 3. Required App Config Keys
```
MCP_SERVERS__PAYCHEX__URL
MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL  
MCP_SERVERS__PAYCHEX__TRANSPORT
LANGCHAIN_TRACING_V2
LANGCHAIN_ENDPOINT
LANGCHAIN_PROJECT
LLM_TYPE
AZURE_ENDPOINT
API_VERSION
```

### 4. Required Key Vault Secrets
```
openai-key
langchain-api-key
mcp-client-id
mcp-client-secret
```

## 🛠️ Adding a New Branch/Environment

To add support for a new branch (e.g., `staging`):

### Step 1: Update the Reusable Action
Edit `.github/actions/deploy-container-app/action.yml` and add your new environment:

```yaml
case "${{ inputs.environment }}" in
  "dev")
    # existing dev config
    ;;
  "demo")
    # existing demo config
    ;;
  "test_paychex")
    # existing test_paychex config
    ;;
  "staging")
    echo "AZURE_CONTAINER_APP=conagenticengineeastussb001-staging" >> $GITHUB_ENV
    ;;
  *)
    echo "Unknown environment: ${{ inputs.environment }}"
    exit 1
    ;;
esac
```

### Step 2: Create New Workflow File
Create `.github/workflows/cd_staging.yml`:

```yaml
name: CD - Payroll Email Agent (staging)

on:
  push:
    branches: [staging]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to staging HEAD)'
        required: false
        default: staging

jobs:
  build-and-deploy:
    runs-on: [self-hosted, nonprod]
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.container-app-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Deploy to Azure Container App
        id: deploy
        uses: ./.github/actions/deploy-container-app
        with:
          environment: staging
          azure-subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          azure-registry-password: ${{ secrets.AZURE_REGISTRY_PASSWORD }}
          git-sha: ${{ github.sha }}
```

### Step 3: Create Azure Resources
Ensure all required Azure resources exist for the new environment.

## 🔍 Monitoring and Troubleshooting

### View Deployment Status
1. Go to **Actions** tab in GitHub repository
2. Click on the running/completed workflow
3. View logs for each step

### Common Issues

**Pipeline fails with "Environment not found"**
- Create the GitHub environment in repository settings
- Configure required secrets

**Azure resources not found**
- Verify resource names in the reusable action match actual Azure resources
- Ensure proper permissions for the service principal

**Image build fails**
- Check Docker build logs in the pipeline
- Verify Dockerfile exists and is valid

**Deployment fails**
- Check Azure Container App logs
- Verify App Config and Key Vault contain required values
- Ensure container app has proper managed identity permissions

## 📚 Legacy Manual Deployment

<details>
<summary>Click to expand manual deployment instructions (for reference only)</summary>

> ⚠️ **Note**: Manual deployment is not recommended. Use the automated pipeline instead.

## Prerequisites

- Azure CLI installed and authenticated
- Access to the sandbox subscription and resource group
- Permissions to read from Key Vault and App Configuration

## Step 1: Set Environment Variables

```bash
# Azure Resources
SUB="sub-paychexai-sandbox-002"
RG="rg-emailpayrollautomation-eastus-sb-001"
LOC="eastus"
ACR="conpayrollsb001"
ACA_ENV_AGENT_MCP="cenv-payrollemailagenteastus-sandbox-001"
KV="kv-payrollai-sb-001"
APPCONFIG_NAME="appcfg-payrollai-sb-001"
EVENTHUB_NAMESPACE="evh-payrollai-shared-eastus-sandbox-001"
EH_HUB="applogs-conapp-payrollai-logging"

# Container App Configuration
APP_AGENT="conagenticengineeastussb001-test"  # Update suffix for your branch
IMAGE_REPO="paychex/payroll-ai-agent"

# Network Configuration
ACA_SUBNET_ID_AGENT_MCP=/subscriptions/3fe57120-1694-4533-ac29-a445c6e071ff/resourceGroups/rg-paychexai-shared-eastus-sandbox-001/providers/Microsoft.Network/virtualNetworks/vnet-paychexai-eastus-sandbox-001/subnets/snet-payrollai-conapps-sandbox-001
```

## Step 2: Retrieve Configuration Values

### Get Container App Environment ID
```bash
ACA_ENV_AGENT_ID=$(az containerapp env show -g "$RG" -n "$ACA_ENV_AGENT_MCP" --query id -o tsv)
```

### Get Event Hub Connection String
```bash
EVH_CONN=$(az eventhubs namespace authorization-rule keys list \
           -g "$RG" --namespace-name "$EVENTHUB_NAMESPACE" \
           --name "RootManageSharedAccessKey" \
           --query primaryConnectionString -o tsv)
```

### Get ACR Access Token
```bash
ACR_PASSWORD=$(az acr login --name "$ACR" --expose-token --output tsv --query accessToken)
```

### Get Secrets from Key Vault
```bash
MCP_CLIENT_ID=$(az keyvault secret show --vault-name "$KV" --name "MCP-CLIENT-ID" --query "value" -o tsv)
MCP_CLIENT_SECRET=$(az keyvault secret show --vault-name "$KV" --name "MCP-CLIENT-SECRET" --query "value" -o tsv)
OPENAI_API_KEY=$(az keyvault secret show --vault-name "$KV" --name "OPENAI-API-KEY" --query "value" -o tsv)
LANGCHAIN_API_KEY=$(az keyvault secret show --vault-name "$KV" --name "LANGCHAIN-API-KEY" --query "value" -o tsv)
```

### Get Configuration from App Config
```bash
MCP_URL=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__URL" --query "value" -o tsv)
MCP_AUTH_URL=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL" --query "value" -o tsv)
MCP_TRANSPORT=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "MCP_SERVERS__PAYCHEX__TRANSPORT" --query "value" -o tsv)
LANGCHAIN_TRACING=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_TRACING_V2" --query "value" -o tsv)
LANGCHAIN_EP=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_ENDPOINT" --query "value" -o tsv)
LANGCHAIN_PROJ=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LANGCHAIN_PROJECT" --query "value" -o tsv)
LLM_TYPE=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "LLM_TYPE" --query "value" -o tsv)
AZURE_ENDPOINT=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "AZURE_ENDPOINT" --query "value" -o tsv)
API_VERSION=$(az appconfig kv show --name "$APPCONFIG_NAME" --key "API_VERSION" --query "value" -o tsv)
```

## Step 3: Validate Configuration

Before deployment, validate all required variables are set:

```bash
echo "🔍 Validating required variables..."
for var in RG ACR IMAGE_REPO APP_AGENT ACA_ENV_AGENT_MCP MCP_CLIENT_ID MCP_CLIENT_SECRET OPENAI_API_KEY LANGCHAIN_API_KEY EVH_CONN MCP_URL MCP_AUTH_URL MCP_TRANSPORT LANGCHAIN_TRACING LANGCHAIN_EP LANGCHAIN_PROJ LLM_TYPE AZURE_ENDPOINT API_VERSION; do
  if [[ -z "${!var}" ]]; then
    echo "❌ Missing: $var"
  else
    echo "✅ $var: ${!var:0:20}..."
  fi
done
```

## Step 4: Deploy Container App

⚠️ **Important**: Run this after the Docker image is built and pushed to ACR.

```bash
az containerapp create \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --environment "$ACA_ENV_AGENT_MCP" \
  --image "$ACR.azurecr.io/$IMAGE_REPO:dev.latest" \
  --registry-server "$ACR.azurecr.io" \
  --registry-username "conpayrollsb001" \
  --registry-password "za/I5S0noGL4d0JMN/9zOvb3HZTpF+KGjw0LLhLa6K+ACRASLJH4" \
  --ingress external \
  --target-port 8000 \
  --cpu 2 \
  --memory 4Gi \
  --min-replicas 0 \
  --max-replicas 3 \
  --secrets \
    mcp-client-id="$MCP_CLIENT_ID" \
    mcp-client-secret="$MCP_CLIENT_SECRET" \
    openai-key="$OPENAI_API_KEY" \
    langchain-api-key="$LANGCHAIN_API_KEY" \
    evh-conn="$EVH_CONN" \
  --env-vars \
    MCP_CLIENT_ID=secretref:mcp-client-id \
    MCP_CLIENT_SECRET=secretref:mcp-client-secret \
    OPENAI_API_KEY=secretref:openai-key \
    LANGCHAIN_API_KEY=secretref:langchain-api-key \
    EH_CONN=secretref:evh-conn \
    MCP_SERVERS__PAYCHEX__URL="$MCP_URL" \
    MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="$MCP_AUTH_URL" \
    MCP_SERVERS__PAYCHEX__TRANSPORT="$MCP_TRANSPORT" \
    LANGCHAIN_TRACING_V2="$LANGCHAIN_TRACING" \
    LANGCHAIN_ENDPOINT="$LANGCHAIN_EP" \
    LANGCHAIN_PROJECT="$LANGCHAIN_PROJ" \
    LLM_TYPE="$LLM_TYPE" \
    AZURE_ENDPOINT="$AZURE_ENDPOINT" \
    API_VERSION="$API_VERSION"
```

💡 **Note**: The container can be created using the dev image and changed in the pipeline to the right image later.

## Step 5: Configure Authentication (Optional)

If you need to add Azure AD authentication to the container app:

```bash
# Authentication Configuration
APP_AUTH_NAME="app-agenticai-payroll-sandbox"  # Update for your app registration
AD_GROUP_NAME="azr_sg_sub_payroll_sandbox_001_bain_contrib"

# Get App Registration and Group IDs
APP_ID=$(az ad app list --display-name "$APP_AUTH_NAME" --query "[0].appId" -o tsv)
GROUP_ID=$(az ad group show --group "$AD_GROUP_NAME" --query "id" -o tsv)

# Use the same secret as other deployed containers
CLIENT_SECRET="your-client-secret-here"

# Configure Microsoft Authentication
az containerapp auth microsoft update \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --client-id "$APP_ID" \
  --client-secret "$CLIENT_SECRET" \
  --tenant-id "$(az account show --query tenantId -o tsv)"

# Set authentication behavior
az containerapp auth update \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --unauthenticated-client-action RedirectToLoginPage

# Get the callback URL for App Registration
APP_URL=$(az containerapp show \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --query "properties.configuration.ingress.fqdn" -o tsv)

echo "Add this URL to your App Registration Authentication settings:"
echo "https://$APP_URL/.auth/login/aad/callback"
```

## Step 6: Verify Deployment

After deployment, verify the container app is running:

```bash
# Check container app status
az containerapp show --name "$APP_AGENT" --resource-group "$RG" --query "properties.provisioningState" -o tsv

# Get the application URL
APP_URL=$(az containerapp show \
  --name "$APP_AGENT" \
  --resource-group "$RG" \
  --query "properties.configuration.ingress.fqdn" -o tsv)

echo "Application URL: https://$APP_URL"
```

</details>

## 🎯 Benefits of the New Pipeline

- **DRY Principle**: Single source of truth for deployment logic
- **Consistency**: Same deployment process across all environments
- **Automation**: No manual steps required
- **Environment Safety**: Automatic environment-specific configuration
- **Maintainability**: Easy to update deployment logic in one place
- **Traceability**: Full deployment history in GitHub Actions

## 📞 Support

For pipeline issues or questions:
1. Check GitHub Actions logs for detailed error messages
2. Verify Azure resource configurations
3. Ensure all prerequisites are met
4. Contact the DevOps team for infrastructure issues
