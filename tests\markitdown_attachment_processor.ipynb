{"cells": [{"cell_type": "markdown", "id": "c0bdfabe", "metadata": {}, "source": ["# MarkItDown Attachment Processor\n", "\n", "This notebook demonstrates using Microsoft's MarkItDown library for processing various file attachments and converting them to markdown format for LLM consumption."]}, {"cell_type": "code", "execution_count": 1, "id": "6802e631", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: markitdown[all] in /opt/homebrew/lib/python3.11/site-packages (0.1.2)\n", "Requirement already satisfied: beautifulsoup4 in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (4.12.3)\n", "Requirement already satisfied: charset-normalizer in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (3.4.0)\n", "Requirement already satisfied: defusedxml in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (0.7.1)\n", "Requirement already satisfied: magika~=0.6.1 in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (0.6.2)\n", "Requirement already satisfied: markdownify in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.2.0)\n", "Requirement already satisfied: requests in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (2.32.3)\n", "Requirement already satisfied: azure-ai-documentintelligence in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.0.2)\n", "Requirement already satisfied: azure-identity in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.24.0)\n", "Requirement already satisfied: lxml in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (4.9.3)\n", "Requirement already satisfied: mammoth in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.10.0)\n", "Requirement already satisfied: olefile in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (0.47)\n", "Requirement already satisfied: openpyxl in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (3.1.5)\n", "Requirement already satisfied: pandas in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (2.2.3)\n", "Requirement already satisfied: pdfminer-six in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (20221105)\n", "Requirement already satisfied: pydub in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (0.25.1)\n", "Requirement already satisfied: python-pptx in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.0.2)\n", "Requirement already satisfied: speechrecognition in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (3.14.3)\n", "Requirement already satisfied: xlrd in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (2.0.2)\n", "Requirement already satisfied: youtube-transcript-api~=1.0.0 in /opt/homebrew/lib/python3.11/site-packages (from markitdown[all]) (1.0.3)\n", "Requirement already satisfied: click>=8.1.7 in /opt/homebrew/lib/python3.11/site-packages (from magika~=0.6.1->markitdown[all]) (8.1.7)\n", "Requirement already satisfied: onnxruntime>=1.17.0 in /opt/homebrew/lib/python3.11/site-packages (from magika~=0.6.1->markitdown[all]) (1.22.1)\n", "Requirement already satisfied: numpy>=1.24 in /opt/homebrew/lib/python3.11/site-packages (from magika~=0.6.1->markitdown[all]) (1.26.4)\n", "Requirement already satisfied: python-dotenv>=1.0.1 in /opt/homebrew/lib/python3.11/site-packages (from magika~=0.6.1->markitdown[all]) (1.0.1)\n", "Requirement already satisfied: isodate>=0.6.1 in /opt/homebrew/lib/python3.11/site-packages (from azure-ai-documentintelligence->markitdown[all]) (0.6.1)\n", "Requirement already satisfied: azure-core>=1.30.0 in /opt/homebrew/lib/python3.11/site-packages (from azure-ai-documentintelligence->markitdown[all]) (1.31.0)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /Users/<USER>/Library/Python/3.11/lib/python/site-packages (from azure-ai-documentintelligence->markitdown[all]) (4.13.2)\n", "Requirement already satisfied: cryptography>=2.5 in /opt/homebrew/lib/python3.11/site-packages (from azure-identity->markitdown[all]) (40.0.1)\n", "Requirement already satisfied: msal>=1.30.0 in /opt/homebrew/lib/python3.11/site-packages (from azure-identity->markitdown[all]) (1.33.0)\n", "Requirement already satisfied: msal-extensions>=1.2.0 in /opt/homebrew/lib/python3.11/site-packages (from azure-identity->markitdown[all]) (1.3.1)\n", "Requirement already satisfied: soupsieve>1.2 in /opt/homebrew/lib/python3.11/site-packages (from beautifulsoup4->markitdown[all]) (2.6)\n", "Requirement already satisfied: cobble<0.2,>=0.1.3 in /opt/homebrew/lib/python3.11/site-packages (from mammoth->markitdown[all]) (0.1.4)\n", "Requirement already satisfied: six<2,>=1.15 in /Users/<USER>/Library/Python/3.11/lib/python/site-packages (from markdownify->markitdown[all]) (1.17.0)\n", "Requirement already satisfied: et-xmlfile in /opt/homebrew/lib/python3.11/site-packages (from openpyxl->markitdown[all]) (1.1.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Library/Python/3.11/lib/python/site-packages (from pandas->markitdown[all]) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /opt/homebrew/lib/python3.11/site-packages (from pandas->markitdown[all]) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /opt/homebrew/lib/python3.11/site-packages (from pandas->markitdown[all]) (2024.2)\n", "Requirement already satisfied: Pillow>=3.3.2 in /opt/homebrew/lib/python3.11/site-packages (from python-pptx->markitdown[all]) (11.0.0)\n", "Requirement already satisfied: XlsxWriter>=0.5.7 in /opt/homebrew/lib/python3.11/site-packages (from python-pptx->markitdown[all]) (3.2.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/homebrew/lib/python3.11/site-packages (from requests->markitdown[all]) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/homebrew/lib/python3.11/site-packages (from requests->markitdown[all]) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/homebrew/lib/python3.11/site-packages (from requests->markitdown[all]) (2024.8.30)\n", "Requirement already satisfied: cffi>=1.12 in /opt/homebrew/lib/python3.11/site-packages (from cryptography>=2.5->azure-identity->markitdown[all]) (1.17.1)\n", "Requirement already satisfied: PyJWT<3,>=1.0.0 in /opt/homebrew/lib/python3.11/site-packages (from PyJWT[crypto]<3,>=1.0.0->msal>=1.30.0->azure-identity->markitdown[all]) (2.6.0)\n", "Requirement already satisfied: coloredlogs in /opt/homebrew/lib/python3.11/site-packages (from onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (15.0.1)\n", "Requirement already satisfied: flatbuffers in /opt/homebrew/lib/python3.11/site-packages (from onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (25.2.10)\n", "Requirement already satisfied: packaging in /Users/<USER>/Library/Python/3.11/lib/python/site-packages (from onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (25.0)\n", "Requirement already satisfied: protobuf in /opt/homebrew/lib/python3.11/site-packages (from onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (5.28.2)\n", "Requirement already satisfied: sympy in /opt/homebrew/lib/python3.11/site-packages (from onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (1.13.1)\n", "Requirement already satisfied: pycparser in /opt/homebrew/lib/python3.11/site-packages (from cffi>=1.12->cryptography>=2.5->azure-identity->markitdown[all]) (2.22)\n", "Requirement already satisfied: humanfriendly>=9.1 in /opt/homebrew/lib/python3.11/site-packages (from coloredlogs->onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (10.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /opt/homebrew/lib/python3.11/site-packages (from sympy->onnxruntime>=1.17.0->magika~=0.6.1->markitdown[all]) (1.3.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpython3.11 -m pip install --upgrade pip\u001b[0m\n"]}], "source": ["# Install MarkItDown with all optional dependencies\n", "!pip install 'markitdown[all]'"]}, {"cell_type": "code", "execution_count": 2, "id": "4e2961e5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ MarkItDown imported successfully\n", "✅ OpenAI available for enhanced image processing\n", "📦 Setup complete!\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import time\n", "from pathlib import Path\n", "from typing import Optional, Dict, Any, List\n", "\n", "# Import MarkItDown\n", "try:\n", "    from markitdown import MarkItDown\n", "    print(\"✅ MarkItDown imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Failed to import MarkItDown: {e}\")\n", "    print(\"   Try installing with: pip install 'markitdown[all]'\")\n", "    MarkItDown = None\n", "\n", "# Optional: Import OpenAI for enhanced image processing\n", "try:\n", "    from openai import OpenAI\n", "    print(\"✅ OpenAI available for enhanced image processing\")\n", "    HAS_OPENAI = True\n", "except ImportError:\n", "    print(\"ℹ️ OpenAI not available - MarkItDown will use basic OCR\")\n", "    OpenAI = None\n", "    HAS_OPENAI = False\n", "\n", "print(\"📦 Setup complete!\")"]}, {"cell_type": "markdown", "id": "13c059dc", "metadata": {}, "source": ["## Initialize MarkItDown\n", "\n", "MarkItDown can be configured with or without OpenAI integration for enhanced image processing."]}, {"cell_type": "code", "execution_count": 3, "id": "942204b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ MarkItDown initialized with OpenAI integration (gpt-4o)\n", "   Enhanced image processing and OCR available\n"]}], "source": ["def setup_markitdown(use_openai: bool = True) -> Optional[MarkItDown]:\n", "    \"\"\"\n", "    Initialize MarkItDown with optional OpenAI integration\n", "    \n", "    Args:\n", "        use_openai: Whether to use OpenAI for enhanced image processing\n", "    \n", "    Returns:\n", "        Configured MarkItDown instance or None if failed\n", "    \"\"\"\n", "    if not MarkItDown:\n", "        print(\"❌ MarkItDown not available\")\n", "        return None\n", "    \n", "    try:\n", "        if use_openai and HAS_OPENAI:\n", "            # Try to initialize with OpenAI integration\n", "            try:\n", "                client = OpenAI()  # Uses OPENAI_API_KEY from environment\n", "                md = MarkItDown(\n", "                    llm_client=client, \n", "                    llm_model=\"gpt-4o\"  # Best model for document understanding\n", "                )\n", "                print(\"✅ MarkItDown initialized with OpenAI integration (gpt-4o)\")\n", "                print(\"   Enhanced image processing and OCR available\")\n", "                return md\n", "            except Exception as e:\n", "                print(f\"⚠️ OpenAI integration failed: {e}\")\n", "                print(\"   Falling back to basic MarkItDown (still very capable!)\")\n", "        \n", "        # Initialize without OpenAI integration\n", "        md = MarkItDown()\n", "        print(\"✅ MarkItDown initialized (basic mode)\")\n", "        print(\"   Built-in OCR and document processing available\")\n", "        return md\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Failed to initialize MarkItDown: {e}\")\n", "        return None\n", "\n", "# Initialize MarkItDown\n", "markitdown = setup_markitdown(use_openai=True)"]}, {"cell_type": "markdown", "id": "8ebe21a2", "metadata": {}, "source": ["## Core Processing Function\n", "\n", "Simple, clean function for processing any supported file type with MarkItDown."]}, {"cell_type": "code", "execution_count": 4, "id": "10939383", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛠️ Processing functions ready!\n"]}], "source": ["def process_file_with_markitdown(\n", "    file_path: str,\n", "    markitdown_instance: Optional[MarkItDown] = None\n", ") -> Dict[str, Any]:\n", "    \"\"\"\n", "    Process any supported file with MarkItDown\n", "    \n", "    Args:\n", "        file_path: Path to the file to process\n", "        markitdown_instance: MarkItDown instance to use\n", "    \n", "    Returns:\n", "        Dictionary with processing results and metadata\n", "    \"\"\"\n", "    # Use global instance if none provided\n", "    if markitdown_instance is None:\n", "        markitdown_instance = markitdown\n", "    \n", "    result = {\n", "        \"file_path\": file_path,\n", "        \"file_name\": os.path.basename(file_path),\n", "        \"file_size\": 0,\n", "        \"file_type\": \"\",\n", "        \"success\": <PERSON><PERSON><PERSON>,\n", "        \"markdown_content\": None,\n", "        \"error\": None,\n", "        \"processing_time\": 0,\n", "        \"content_length\": 0\n", "    }\n", "    \n", "    # Check if file exists\n", "    if not os.path.exists(file_path):\n", "        result[\"error\"] = \"File not found\"\n", "        return result\n", "    \n", "    # Get file info\n", "    result[\"file_size\"] = os.path.getsize(file_path)\n", "    result[\"file_type\"] = Path(file_path).suffix.lower()\n", "    \n", "    # Check if MarkItDown is available\n", "    if not markitdown_instance:\n", "        result[\"error\"] = \"MarkItDown not initialized\"\n", "        return result\n", "    \n", "    print(f\"📄 Processing: {result['file_name']} ({result['file_type']})\")\n", "    print(f\"📏 File size: {result['file_size']:,} bytes\")\n", "    \n", "    try:\n", "        start_time = time.time()\n", "        \n", "        # Process the file with MarkItDown\n", "        conversion_result = markitdown_instance.convert(file_path)\n", "        \n", "        processing_time = time.time() - start_time\n", "        result[\"processing_time\"] = processing_time\n", "        \n", "        if conversion_result and conversion_result.text_content:\n", "            result[\"markdown_content\"] = conversion_result.text_content\n", "            result[\"content_length\"] = len(conversion_result.text_content)\n", "            result[\"success\"] = True\n", "            \n", "            print(f\"✅ Processing successful!\")\n", "            print(f\"⏱️ Processing time: {processing_time:.2f} seconds\")\n", "            print(f\"📝 Content length: {result['content_length']:,} characters\")\n", "            \n", "        else:\n", "            result[\"error\"] = \"MarkItDown returned empty result\"\n", "            print(f\"❌ Processing failed: Empty result\")\n", "            \n", "    except Exception as e:\n", "        result[\"error\"] = str(e)\n", "        result[\"processing_time\"] = time.time() - start_time\n", "        print(f\"❌ Processing failed: {e}\")\n", "    \n", "    return result\n", "\n", "def display_result_preview(result: Dict[str, Any], max_chars: int = 1000) -> None:\n", "    \"\"\"\n", "    Display a preview of the processing result\n", "    \n", "    Args:\n", "        result: Result dictionary from process_file_with_markitdown\n", "        max_chars: Maximum characters to show in preview\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(f\"📋 PROCESSING RESULT: {result['file_name']}\")\n", "    print(\"=\"*60)\n", "    print(f\"Success: {result['success']}\")\n", "    print(f\"File Type: {result['file_type']}\")\n", "    print(f\"File Size: {result['file_size']:,} bytes\")\n", "    print(f\"Processing Time: {result['processing_time']:.2f} seconds\")\n", "    \n", "    if result['success']:\n", "        print(f\"Content Length: {result['content_length']:,} characters\")\n", "        print(\"\\n📝 MARKDOWN PREVIEW:\")\n", "        print(\"-\" * 40)\n", "        \n", "        content = result['markdown_content']\n", "        if len(content) > max_chars:\n", "            print(content[:max_chars])\n", "            print(f\"\\n... (truncated, showing first {max_chars} of {len(content)} characters)\")\n", "        else:\n", "            print(content)\n", "    else:\n", "        print(f\"Error: {result['error']}\")\n", "    \n", "    print(\"=\"*60)\n", "\n", "print(\"🛠️ Processing functions ready!\")"]}, {"cell_type": "markdown", "id": "e54fcf78", "metadata": {}, "source": ["## Test with Excel File\n", "\n", "Test MarkItDown with your payroll Excel file."]}, {"cell_type": "code", "execution_count": 5, "id": "aa1b3231", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Testing MarkItDown with Excel file...\n", "📄 Processing: attachment_example.xlsx (.xlsx)\n", "📏 File size: 32,883 bytes\n", "✅ Processing successful!\n", "⏱️ Processing time: 0.06 seconds\n", "📝 Content length: 381 characters\n", "\n", "============================================================\n", "📋 PROCESSING RESULT: attachment_example.xlsx\n", "============================================================\n", "Success: True\n", "File Type: .xlsx\n", "File Size: 32,883 bytes\n", "Processing Time: 0.06 seconds\n", "Content Length: 381 characters\n", "\n", "📝 MARKDOWN PREVIEW:\n", "----------------------------------------\n", "## SPI Client Template\n", "| Unnamed: 0 | Unnamed: 1 | Unnamed: 2 | Unnamed: 3 | Unnamed: 4 | Unnamed: 5 | Unnamed: 6 |\n", "| --- | --- | --- | --- | --- | --- | --- |\n", "| Name: Bubly test llc\\n | NaN | NaN | NaN | NaN | NaN | NaN |\n", "| Company ID | Worker ID | Employee Name | Pay Component | Hours | Rate | Amount |\n", "| TT1478TT | TT4UWBZQL0TWNNTZECTT | <PERSON><PERSON> | Hours | 69 | NaN | NaN |\n", "============================================================\n"]}], "source": ["# Path to your test Excel file\n", "excel_file_path = \"/Users/<USER>/Library/CloudStorage/OneDrive-Bain/Projects/paychex/payroll-email-agent/tests/attachment_example.xlsx\"\n", "\n", "# Test processing\n", "if os.path.exists(excel_file_path):\n", "    print(f\"🔄 Testing MarkItDown with Excel file...\")\n", "    \n", "    excel_result = process_file_with_markitdown(excel_file_path)\n", "    display_result_preview(excel_result)\n", "    \n", "else:\n", "    print(f\"❌ Excel file not found at: {excel_file_path}\")\n", "    print(\"Please update the path to point to your Excel file.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "52bf3285", "metadata": {}, "outputs": [], "source": ["test_case =   {\n", "    \"displayId\": \"TT1478TT\",\n", "    \"comment\": {\n", "      \"uid\": \"1\",\n", "      \"id\": \"1000\",\n", "      \"timestamp\": \"2025-04-10T15:43:30.560000\",\n", "      \"user_properties\": {\n", "        \"string:Sender\": \"<EMAIL>\",\n", "        \"string:Sender Domain\": \"gmail.com\"\n", "      },\n", "      \"messages\": [\n", "        {\n", "          \"from\": \"<EMAIL>\",\n", "          \"to\": [\"<EMAIL>\"],\n", "          \"body\": { \"text\": \"Good morning We are just reporting hours for Bubly test llc Elena 38 Lejla  44 horus Irina 24 vacation hours Regular hours 19.75 total of 43.75 Thank you Please let us know you got this email. -- HR Team of Bubly Test LLC 123 Abc Rd Universe WA 00000\" },\n", "          \"subject\": { \"text\": \"Bubly test staff payroll\" }\n", "        }\n", "      ]\n", "    }\n", "  }"]}, {"cell_type": "code", "execution_count": 7, "id": "e0550b25", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Testing attachment integration with test case...\n", "📎 Processing attachment: attachment_example.xlsx\n", "📄 Processing: attachment_example.xlsx (.xlsx)\n", "📏 File size: 32,883 bytes\n", "✅ Processing successful!\n", "⏱️ Processing time: 0.02 seconds\n", "📝 Content length: 381 characters\n", "✅ Successfully added attachment content to email body\n", "   Original body length: 250 characters\n", "   Updated body length: 748 characters\n", "   Added content: 498 characters\n", "\n", "📧 UPDATED EMAIL BODY (first 1000 characters):\n", "============================================================\n", "Good morning We are just reporting hours for Bubly test llc Elena 38 Lejla  44 horus Irina 24 vacation hours Regular hours 19.75 total of 43.75 Thank you Please let us know you got this email. -- HR Team of Bubly Test LLC 123 Abc Rd Universe WA 00000\n", "\n", "---\n", "\n", "**📎 ATTACHMENT: attachment_example.xlsx**\n", "*File Type: .xlsx | Size: 32,883 bytes | Processed in 0.02s*\n", "\n", "## SPI Client Template\n", "| Unnamed: 0 | Unnamed: 1 | Unnamed: 2 | Unnamed: 3 | Unnamed: 4 | Unnamed: 5 | Unnamed: 6 |\n", "| --- | --- | --- | --- | --- | --- | --- |\n", "| Name: Bubly test llc\\n | NaN | NaN | NaN | NaN | NaN | NaN |\n", "| Company ID | Worker ID | Employee Name | Pay Component | Hours | Rate | Amount |\n", "| TT1478TT | TT4UWBZQL0TWNNTZECTT | <PERSON><PERSON> | Hours | 69 | NaN | NaN |\n", "\n", "---\n", "\n", "============================================================\n"]}], "source": ["def add_attachment_to_test_case(\n", "    test_case: Dict[str, Any], \n", "    attachment_path: str,\n", "    markitdown_instance: Optional[MarkItDown] = None\n", ") -> Dict[str, Any]:\n", "    \"\"\"\n", "    Process an attachment with MarkItDown and add the result to the test case email body\n", "    \n", "    Args:\n", "        test_case: The test case dictionary with email structure\n", "        attachment_path: Path to the attachment file to process\n", "        markitdown_instance: MarkItDown instance to use\n", "    \n", "    Returns:\n", "        Updated test case with attachment content appended to email body\n", "    \"\"\"\n", "    # Create a copy of the test case to avoid modifying the original\n", "    updated_test_case = test_case.copy()\n", "    \n", "    # Process the attachment\n", "    print(f\"📎 Processing attachment: {os.path.basename(attachment_path)}\")\n", "    result = process_file_with_markitdown(attachment_path, markitdown_instance)\n", "    \n", "    if result['success']:\n", "        # Get the original email body text\n", "        original_body = updated_test_case[\"comment\"][\"messages\"][0][\"body\"][\"text\"]\n", "        \n", "        # Create attachment section\n", "        attachment_section = f\"\"\"\n", "\n", "---\n", "\n", "**📎 ATTACHMENT: {result['file_name']}**\n", "*File Type: {result['file_type']} | Size: {result['file_size']:,} bytes | Processed in {result['processing_time']:.2f}s*\n", "\n", "{result['markdown_content']}\n", "\n", "---\n", "\"\"\"\n", "        \n", "        # Append the attachment content to the email body\n", "        updated_body = original_body + attachment_section\n", "        updated_test_case[\"comment\"][\"messages\"][0][\"body\"][\"text\"] = updated_body\n", "        \n", "        print(f\"✅ Successfully added attachment content to email body\")\n", "        print(f\"   Original body length: {len(original_body):,} characters\")\n", "        print(f\"   Updated body length: {len(updated_body):,} characters\")\n", "        print(f\"   Added content: {len(attachment_section):,} characters\")\n", "        \n", "    else:\n", "        print(f\"❌ Failed to process attachment: {result['error']}\")\n", "        \n", "        # Add error note to email body\n", "        original_body = updated_test_case[\"comment\"][\"messages\"][0][\"body\"][\"text\"]\n", "        error_section = f\"\"\"\n", "\n", "---\n", "\n", "**⚠️ ATTACHMENT PROCESSING ERROR: {os.path.basename(attachment_path)}**\n", "*Error: {result['error']}*\n", "\n", "---\n", "\"\"\"\n", "        updated_body = original_body + error_section\n", "        updated_test_case[\"comment\"][\"messages\"][0][\"body\"][\"text\"] = updated_body\n", "    \n", "    return updated_test_case\n", "\n", "# Test the function with your Excel file\n", "if os.path.exists(excel_file_path) and markitdown:\n", "    print(\"🔄 Testing attachment integration with test case...\")\n", "    \n", "    # Process attachment and add to test case\n", "    updated_test_case = add_attachment_to_test_case(\n", "        test_case, \n", "        excel_file_path, \n", "        markitdown\n", "    )\n", "    \n", "    # Display the updated email body (first 1000 chars)\n", "    updated_body = updated_test_case[\"comment\"][\"messages\"][0][\"body\"][\"text\"]\n", "    print(f\"\\n📧 UPDATED EMAIL BODY (first 1000 characters):\")\n", "    print(\"=\"*60)\n", "    print(updated_body[:1000])\n", "    if len(updated_body) > 1000:\n", "        print(f\"\\n... (truncated, total length: {len(updated_body):,} characters)\")\n", "    print(\"=\"*60)\n", "    \n", "else:\n", "    print(\"⚠️ Cannot test attachment integration:\")\n", "    if not os.path.exists(excel_file_path):\n", "        print(f\"   - Excel file not found: {excel_file_path}\")\n", "    if not markitdown:\n", "        print(\"   - MarkItDown not initialized\")"]}, {"cell_type": "code", "execution_count": 8, "id": "9e0bd8c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'displayId': 'TT1478TT',\n", " 'comment': {'uid': '1',\n", "  'id': '1000',\n", "  'timestamp': '2025-04-10T15:43:30.560000',\n", "  'user_properties': {'string:Sender': '<EMAIL>',\n", "   'string:Sender Domain': 'gmail.com'},\n", "  'messages': [{'from': '<EMAIL>',\n", "    'to': ['<EMAIL>'],\n", "    'body': {'text': 'Good morning We are just reporting hours for Bubly test llc <PERSON> 38 <PERSON>jla  44 horus Irina 24 vacation hours Regular hours 19.75 total of 43.75 Thank you Please let us know you got this email. -- HR Team of Bubly Test LLC 123 Abc Rd Universe WA 00000\\n\\n---\\n\\n**📎 ATTACHMENT: attachment_example.xlsx**\\n*File Type: .xlsx | Size: 32,883 bytes | Processed in 0.02s*\\n\\n## SPI Client Template\\n| Unnamed: 0 | Unnamed: 1 | Unnamed: 2 | Unnamed: 3 | Unnamed: 4 | Unnamed: 5 | Unnamed: 6 |\\n| --- | --- | --- | --- | --- | --- | --- |\\n| Name: Bubly test llc\\\\n | NaN | NaN | NaN | NaN | NaN | NaN |\\n| Company ID | Worker ID | Employee Name | Pay Component | Hours | Rate | Amount |\\n| TT1478TT | TT4UWBZQL0TWNNTZECTT | <PERSON><PERSON> | Hours | 69 | NaN | NaN |\\n\\n---\\n'},\n", "    'subject': {'text': 'Bubly test staff payroll'}}]}}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["updated_test_case "]}, {"cell_type": "code", "execution_count": null, "id": "facd4732", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}