# Ground Truth Evaluation Setup Guide

This guide walks through the process of setting up and running ground truth evaluations for the payroll email processing system.

## Overview

The ground truth evaluation process consists of three main steps:
1. **Format GT Test Cases**: Convert the Excel ground truth file into the JSON structure needed for API testing
2. **Run Evaluations**: Execute the formatted test cases against the process-email endpoint
3. **Evaluate results** Execute the GT evaluation script to collect evaluation numbers

## Prerequisites

- Access to the Paychex SharePoint site
- Jupyter notebook environment set up
- Repository cloned locally
- Payroll agent API server running on localhost:8000

## Step 1: Download Latest Ground Truth Data

1. Navigate to the SharePoint location:
[Latest GT](https://paychex-my.sharepoint.com/:x:/r/personal/sjuel_paychex_com/_layouts/15/Doc.aspx?sourcedoc=%7B95E3F005-184C-4372-81D2-03C4ABC82916%7D&file=Payroll_processing_golden_set.xlsx&action=default&mobileredirect=true)


2. Download the `Payroll_processing_golden_set.xlsx` file

3. Save the file to the repository's evaluation directory: `app/payroll_agent/evaluation/ground_truth/{date}_Payroll_processing_golden_set.xlsx`

## Step 2: Format Ground Truth Test Cases

1. Open the Jupyter notebook: `app/payroll_agent/evaluation/format_GT_test_cases.ipynb`


2. Update the variables in the notebook configuration cell to match your downloaded file:

    ```python
    base_path = r'C:\Users\<USER>\payroll-email-agent\app\payroll_agent\evaluation\ground_truth' # Update to your base path
    input_path = r"\{date}_Payroll_processing_golden_set.xlsx"  # Update with your date
    output_file = r'\{date}_Payroll_processing_golden_set.json'  # Update with your date
    ```

3. Run all cells in the notebook to:
    - Load the Excel ground truth data
    - Transform it into the JSON structure required by the process-email endpoint
    - Export the formatted test cases

4. The notebook will generate a JSON file containing all test cases in the proper format for API evaluation

## Step 3: Run Ground Truth Evaluations

1. **Start the Payroll Agent API Server**
   - Ensure your payroll agent is running on `http://localhost:8000`
   - Verify the `/api/v1/process-email` endpoint is accessible

2. **Open the Evaluation Notebook**: `app/payroll_agent/evaluation/eval_notebook_GT.ipynb`

3. **Configure the evaluation variables** to match your formatted JSON file:

    ```python
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    base_path = r'C:\Users\<USER>\payroll-email-agent\app\payroll_agent\evaluation\ground_truth'  # Update to your base path
    input_file = r'\{date}_Payroll_processing_golden_set.json'  # Update with your date
    output_file = rf'\{timestamp}_Payroll_processing_golden_set_results.json'
    ```

4. Configure API endpoint settings (if different from defaults):
    ```python
    # Config
    HOST = "http://localhost:8000"
    API_PREFIX = "/api/v1"
    ROUTE = "/process-email"
    ENDPOINT_URL = f"{HOST}{API_PREFIX}{ROUTE}"
    ```

5. Run the evaluation:
    - Execute all cells in the notebook
    - The notebook will load test cases and run them through the payroll processing engine
    - Progress will be shown via a progress bar
    - Results will be saved automatically with timestamp

6. Review Results:
    - Check for any failed cases in the failed variable
    - Results are saved to: {timestamp}_Payroll_processing_golden_set_results.json
    - Each result contains:
        - `New GT ID`: Test case identifier
        - `input`: Original test case data
        - `output`: API response from the payroll agent

- Output: The evaluation generates a JSON file with detailed results for each test case:
    ```python
    [
    {
        "New GT ID": "test_case_id",
        "input": { /* original test case */ },
        "output": { /* API response with processing results */ }
    }
    ]
    ```

## Notes

- Ensure the SharePoint link is accessible and you have the necessary permissions
- The Excel file structure should match the expected format in the notebook
- Make sure the API server is running before starting evaluations
- Update the date in `input_file` variable to match your formatted JSON file
- Results are automatically timestamped to avoid overwriting previous evaluations

## Troubleshooting

**API Connection Issues:**
- Verify the payroll agent is running on localhost:8000
- Check that the `/api/v1/process-email` endpoint responds
- Ensure no firewall is blocking local connections

**File Path Issues:**
- Verify all file paths use the correct date format
- Check that the formatted JSON file exists in the ground_truth directory
- Ensure write permissions for the output directory

**Evaluation Failures:**
- Check the `failed` variable for cases that didn't complete
- Review API timeout settings if processing takes too long
- Verify the JSON structure matches expected input format

## Step 4: Analyze Results with GT Evaluation Utilities

After running the ground truth through the email agent and generating results, the next step is to analyze and compare the outputs using the dedicated evaluation utilities.

### Overview

The evaluation analysis is performed using a separate repository `payroll-email-agent-utilities` which contains specialized tools for:
- Comparing actual outputs against expected ground truth results
- Generating evaluation metrics and performance scores
- Creating detailed analysis reports
- Identifying patterns in failures and successes

### Prerequisites for Analysis

    - Completed Step 3 with generated results file: `{timestamp}_Payroll_processing_golden_set_results.json`
    - Access to the `payroll-email-agent-utilities` repository
    - Python environment set up for the utilities repository

1. Move key files to analysis folder
   - base_path + `{timestamp}_Payroll_processing_golden_set_results.json` -> 


2. Run the evaluation script 
    - Update the files paths to the current paths
    ```json
        file_path = "data/test_results/{date}_Payroll_processing_golden_set_results.json"
        gt_file_path = "data/source/{date}_Payroll_processing_golden_set.xlsx"
        ```
    -  ```bash
        python common/evals/run_gt_evaluation.py
        ```


3. Evaluate results
    - Files created: 
    ```bash
    Email-level results saved to: data/output/evaluation_results_{timestamp}/test_results_extracted_fields_emails.xlsx
    Pay components results saved to: data/output/evaluation_results_{timestamp}/test_results_extracted_paycomponents.xlsx
    Email evaluation results saved to: data/output/evaluation_results_{timestamp}/test_results_emails_evaluation.xlsx
    Paycomponent evaluation results saved to: data/output/evaluation_results_{timestamp}/test_results_paycomponents_evaluation.xlsx
    Analytics table saved to: data/output/evaluation_results_{timestamp}/email_evaluation_analytics.xlsx
    Pivoted analytics table saved to: data/output/evaluation_results_{timestamp}/email_evaluation_analytics_pivoted.xlsx
    Worker-level payroll success results saved to: data/output/evaluation_results_{timestamp}/payroll_success_worker_level.xlsx
    GT-level payroll success summary saved to: data/output/evaluation_results_{timestamp}/payroll_success_gt_summary.xlsx
    ```

    Evaluation process currently includes: 
    - Review high level stats in `email_evaluation_analytics.xlsx`
    - Review component level metrics in `test_results_paycomponents_evaluation.xlsx`. Create a pivot table with: 
       - Filters:EARNINGS_CLASSIFICATION_TYPE: Regular (Main type we are evaluating), Blank in Termination Graph, Non blank in Email Content
       - Rows: NEW_GT_ID
        - Columns: paycomponent_match 
        - Count: NEW_GT_ID
        - Count how many have 100% of workers correct
    Also filter the main sheet to the same filters + paycomponent_match = False and evaluate if the determination of paycomponent_match is correct. The current method is not 100% always

    See example of how this was analyzed [here](https://paychex-my.sharepoint.com/personal/lamontoya_paychex_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Flamontoya%5Fpaychex%5Fcom%2FDocuments%2FDocuments%2FRepos%2Fpayroll%2Demail%2Dagent%2Dutilities%2Fcommon%2Fevals%2Fdata%2Foutput%2Fevaluation%5Fresults%5F20250901%5F224636&ga=1)


4. Report results in Miro

    As a final step we have been tracking progress in Miro. 

    This should be updated using the evaluation files or alternatively evaluation should be tracked in a new format
    ![GT Evaluation Tracking](img/miro-gt-emals.png)


## Additional details on evaluation script

The script performs a comprehensive evaluation process in 4 main steps:

### 1. **Email-Level Field Extraction (`extract_specific_fields_emails`)**
Extracts structured data from test results JSON files containing AI agent responses for payroll processing emails.

**Key Fields Extracted:**
- **Basic Information**: GT ID, Agent Version, Display ID, Company ID, Timestamp, Email Content
- **Classification Results**: 
  - Intent triage (work-related flags, payroll flags, continue flags)
  - Business knockout metadata and pass/fail flags
  - Technical knockout metadata and pass/fail flags
- **Worker Lookup Results**: Search success, number of workers matched, pay period ID, continue flags
- **Payroll Processing Results**: Agent matching status, number of pay components found, continue flags
- **Validation Results**: Continue flags from validation stage
- **Termination Information**: Termination graph, reason, and specific node where processing ended
- **Performance Data**: Runtime statistics

### 2. **Pay Component Extraction (`extract_paycomponents`)**
Extracts individual pay components from the `processed_payrolls` section of AI agent responses.

**Component Data Extracted:**
- Worker ID and name information
- Pay amounts, hours, and rates
- Pay types and classifications
- Links each component back to its source GT ID for evaluation

### 3. **Email-Level Evaluation (`evaluate_emails`)**
Compares extracted email data against ground truth from Excel file to assess AI agent accuracy.

**Evaluation Criteria:**
- **Display ID Matching**: Exact string comparison between AI extracted and ground truth
- **Pay Period Matching**: Handles float/scientific notation conversion for proper comparison
- **Knockout Flag Analysis**: 
  - Validates if AI correctly identified emails that should be knocked out
  - Cross-references GT knockout flag (1=should knockout) with AI continue flag (False=knocked out)
- **Knockout Event Detection**: Checks if AI detected specific knockout events mentioned in ground truth
- **Worker Count Accuracy**: Compares number of workers found by AI vs expected count in GT
  - Handles comma-separated worker names in GT data
  - Converts worker lists to counts for comparison

### 4. **Pay Component Evaluation (`evaluate_paycomponents`)**
Performs sophisticated fuzzy matching between AI-found components and ground truth components.

**Matching Algorithm:**
- **Worker-Level Grouping**: Groups components by GT ID and Worker ID for proper comparison
- **Fuzzy Score Calculation** (`calculate_component_match_score`):
  - **Amount matching (40% weight)**: $1 tolerance for exact match, partial credit within 10-20%
  - **Hours matching (30% weight)**: 0.1 hour tolerance for exact match, partial credit for close values  
  - **Rate matching (30% weight)**: $0.10 tolerance for exact match, partial credit for close values
- **Optimal Matching** (`find_optimal_paycomponent_matches`):
  - Calculates match scores for all GT-AI component combinations
  - Uses greedy algorithm to assign best matches while ensuring each component is matched only once
  - AI-centric approach: each AI component gets its best available GT match
- **Match Evaluation**:
  - Amount match: Within $1 tolerance
  - Hours+Rate match: Both within tolerance, or hours-only for $0 earnings
  - Overall component match: Either amount match OR hours+rate match is sufficient
  - Special handling for $0 earnings (treated as successful matches)

**Advanced Features:**
- **Data Type Handling**: Robust conversion of mixed data types (strings, floats, NaN values)
- **Edge Case Management**: Handles missing data, empty components, and zero-value earnings
- **Comprehensive Statistics**: Provides detailed accuracy percentages and match distributions
- **Cross-Reference Integration**: Merges email-level data with component-level results for complete analysis

The evaluation framework provides multi-dimensional assessment covering both high-level email processing accuracy and detailed component-level precision, enabling comprehensive performance analysis of the AI payroll processing