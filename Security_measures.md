Security Measures in the AI agent
=================

The following security measures are implemented in the AI agent. Every email processed by the agent must pass through all these security checks before payroll is submitted into the system.

## 1. Content Filtering

All incoming emails are scrutinized for malicious content and prompt injection. If such content is detected, the email will be rejected and redirected to Upstream for human processing.
A. Hate and Fairness
B. Sexual
C. Violence
D. Self-Harm
E. User Prompt Attacks (Prompt Injection)

In addition to general content filtering, the agent also detects payroll-specific spam and fraudulent content.

## 2. Email Knock-out

The agent checks emails against pre-defined knock-out rules. If any knock-out situation is triggered, the email is rejected and sent to Upstream for human processing.

It's important to note that knock-out filtration occurs twice: first before processing the email content, and again after payroll data is formulated.

## 3. Account Validation

The AI agent receives account authentication/authorization results from RPA in the input payload. It validates this information by comparing the email sender, domain, and content with account information retrieved from the Core Advance system. Processing continues only when the RPA-suggested account matches the Core Advance system information.

## 4. Worker Validation

The AI agent validates all workers mentioned in the email to ensure each one is identified under the account—no unauthorized individuals are permitted. If any worker cannot be found in the account, the entire email is rejected and sent to Upstream for human processing.

## 5. Payroll Validation

After payroll data is formulated, a dedicated validation agent examines the data at the worker level, comparing it with the worker's previous three pay periods. The email will be rejected and sent to Upstream for human processing if any amount:
A. Is ≥ $5000
B. Exceeds the maximum amount in the prior three pay periods by ≥ xx% (to be determined by Service Ops team)
C. Shows an overridden rate that exceeds the default system rate by ≥ xx% (to be determined by Service Ops team)

## 6. CA System Feedback

If the Core Advance system rejects the payroll submission or returns any errors during submission, the agent stops processing and redirects the email to Upstream for human processing.

## 7. Journal Notes

The AI agent reviews journal notes at both account and worker levels. If an email contains information that conflicts with these notes, the email is rejected and sent to Upstream for human processing.