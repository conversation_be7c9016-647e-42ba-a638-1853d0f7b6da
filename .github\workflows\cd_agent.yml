name: Build and Deploy Agents

on:
  push:
    branches: [dev]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to dev HEAD)'
        required: false
        default: dev

env:
  REGISTRY_LOGIN_SERVER: ${{vars.LANGGRAPH_SB_REGISTRY_SERVER}}
  REGISTRY_USERNAME: ${{secrets.LANGGRAPH_SB_REGISTRY_SERVER_USERNAME}}
  REGISTRY_PASSWORD: ${{secrets.LANGGRAPH_SB_REGISTRY_SERVER_PASSWORD}}
  LANGGRAPH_CLOUD_LICENSE_KEY: ${{ secrets.LANGGRAPH_CLOUD_LICENSE_KEY }}
  LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
  ACTIONS_STEP_DEBUG: true
  NODE_EXTRA_CA_CERTS: /etc/ssl/certs/ca-certificates.crt
  LLM_TYPE: ${{ vars.LLM_TYPE }}
  API_VERSION: ${{ vars.API_VERSION }}
  AZURE_ENDPOINT: ${{ vars.AZURE_ENDPOINT }}
  MCP_SERVERS__PAYCHEX__URL: ${{ vars.MCP_SERVERS__PAYCHEX__URL }}
  MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL: ${{ vars.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL }}
  MCP_SERVERS__PAYCHEX__TRANSPORT: ${{ vars.MCP_SERVERS__PAYCHEX__TRANSPORT }}
  MCP_SERVERS__PAYCHEX__CLIENT_ID: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_ID }}
  MCP_SERVERS__PAYCHEX__CLIENT_SECRET: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_SECRET }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}


jobs:
  build-scan-push-image:
    uses: paychex/shared-workflows-aipe/.github/workflows/langgraph_image_build_push.yml@main
    with:
      runs_on: paychex-ai-platform-arc-sandbox
      image_name: payroll-email-agent
      image_tag: ${{ github.sha }}
      registry_login_server: ${{vars.LANGGRAPH_SB_REGISTRY_SERVER}}
    secrets:
      registry_username: ${{ secrets.LANGGRAPH_SB_REGISTRY_SERVER_USERNAME }}
      registry_password: ${{ secrets.LANGGRAPH_SB_REGISTRY_SERVER_PASSWORD }}
      wiz_client_id: ${{ secrets.WIZ_CLIENT_ID }}
      wiz_client_secret: ${{ secrets.WIZ_CLIENT_SECRET }}

  deploy-agent:
    needs: build-scan-push-image
    runs-on:
      group: paychex-ai-platform-arc-sandbox
    environment: dev

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Deploy Agent
        id: deploy-agent
        uses: paychex/actions-langgraph-deployment-action@v1
        with:
          langsmith_api_key: ${{ env.LANGSMITH_API_KEY }}
          project_name: payroll-email-agent
          environment: sandbox
          image_name: payroll-email-agent
          image_tag: ${{ github.sha }}
          env_vars: |
            - name: REQUESTS_CA_BUNDLE
              value: /etc/ssl/certs/ca-certificates.crt
              type: default
            - name: LLM_TYPE
              value: ${{ env.LLM_TYPE }}
              type: default
            - name: API_VERSION
              value: ${{ env.API_VERSION }}
              type: default
            - name: AZURE_ENDPOINT
              value: ${{ env.AZURE_ENDPOINT }}
              type: default
            - name: MCP_SERVERS__PAYCHEX__URL
              value: ${{ env.MCP_SERVERS__PAYCHEX__URL }}
              type: default
            - name: MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL
              value: ${{ env.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL }}
              type: default
            - name: MCP_SERVERS__PAYCHEX__TRANSPORT
              value: ${{ env.MCP_SERVERS__PAYCHEX__TRANSPORT }}
              type: default
            - name: MCP_SERVERS__PAYCHEX__CLIENT_ID
              value: ${{ env.MCP_SERVERS__PAYCHEX__CLIENT_ID }}
              type: secret
            - name: MCP_SERVERS__PAYCHEX__CLIENT_SECRET
              value: ${{ env.MCP_SERVERS__PAYCHEX__CLIENT_SECRET }}
              type: secret
            - name: OPENAI_API_KEY
              value: ${{ env.OPENAI_API_KEY }}
              type: secret
          container_spec: |
            cpu: 2
            memory: 4096
            min_scale: 1
            max_scale: 3

      - name: Process scan results
        if: always()
        run: |
          echo "Docker violations: ${{ steps.scan.outputs.docker-violations }}"