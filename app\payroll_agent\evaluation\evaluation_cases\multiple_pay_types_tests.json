[{"companyID": "A1", "comment": {"uid": "TEST_101", "id": "TEST_101", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC100"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON>,\nPlease pay me $5000.00 this period.\nThank you,\n<PERSON> from my iPhone"}, "subject": {"text": "Payroll Gross Pay"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_102", "id": "TEST_102", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC101"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Here is the payroll time for April 11th -  April 25th. Check date for April\n 30th   - THE SANCHEZ FIRM ACC101 \n-<PERSON> - 66 hrs. @ 22.50/hour \n\n\n-<PERSON><PERSON><PERSON> -  <PERSON> \n\n\n-<PERSON> - 88 hrs. @ 18.00/hour \n\n\n hour \n\n\n-<PERSON> -  <PERSON><PERSON> \n\n\nThanks and have a great day, \n\n<PERSON> \n[email tag2]\n[cid:image004.jpg@01DBB823.5C2004D0]\n352 N. Park Blvd., Suite 15\nBaton Rouge, LA 78602\n504-309-5000 ext. 218\b<EMAIL>"}, "subject": {"text": "Payroll Hourly and Salary"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_103", "id": "TEST_103", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC102"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>, \n\nHere is this weeks payroll:\n\n<PERSON> - $459\n\nThanks,\n<PERSON>, <PERSON>, <PERSON><PERSON>, Wag!\n513-332-9217"}, "subject": {"text": "Payroll Gross"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_104", "id": "TEST_104", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC103"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "On the Top payroll\n\nGood afternoon\n\n<PERSON><PERSON>.  92 hrs\n\n<PERSON>.  58 hrs\n\n<PERSON>.  150 hrs\n\n<PERSON>. 80 hrs\n\n<PERSON>. 80 hrs\n\nThank you"}, "subject": {"text": "On the Top Payroll - Hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_104", "id": "TEST_104", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC103"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> 30 hrs <PERSON> 35 hrs.\n\n<PERSON>.  Owner\nPainters of <PERSON>@paintersofcharlotte.com"}, "subject": {"text": "Payroll - Hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_105", "id": "TEST_105", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC104"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good morning:\n\nPlease process payroll for the week ending May 2, 2025, with a check date of May 2, 2025. Please note that my weekly gross pay is now $1,339.25. Please advise if you need anything further.\nIn addition, please remit a check for <PERSON> for 35 hours, hourly rate is $22.00 per hour.\n\nPlease confirm receipt of this email.\n\n\n\n\n\n\n\n<PERSON>, Paralegal\nPlicorn & Carney LLP\n78 Court Street, Suite 205\nBuffalo, New York 14253\nPhone# 716/588-7000\nFax# 716/588-7003"}, "subject": {"text": "Client #ACC104 - hourly and gross"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_106", "id": "TEST_106", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC105"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON>,\n\nPlease process the following payroll with a check date of Wednesday April 30th, 2025.\n\nAll regular salaries:\n\n  *   <PERSON>: $1,408.33 gross salary\n  *   <PERSON>: $1,700.00 gross salary\n  *   <PERSON>: $675.00 gross salary\n  *   <PERSON>: $650.00 gross salary\n  *   <PERSON>: $325.00 gross salary\n  *   <PERSON>: $525.00 gross salary\n\nHourly:\n\n  *   <PERSON> - 46 Hours\n\nThank you!"}, "subject": {"text": "Moss Mountain (ACC105) #client# - salary and hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_107", "id": "TEST_107", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC106"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON><PERSON>-40 hrs at $31\n\n<PERSON>-34 hrs at $27\n\n<PERSON>-34 hrs at $27\n\n<PERSON>-37 hrs at $24\nSent from my iPhone"}, "subject": {"text": "Payroll week ending 4/27 - hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_108", "id": "TEST_108", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC107"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Payroll for week ending *4/26/2025*\n\n*<PERSON>*\n*40  Reg*\n*13  OT*\n\n\n-- \nThank you,\n\n*Adam*"}, "subject": {"text": "Payroll - Hourly and OT"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_109", "id": "TEST_109", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC108"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON><PERSON>\n\n\nI am emailing my payroll to you for the Law Office of <PERSON>, PLL\n\n\nthe payroll posting 5/1/25 for 4/15/25=C2=A0 to 4/30/25 is\n\n\n\n\n<PERSON><PERSON> No Pay\n\n<PERSON> Pay $ 2,500.00 Sal<PERSON> pay 48.5 all regular hours\n\n<PERSON> pay 46.5 regular hours\n\n<PERSON> pay 75.5 all regular hours\n\n<PERSON><PERSON><PERSON> pay 79.0 regular hours\n\nThank you\nIf you need to speak with me, My cell phone is ************.\n\n<PERSON>"}, "subject": {"text": "Aruzzo law office - Hourly and salary"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_110", "id": "TEST_110", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC109"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> - 24 hrs.\n\n<PERSON> - 24 hrs.\n\n<PERSON>,\n<PERSON>"}, "subject": {"text": "Payroll - hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_110", "id": "TEST_110", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC109"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>;<EMAIL>"], "body": {"text": "Please see below new payroll from 04/14 to 04/27 dated for may 9/25\n dwight schrute\n43 h 05 m\n\njim hal<PERSON>\n61h 45 m regular\n7 h 30 m personal day\ntotal 69 h 24 m\n stanley hudson\nsame\n\npamela beasley\nno check \noscar martinez\nno check\nplease send back journal for approval\nPlease adjust your fees to 3 checks.\nthanks\nmicha<PERSON>"}, "subject": {"text": "NEW PAYROLL - hourly"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_111", "id": "TEST_111", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC110"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>,\n\n In next regular payroll please add the following gross bonus amount:\n\n (1)  $13,820.84 (Thirteen Thousand Eight Hundred Twenty dollars and 84 cents) into <PERSON>'s normal paycheck.\n\n (2) $10,000 (Ten Thousand dollars) into my (<PERSON>'s) normal paycheck.\n\n(3) $1,250 (Twelve hundred Fifty dollars) into <PERSON>'s normal paycheck.\n\n Please include the above bonus amounts in <PERSON>'s, my and <PERSON>'s regular paycheck.  Please issue a single check that includes both the bonus and regular pay in one check as you have done in past.\n\n Please confirm via e-mail bonus amount will be added to next regular payroll run. \n\n Thank you!\n\n Ron"}, "subject": {"text": "Payroll - Bonus"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_112", "id": "TEST_112", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC111"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>,\n\nA few things:\n\nSalaried Employees:\n\n<PERSON> - Gross Pay $9,000\n\nBritta Sullaway - Gross Pay $9,000\n\nEmma Sullaway - Gross Pay $2000 \n\nSawyer Sullaway - Gross Pay $2000 \n\nKeaton Sullaway - Gross Pay $1250 \n\nJesus Saravia - Gross Pay: $6032.83\n\n<PERSON><PERSON><PERSON><PERSON> - Gross Pay $5,208.33\n\n<PERSON><PERSON>- Gross Pay $3333.33. Issue $1000 Bonus.\n\nTricia Rust- Gross Pay - $3125. Bonus: $1,799.60 \n\nThank you.\n\n<PERSON><PERSON><PERSON>\n\nVice President"}, "subject": {"text": "Payroll - Bonus and Gross"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_113", "id": "TEST_113", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC112"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Caroline- $1463 + $165.25 in CC tips\n\nJan- $1647.25 + $ 287.75 in CC tips"}, "subject": {"text": "Payroll - Tips"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_114", "id": "TEST_114", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC113"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>: 15 hrs at $7.25/hr + $48.62 in tips\n\nJoan: 24 hrs at $7.75/hr + $32.98 in tips"}, "subject": {"text": "Payroll - Tips"}}]}}]