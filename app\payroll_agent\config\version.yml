version: "0.2.30"
release_date: "2025-09-19"
change_log:
  - "Changed order between execution graph and validation graph"
---
version: "0.2.29"
release_date: "2025-09-18"
change_log:
  - "worker check POST and RELEASE only working on PRODUCTION MODE"
---
version: "0.2.28"
release_date: "2025-09-18"
change_log:
  - "Commands for same worker grouped in the same payload for worker check POST"
---
version: "0.2.27"
release_date: "2025-09-17"
change_log:
  - "Added worker check POST and RELEASE"
---
version: "0.2.26"
release_date: "2025-09-11"
change_log:
  - "Added late stage knockout rules to validation agent in graph 4"
---
version: "0.2.25"
release_date: "2025-09-04"
change_log:
  - "Ability to handle attachments"
  - "Optimized parrallel processing of company worker node"
  - "Handle handwritten notes"
---
version: "0.2.24"
release_date: "2025-08-26"
change_log:
  - "Updated worker_id matching in the payroll extraction agent"
---

version: "0.2.23"
release_date: "2025-08-26"
change_log:
  - "Updated reference to myself cases"
---

version: "0.2.22"
release_date: "2025-08-26"
change_log:
  - "Created Agentic Engine Orchestrator with remote Langgraph"
---

version: "0.2.21"
release_date: "2025-08-26"
change_log:
  - "Added Payroll guide for command extraction"
---

version: "0.2.20"
release_date: "2025-08-26"
change_log:
  - "Updated knockouts"
---

version: "0.2.19"
release_date: "2025-08-26"
change_log:
  - "Created Agentic Engine Orchestrator"
---
version: "0.2.18"
release_date: "2025-08-20"
change_log:
  - "Added MCP refresh token"
---
version: "0.2.17"
release_date: "2025-08-19"
change_log:
  - "Added earning name and classificationType to payroll commands"
---
version: "0.2.16"
release_date: "2025-08-19"
change_log:
  - "Knockouts"
---
version: "0.2.15"
release_date: "2025-08-14"
change_log:
  - "Merged CD langGraph deployment"
---
version: "0.2.14"
release_date: "2025-08-12"
change_log:
  - "Updated knockouts"
---
version: "0.2.13"
release_date: "2025-08-12"
change_log:
  - "Improvements for handling implicit workers, e.g. myself"
---
version: "0.2.12"
release_date: "2025-08-12"
change_log:
  - "Fixed annual salary being paid to workers, and agent can now handle multiple commands to same worker"
---
version: "0.2.11"
release_date: "2025-08-08"
change_log:
  - "Ensure we can handle all workers and same as last period with no employees metadata"
---
version: "0.2.10"
release_date: "2025-08-08"
change_log:
  - "Myself in email"
---
version: "0.2.9"
release_date: "2025-08-08"
change_log:
  - "Added reasoning steps of the payroll extraction agent by calling it as a stream"
---
version: "0.2.8"
release_date: "2025-08-07"
change_log:
  - "Changed Agent model to use gpt-4.1, removed unused node in payroll processing"
---
version: "0.2.7"
release_date: "2025-08-06"
change_log:
  - "Added tool for getting company checks when email says same as last pay period"
---
version: "0.2.7"
release_date: "2025-08-06"
change_log:
  - "Bugfix: Update graph 1 matching to >= logic for knockout thresholds to have detected ruled match"
---
version: "0.2.6"
release_date: "2025-08-06"
change_log:
  - "Graph 2 refactor for getting agent context and parallel branching for payperiod and workers matching"
---
version: "0.2.5"
release_date: "2025-08-05"
change_log:
  - "Add llm usage and run time to each node"
  - "Update to model o4 where easoning is not needed"
---
version: "0.2.4"
release_date: "2025-08-05"
change_log:
  - "Add shadow mode toggle"
---

version: "0.2.3"
release_date: "2025-08-05"
change_log:
  - "Add examples to run case with mock ca"
---

version: "0.2.2"
release_date: "2025-08-04"
change_log:
  - "Parallel calls to LLMs for classification business and technical knockouts"
---
version: "0.2.1"
release_date: "2025-08-02"
change_log:
  - "LLM calls are now asynchronous, and threading has been replaced with asyncio semaphores"
---
version: "0.2.0"
release_date: "2025-07-31"
change_log:
  - "Merge to main branch"
---
version: "0.1.13"
release_date: "2025-07-31"
change_log:
  - "Agent nows maps displayId -> companyId"
  - "displayiD is now required in the input state for API and Playground"
  - "Improved error handling for failed MCP calls"
---
version: "0.1.12"
release_date: "2025-07-31"
change_log:
  - "Update classification triage based on latest comments"
---
version: "0.1.11"
release_date: "2025-07-30"
change_log:
  - "Payroll extraction now has a a tool to get amount from hours and rate"
  - "Validation agent now triggers for commands exceeding 5000 amount"
---
version: "0.1.10"
release_date: "2025-07-30"
change_log:
  - "Update worker match to allow matching on first name if one one with that name"
  - "Update worker extraction to make it ore clear not to extract sender"
---
version: "0.1.9"
release_date: "2025-07-30"
change_log:
  - "Update knockoutlibrary"
  - "Update unaddresable cases"
---
version: "0.1.8"
release_date: "2025-07-30"
change_log:
  - "Update knockoutlibrary"
  - "Update graph 1 to include both business and technical knockouts"
---
version: "0.1.7"
release_date: "2025-07-30"
change_log:
  - "Updated API and Playground inputs to minimal required fields"
---
version: "0.1.6"
release_date: "2025-07-30"
change_log:
  - "Added default rate tool for payroll extraction"
---
version: "0.1.5"
release_date: "2025-07-30"
change_log:
  - "Including l1 intent reasoning only if confidence above knockout threshold"
---
version: "0.1.4"
release_date: "2025-07-29"
change_log:
  - "update primary model to o4-mini and o3-mini for second model"
---
version: "0.1.3"
release_date: "2025-07-29"
change_log:
  - "Added fraud detection"
---
version: "0.1.2"
release_date: "2025-07-29"
change_log:
  - "Include all l1 intents and update threshold to 85"
  - "Fix bug in routing for graph 1"
  - "Use LLM with retrys for all graphs"
  - "Update logging"
---
version: "0.1.1"
release_date: "2025-07-28"
change_log:
  - "Update payperiod matching"
---
version: "0.1.0"
release_date: "2025-07-28"
change_log:
  - "Added versioning and changelog to input state"