from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

from app.payroll_agent.models.payroll_processing import WorkerCommand


class ValidatedPayrollEntries(BaseModel):
    validated_entries: List[WorkerCommand] = Field(default_factory=list, description="payroll_entries")
    success: Optional[bool] = Field(None, description="Indicates if the validation was successful")
    error: Optional[str] = Field(None, description="Error message if validation failed")
