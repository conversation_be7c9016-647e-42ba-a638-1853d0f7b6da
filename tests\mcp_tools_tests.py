import httpx
import json
import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools

import dotenv
dotenv.load_dotenv()

async def get_oauth_token(client_id: str, client_secret: str):
    """Get OAuth token using client credentials grant."""
    async with httpx.AsyncClient() as client:
        form_data = {
            "username": client_id,
            "password": client_secret,
            "scope": ""
        }
        resp = await client.post("http://localhost:9100/auth/token", data=form_data)
        resp.raise_for_status()
        return resp.json()["access_token"]

def get_MCP_client(config: dict = None) -> MultiServerMCPClient:
    try:
        mcp_client = MultiServerMCPClient(config)
        return mcp_client
    except ConnectionError as e:
        raise

async def main():
    client_id = "payroll_agent"
    client_secret = "payroll_agent_secret"

    # 1) Fetch token
    token = await get_oauth_token(client_id, client_secret)

    # 2) Define your MCP server connections
    connections = {
        "paychex": {
            "url": "http://localhost:9100/mcp",
            "transport": "streamable_http",
            "headers": {
                "Authorization": f"Bearer {token}"
            },
        }
    }

    mcp_client = get_MCP_client(connections)
    tools = await mcp_client.get_tools(server_name="paychex")
    print("Tools:", tools)

    # 1) find the tool by name
    tool = next(t for t in tools if t.name == "paychex_company_cheks")

    # 2b) asynchronous call
    result = await tool.arun({"companyID": "A1", "payperiodID": "850000414358408"})
    print("Lookup result:", result)

if __name__ == "__main__":
    asyncio.run(main())