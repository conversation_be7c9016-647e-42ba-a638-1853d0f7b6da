from typing import Optional, List, Any, Dict
from pydantic import BaseModel, Field

from app.payroll_agent.models.validation import ValidatedPayrollEntries



class ValidationOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    validation_output: Optional[Any] = Field(None, description="Validation output")
    DetectedKnockoutRules: Optional[Dict[str, Dict]] = Field(None, description="Knockout rules detected")
    validated_payroll_entries: ValidatedPayrollEntries = Field(default_factory=ValidatedPayrollEntries, description="validated payroll entries")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")
    run_time: Optional[dict] = Field(None, description="Time taken to run the classification graph")
    llm_usage: Optional[dict] = Field(default_factory=dict, description="LLM usage info: model, input tokens, output tokens")
