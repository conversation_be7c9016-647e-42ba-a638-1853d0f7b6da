from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class PreparePayload(BaseModel):
    """Container for the prepared execution payload."""
    payload: List[Dict[str, Any]] = Field(
        default_factory=list, description="Execution payload"
    )
    success: Optional[bool] = Field(
        None, description="Whether preparing payload succeeded"
    )
    error: Optional[str] = Field(
        None, description="Error message if preparation failed"
    )
