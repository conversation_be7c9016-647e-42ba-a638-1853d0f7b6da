import logging
from typing import Dict

from azure.identity.aio import ManagedIdentityCredential, DefaultAzureCredential

logger = logging.getLogger(__name__)


class EasyAuthClient:
    """Client for endpoints protected by Azure Easy Auth (AAD tokens).

    Obtains a Bearer token for a target App Registration client ID and returns
    Authorization headers suitable for HTTP requests. Uses Managed Identity when
    available, or DefaultAzureCredential for local development.
    """

    def __init__(
        self,
        app_url: str,
        target_app_client_id: str,
        use_default_credential: bool = False,
    ) -> None:
        self.app_url = app_url.rstrip("/")
        self.target_app_client_id = target_app_client_id

        # Choose credential source based on explicit flag
        if use_default_credential:
            self.credential = DefaultAzureCredential()
            logger.info("Using DefaultAzureCredential for Easy Auth token acquisition")
        else:
            self.credential = ManagedIdentityCredential()
            logger.info("Using ManagedIdentityCredential for Easy Auth token acquisition")

        # Single scope format consistent with the working example
        self.scope = f"{self.target_app_client_id}/.default"

    async def get_headers(self) -> Dict[str, str]:
        """Return Authorization headers with a fresh Bearer token."""
        token = await self.credential.get_token(self.scope)
        return {
            "Authorization": f"Bearer {token.token}",
        }
