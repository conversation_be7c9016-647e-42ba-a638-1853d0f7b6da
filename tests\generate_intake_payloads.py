import json
import os
import random
from copy import deepcopy
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import httpx
import pandas as pd
from dotenv import load_dotenv

from app.cli.logging_utils import setup_logger

logger = setup_logger("IntakePayloadTestGenerator")
load_dotenv("/Users/<USER>/Documents/Projects/payroll-email-agent/.env")


TEMPLATE_PATH = Path("tests/test_files/sample_rpa_payload.json")
EXCEL_PATH = Path("tests/test_files/Payroll_processing_golden_set_20250923.xlsx")
OUTPUT_DIR = Path("tests/test_files/intake_test_payloads")


REQUIRED_COLS = {
    "displayid": "displayID",
    "timestamp": "TimeStamp",
    "from_email": "From_email",
    "to_email": "To_email",
    "emailsubject": "Email subject",
    "emailtouse": "Email to use",
    "testingcase": "Testing Case",
}


def _norm(s: str) -> str:
    return "".join(ch for ch in s.lower() if ch.isalnum())


def _find_col(
    df: pd.DataFrame, target: str, synonyms: Optional[List[str]] = None
) -> Optional[str]:
    target_norm = _norm(target)
    for col in df.columns:
        if _norm(str(col)) == target_norm:
            return col
    if synonyms:
        for syn in synonyms:
            syn_norm = _norm(syn)
            for col in df.columns:
                if _norm(str(col)) == syn_norm:
                    return col
    return None


def load_template(path: Path) -> Dict[str, Any]:
    with path.open("r", encoding="utf-8") as f:
        return json.load(f)


def to_iso8601(ts_val: Any) -> str:
    if ts_val is None or ts_val == "":
        return datetime.utcnow().isoformat()
    # pandas might parse Excel datetime as Timestamp/Datetime
    try:
        if isinstance(ts_val, (pd.Timestamp, datetime)):
            # Preserve microseconds precision consistent with sample
            return ts_val.to_pydatetime().isoformat()
        # string parse
        parsed = pd.to_datetime(ts_val, errors="coerce")
        if pd.isna(parsed):
            return datetime.utcnow().isoformat()
        return parsed.to_pydatetime().isoformat()
    except Exception:
        return datetime.utcnow().isoformat()


def parse_recipients(value: Any) -> List[str]:
    if value is None:
        return []
    if isinstance(value, list):
        return [str(v).strip() for v in value if str(v).strip()]
    text = str(value)
    # split by common separators
    parts = []
    for sep in [";", ",", " "]:
        if sep in text:
            parts = [p.strip() for p in text.split(sep) if p.strip()]
            break
    if not parts:
        parts = [text.strip()] if text.strip() else []
    return parts


def fill_payload(
    base: Dict[str, Any], row: pd.Series, colmap: Dict[str, str]
) -> Dict[str, Any]:
    payload = deepcopy(base)

    # Extract values with robustness on missing columns
    display_id = str(row.get(colmap["displayid"], "")).strip()
    timestamp_raw = row.get(colmap["timestamp"]) if colmap.get("timestamp") else None
    from_email = str(row.get(colmap["from_email"], "")).strip()
    to_email_raw = row.get(colmap["to_email"]) if colmap.get("to_email") else None
    email_subject = str(row.get(colmap["emailsubject"], "")).strip()
    email_to_use = str(row.get(colmap["emailtouse"], "")).strip()

    iso_ts = to_iso8601(timestamp_raw)
    to_list = parse_recipients(to_email_raw)

    # 1) clients.accountNumber <- displayID
    try:
        if isinstance(payload.get("clients"), list) and payload["clients"]:
            payload["clients"][0]["accountNumber"] = display_id
    except Exception:
        logger.warning("Failed to set clients[0].accountNumber")

    # 2) comment.timestamp/last_modified/created_at <- TimeStamp
    try:
        comment = payload.setdefault("comment", {})
        comment["timestamp"] = iso_ts
        comment["last_modified"] = iso_ts
        comment["created_at"] = iso_ts
    except Exception:
        logger.warning("Failed to set comment timestamps")

    # 3) comment.messages.from <- From_email
    # 4) comment.messages.to <- To_email
    # 5) comment.messages.body.text <- Email subject
    # 7) comment.messages.sent_at <- TimeStamp
    try:
        messages = payload["comment"].setdefault("messages", [{}])
        if not messages:
            messages.append({})
        msg0 = messages[0]
        msg0["from"] = from_email
        msg0["to"] = to_list
        msg0["body"] = {"text": email_to_use}
        msg0["subject"] = {"text": email_subject}
        msg0["sent_at"] = iso_ts
    except Exception:
        logger.warning("Failed to set comment.messages fields")

    # 8) bodyThreaded <- Email to use
    try:
        payload["bodyThreaded"] = email_to_use
    except Exception:
        logger.warning("Failed to set bodyThreaded")

    return payload


def build_colmap(df: pd.DataFrame) -> Dict[str, str]:
    colmap: Dict[str, str] = {}
    for norm_key, friendly in REQUIRED_COLS.items():
        # Add synonyms for common variations
        synonyms: Optional[List[str]] = None
        if norm_key == "from_email":
            synonyms = ["from", "from address", "sender", "sender email", "fromemail"]
        elif norm_key == "to_email":
            synonyms = ["to", "to address", "recipient", "recipient email", "toemail"]
        elif norm_key == "emailsubject":
            synonyms = ["subject", "email subject"]
        elif norm_key == "timestamp":
            synonyms = ["time", "datetime", "date sent", "date"]

        col = _find_col(df, norm_key, synonyms=synonyms)
        if not col:
            # Try to be extra lenient for timestamp
            if norm_key == "timestamp":
                for fallback in ["time", "datetime", "datesent", "date"]:
                    col = _find_col(df, fallback)
                    if col:
                        break
        if col:
            colmap[norm_key] = col
        else:
            logger.warning(
                "Missing expected column for '%s' (%s) in Excel",
                friendly,
                norm_key,
            )
            colmap[norm_key] = None  # mark missing; downstream handles None
    return colmap


def sample_rows(
    df: pd.DataFrame,
    colmap: Dict[str, str],
    n_each: int = 5,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    tc_col = colmap.get("testingcase")
    if not tc_col or tc_col not in df.columns:
        logger.warning(
            "'Testing Case' column not found; random sampling across all rows"
        )
        total = min(2 * n_each, len(df))
        shuffled = df.sample(n=total, random_state=random.randint(1, 10_000))
        half = len(shuffled) // 2
        return shuffled.iloc[:half], shuffled.iloc[half:half + n_each]

    norm_tc = df[tc_col].astype(str).str.lower().str.replace(" ", "", regex=False)
    # case-insensitive match for "Standard Processing"
    std_mask = norm_tc == "standardprocessing"
    ko_mask = norm_tc == "knockout"

    std_df = df[std_mask]
    ko_df = df[ko_mask]

    std_n = min(n_each, len(std_df))
    ko_n = min(n_each, len(ko_df))

    std_sample = (
        std_df.sample(n=std_n, random_state=random.randint(1, 10_000))
        if std_n > 0
        else std_df.head(0)
    )
    ko_sample = (
        ko_df.sample(n=ko_n, random_state=random.randint(1, 10_000))
        if ko_n > 0
        else ko_df.head(0)
    )

    if std_n < n_each or ko_n < n_each:
        logger.warning(
            "Requested 5 each but available "
            f"Standard={len(std_df)}, Knockout={len(ko_df)}; sampling available rows."
        )

    return std_sample, ko_sample


def ensure_output_dir(path: Path) -> None:
    path.mkdir(parents=True, exist_ok=True)


def save_payload(
    payload: Dict[str, Any],
    out_dir: Path,
    idx: int,
    display_id: str,
    ts_iso: str,
) -> Path:
    ts_compact = (
        ts_iso.replace(":", "")
        .replace("-", "")
        .replace(".", "")
        .replace("T", "_")
    )
    fname = f"payload_{idx:02d}_{display_id or 'noid'}_{ts_compact}.json"
    out_path = out_dir / fname
    with out_path.open("w", encoding="utf-8") as f:
        json.dump(payload, f, ensure_ascii=False, indent=2)
    return out_path


def clear_output_dir(path: Path) -> None:
    if not path.exists():
        return
    removed = 0
    for p in path.glob("*.json"):
        try:
            p.unlink()
            removed += 1
        except Exception as e:
            logger.warning(f"Failed to remove {p}: {e}")
    if removed:
        logger.info(f"Cleared {removed} existing payload file(s) in {path}")


def get_bearer_token_from_env() -> Tuple[Optional[str], Optional[str]]:
    """
    Returns (token, error).

    Uses either v2.0 scope flow or v1.0 resource flow, based on env.
    Env vars used:
      - AAD_TENANT_ID
      - AAD_CLIENT_ID
      - AAD_CLIENT_SECRET
      - AAD_SCOPE (preferred, e.g., api://<target-app-client-id>/.default)
      - AAD_RESOURCE (optional; uses v1 token endpoint if set)
    """
    tenant = os.getenv("AAD_TENANT_ID")
    client_id = os.getenv("AAD_CLIENT_ID")
    client_secret = os.getenv("AAD_CLIENT_SECRET")
    scope = os.getenv("AAD_SCOPE")
    resource = os.getenv("AAD_RESOURCE")

    logger.info(
        "AAD_TENANT_ID=%s, AAD_CLIENT_ID=%s, AAD_CLIENT_SECRET=%s, "
        "AAD_SCOPE=%s, AAD_RESOURCE=%s",
        tenant,
        client_id,
        "*****" if client_secret else None,
        scope,
        resource,
    )

    if not (tenant and client_id and client_secret and (scope or resource)):
        return None, (
            "Missing AAD_* env vars. "
            "Required: AAD_TENANT_ID, AAD_CLIENT_ID, "
            "AAD_CLIENT_SECRET, and AAD_SCOPE or AAD_RESOURCE."
        )

    token_url = (
        f"https://login.microsoftonline.com/{tenant}/oauth2/v2.0/token" if scope else
        f"https://login.microsoftonline.com/{tenant}/oauth2/token"
    )

    data: Dict[str, str] = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials",
    }
    if scope:
        data["scope"] = scope
    else:
        data["resource"] = resource or ""

    try:
        with httpx.Client(timeout=15.0) as client:
            resp = client.post(
                token_url,
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
            resp.raise_for_status()
            token = resp.json().get("access_token")
            if not token:
                return None, "Token response missing access_token"
            return token, None
    except httpx.HTTPError as e:
        return None, f"Token request failed: {e}"


def send_payload(
    payload: Dict[str, Any], bearer_token: str, url: str
) -> Tuple[int, str]:
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {bearer_token}",
    }
    with httpx.Client(timeout=20.0) as client:
        r = client.post(url, headers=headers, json=payload)
        status = r.status_code
        text = r.text
        return status, text


def main(count: int = 10, send: bool = True) -> None:
    if not TEMPLATE_PATH.exists():
        raise FileNotFoundError(f"Template not found: {TEMPLATE_PATH}")
    if not EXCEL_PATH.exists():
        raise FileNotFoundError(f"Excel file not found: {EXCEL_PATH}")

    logger.info("Loading template and Excel data")
    template = load_template(TEMPLATE_PATH)
    df = pd.read_excel(EXCEL_PATH)
    colmap = build_colmap(df)

    std, ko = sample_rows(df, colmap, n_each=count // 2)
    combined = pd.concat([std, ko], ignore_index=True)
    if len(combined) < count:
        logger.warning(f"Only {len(combined)} rows sampled; expected {count}")

    ensure_output_dir(OUTPUT_DIR)
    clear_output_dir(OUTPUT_DIR)

    ingest_url = os.getenv("INGEST_EMAIL_URL", "")
    bearer_token = None

    if send:
        if not ingest_url:
            logger.warning("INGEST_EMAIL_URL not set; skipping send phase")
            send = False
        else:
            bearer_token, err = get_bearer_token_from_env()
            logger.info(f"Bearer token: {bearer_token}")
            if err:
                logger.warning(
                    f"Skipping send: {err}. "
                    "Set AAD_* env vars (TENANT_ID, CLIENT_ID, CLIENT_SECRET, "
                    "and SCOPE or RESOURCE)."
                )
                send = False

    logger.info(f"Generating {len(combined)} payload(s)")
    for idx, (_, row) in enumerate(combined.iterrows(), start=1):
        payload = fill_payload(template, row, colmap)

        # Best-effort for filename metadata
        display_id = (
            str(row.get(colmap.get("displayid", ""), "")).strip()
            if colmap.get("displayid")
            else ""
        )
        ts_raw = row.get(colmap.get("timestamp")) if colmap.get("timestamp") else None
        ts_iso = to_iso8601(ts_raw)

        out_path = save_payload(payload, OUTPUT_DIR, idx, display_id, ts_iso)
        logger.info(f"Wrote {out_path}")

        if send and bearer_token:
            try:
                status, text = send_payload(payload, bearer_token, ingest_url)
                logger.info(f"Sent payload {idx:02d}: HTTP {status}")
                if status >= 300:
                    logger.warning(f"Server response: {text[:300]}")
            except Exception as e:
                logger.error(f"Failed to send payload {idx:02d}: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Generate and optionally send intake payloads"
    )
    parser.add_argument(
        "-n",
        "--count",
        type=int,
        default=10,
        help="Total payloads to generate (default: 10)",
    )
    parser.add_argument(
        "--no-send",
        action="store_true",
        help="Generate files only; do not send to endpoint",
    )
    args = parser.parse_args()

    main(count=args.count, send=not args.no_send)
