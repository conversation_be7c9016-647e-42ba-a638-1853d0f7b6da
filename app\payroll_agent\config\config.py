from functools import lru_cache
from typing import Dict, List, Literal, Optional, ClassVar, Union
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import httpx
from urllib.parse import urlparse

import truststore
from app.payroll_agent.utils.version import load_version_config

truststore.inject_into_ssl()

LLMType = Union[ChatOpenAI, AzureChatOpenAI]
VERSION_CONFIG = load_version_config()

class MCPConnection(BaseModel):
    """Configuration for a single MCP server."""
    url: Optional[str] = None
    transport: Literal["streamable_http", "websocket", "stdio"]
    command: Optional[str] = Field(None, exclude=True)
    args: Optional[List[str]] = Field(None, exclude=True)

    # Mark these as Secret (and exclude them from dumps)
    authentication_url: Optional[str] = Field(None, exclude=True)
    client_id: Optional[str] = Field(None, exclude=True)
    client_secret: Optional[str] = Field(None, exclude=True)

    # Headers will be populated by Settings
    headers: Dict[str, str] = Field(default_factory=dict)


class Settings(BaseSettings):
    # Environment Mode
    ENV_MODE: str = "SHADOW_MODE"

    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = False

    # OpenAI Settings
    OPENAI_API_KEY: Optional[str] = None

    # LLM Settings
    LLM_MODEL_REASONING: str = "o4-mini"
    LLM_MODEL_MULTI_MODAL: str = "gpt-4o"
    LLM_MODEL_REASONING_SECOND_AGENT : str = "gpt-41"
    LLM_MODEL_NON_REASONING: str = "gpt-41"
    LLM_TEMPERATURE: float = 0.0
    LLM_TYPE: Optional[str] = "OPENAI"
    API_VERSION: Optional[str] = None
    AZURE_ENDPOINT: Optional[str] = None

    # Azure blob storage settings
    AZURE_STORAGE_SAS_TOKEN: Optional[str] = None
    AZURE_STORAGE_ACCOUNT_URL:  Optional[str] = "https://rgemailpayrollautom97ca.blob.core.windows.net"
    AZURE_STORAGE_CONTAINER_NAME: Optional[str] = "payroll-intake" 
    AZURE_STORAGE_TIMEOUT: Optional[int] = 30

    # Version Settings - loaded from version.yml
    AGENT_VERSION: str = VERSION_CONFIG["version"]
    AGENT_RELEASE_DATE: str = VERSION_CONFIG["release_date"]

    _llm_instance: ClassVar[Optional[LLMType]] = None
    _alt_llm_instances: ClassVar[Dict[int, LLMType]] = {}

    # LangSmith Settings
    LANGCHAIN_API_KEY: Optional[str] = None
    LANGCHAIN_TRACING_V2: Optional[str] = None
    LANGCHAIN_ENDPOINT: Optional[str] = None
    LANGCHAIN_PROJECT: Optional[str] = None

    # Langgraph settings
    LANGGRAPH_API_URL: Optional[str] = None

    # Dashboard logging settings
    DASHBOARD_API_URL: Optional[str] = None
    ENABLE_DASHBOARD_INTEGRATION: bool = False
    DASHBOARD_TIMEOUT: float = 15.0
    # Dashboard Easy Auth settings
    AZURE_CLIENT_ID: Optional[str] = None
    EASY_AUTH_ENABLED: bool = False
    EASY_AUTH_USE_DEFAULT_CREDENTIAL: bool = False


    # MCP Settings
    mcp_servers: Dict[str, MCPConnection] = Field(
        default_factory=dict,
        description="Mapping of server name to MCPConnection configs"
    )

    # Pydantic Settings configuration
    model_config = SettingsConfigDict(
        env_file='.env',
        case_sensitive=False,
        env_nested_delimiter='__',
        ignored_types=(ChatOpenAI, AzureChatOpenAI)
    )


    def __init__(self, **data):
        super().__init__(**data)
        # right after we’re built, fetch each token sync-ly
        self._populate_mcp_headers_sync()

    def _populate_mcp_headers_sync(self) -> None:
        for server_name, conn in self.mcp_servers.items():
            if conn.client_id and conn.client_secret and conn.authentication_url:
                # Check if the server is reachable before attempting OAuth
                if self._is_server_reachable(conn.authentication_url, server_name):
                    try:
                        token = self._fetch_oauth_token_sync(
                            conn.authentication_url,
                            conn.client_id,
                            conn.client_secret,
                        )
                        conn.headers["Authorization"] = f"Bearer {token}"
                        print(f"✅ Successfully authenticated with MCP server '{server_name}'")
                    except Exception as e:
                        print(f"⚠️ Failed to authenticate with MCP server '{server_name}': {e}")
                        print(f"   Server is reachable but authentication failed")
                else:
                    print(f"❌ MCP server '{server_name}' is not reachable at {conn.authentication_url}")
                    print(f"   Skipping authentication for this server")

    @staticmethod
    def _is_server_reachable(auth_url: str, server_name: str, timeout: int = 5) -> bool:
        """Check if the MCP server is reachable before attempting authentication.
        
        Args:
            auth_url: The authentication URL to check
            server_name: Name of the server for logging
            timeout: Timeout in seconds for the check
            
        Returns:
            True if server is reachable, False otherwise
        """
        try:
            # Parse the URL to get the base server URL
            parsed_url = urlparse(auth_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            print(f"🔍 Checking reachability of MCP server '{server_name}' at {base_url}...")
            
            with httpx.Client(timeout=timeout) as client:
                # Try a simple GET request to the base URL first
                response = client.get(base_url, follow_redirects=True)
                if response.status_code < 500:  # Accept any non-server-error response
                    return True
        except httpx.ConnectError:
            print(f"   Connection failed: Cannot resolve hostname or connect to server")
            return False
        except httpx.TimeoutException:
            print(f"   Connection timed out after {timeout} seconds")
            return False
        except Exception as e:
            print(f"   Unexpected error during reachability check: {e}")
            return False
        
        # If base URL fails, try the auth endpoint with a HEAD request (lighter than GET)
        try:
            with httpx.Client(timeout=timeout) as client:
                # Use HEAD request to check if auth endpoint exists without sending data
                response = client.head(auth_url, follow_redirects=True)
                if response.status_code < 500:  # Accept any non-server-error response
                    return True
        except Exception:
            pass
            
        return False

    @staticmethod
    def _fetch_oauth_token_sync(auth_url: str , client_id: str, client_secret: str) -> str:
        with httpx.Client() as client:
            resp = client.post(
                auth_url,
                data={
                    "username": client_id,
                    "password": client_secret,
                    "scope": ""
                },
            )
            resp.raise_for_status()
            return resp.json()["access_token"]


    def LLM(self, model: str = None, reasoning_effort:str = None) -> LLMType:
        """
        Get or create a cached LLM instance for the requested model and agent number.
        """
        if model is None:
            model = self.LLM_MODEL_REASONING

        if not hasattr(self, "_llm_instances"):
            self._llm_instances = {}

        model_name = f"{model}_{reasoning_effort}" if reasoning_effort else model
        if model_name not in self._llm_instances.keys():
            self._llm_instances[model_name] = self._create_llm(model=model, effort=reasoning_effort)

        return self._llm_instances[model_name]

    def _create_llm(self, model: str, effort: str = None) -> LLMType:
        """Create an LLM instance with the specified model.

        Args:
            model: The model name to use (e.g., "gpt-4o", "gpt-4.1", etc.)

        Returns:
            An instance of ChatOpenAI or AzureChatOpenAI
        """
        # Models that support temperature tuning
        temp_models = {"gpt-4o", "gpt-4.1", "gpt-41"}
        temperature = self.LLM_TEMPERATURE if model in temp_models else None

        if self.LLM_TYPE == "AZURE":
            # api_version = self.API_VERSION or "2024-02-01"
            api_version = self.API_VERSION or "2024-12-01-preview"
            endpoint = self.AZURE_ENDPOINT or "https://service-internal-n1.paychex.com"

            # Support Paychex-specific routing
            if "service-internal" in endpoint and "paychex.com" in endpoint:
                endpoint = f"{endpoint}/eps/shared/azure/openai/deployments/{model}"

            azure_args = {
                "api_version": api_version,
                "azure_endpoint": endpoint,
                "model": model,
                "api_key": self.OPENAI_API_KEY,
            }
            if temperature is not None:
                azure_args["temperature"] = temperature
            else:
                if effort:
                    azure_args["reasoning_effort"] = effort
                pass
            return AzureChatOpenAI(**azure_args)

        elif self.LLM_TYPE == "OPENAI":
            openai_args = {
                "model": model,
                "api_key": self.OPENAI_API_KEY,
            }
            if temperature is not None:
                openai_args["temperature"] = temperature
            else :
                if effort:
                    openai_args["reasoning_effort"] = effort
                pass
            return ChatOpenAI(**openai_args)

        else:
            raise ValueError(f"Unsupported LLM_TYPE: {self.LLM_TYPE!r}")

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        # Override to prioritize .env file over environment variables
        return (
            dotenv_settings,  # .env file has higher priority than env vars
            init_settings,
            env_settings,
            file_secret_settings,
        )

    def highlight_env(self):
        print(f"Running in ENV_MODE: {self.ENV_MODE}")

@lru_cache()
def get_settings() -> Settings:
    """Get the settings instance."""
    return Settings()

settings = get_settings()
settings.highlight_env()