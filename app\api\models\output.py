from pydantic import BaseModel, Field
from typing import Any, Optional, List




class EmailResponse(BaseModel):
    """Response model for processed payroll emails"""
    response : dict = Field(description="Response message")

class OrchestrationResponse(BaseModel):
    """Response model for orchestrated processing"""
    # Core orchestration fields
    ingest_id: str = Field(description="Unique identifier for the orchestrated process")
    status: str = Field(description="Status of the orchestration")
    status_code: int = Field(description="HTTP status code for the orchestration")
    started_at: str = Field(description="Start time of the orchestration")
    finished_at: str = Field(description="Finish time of the orchestration")
    run_time: float = Field(description="Total runtime in seconds")
    
    # Processing status fields with defaults
    processing_status: str = Field(default="fail", description="Processing status (success/fail)")
    displayId: str = Field(default="", description="Display ID from input")
    payPeriodId: str = Field(default="", description="Pay period identifier")
    payrollId: str = Field(default="", description="Payroll identifier")
    knockout_flag: bool = Field(default=True, description="Whether processing was knocked out")
    knockout_reason: List[str] = Field(default_factory=lambda: ["MANUAL_CHECKS"], description="Reasons for knockout")
    csa_action_flag: bool = Field(default=False, description="CSA action required flag")
    csa_note: dict = Field(default_factory=dict, description="CSA notes")
    pre_processing_flag: bool = Field(default=False, description="Pre-processing flag")
    reports: List[Any] = Field(default_factory=list, description="Generated reports")
    hold_flag: bool = Field(default=False, description="Hold flag")
    hold: List[Any] = Field(default_factory=list, description="Hold details")
    email_response: dict = Field(default_factory=dict, description="Email response details")
    
    # Optional temp fields
    temp_langgraph_invoke_method: Optional[Any] = Field(
        default=None,
        description="Optional agent response — any JSON-serializable format is allowed (dict, list, str, number, etc.)"
    )
    temp_agent_response: Optional[Any] = Field(
        default=None,
        description="Optional agent response — any JSON-serializable format is allowed (dict, list, str, number, etc.)"
    )