from typing import Optional, List
from pydantic import BaseModel, Field


from app.payroll_agent.models.execution import PreparePayload


class ExecutionOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    prepare_payload: PreparePayload = Field(default_factory=PreparePayload, description="Prepared payload for execution")
    formatted_payroll_entries: Optional[List] = Field(None, description="formatted payroll entries")
    created_checks: Optional[List[str]] = Field(None, description="payroll_entries")
    created_checks_flag: Optional[bool] = Field(None, description="Whether to payrolls are completed")
    failed_payrolls: Optional[List[str]] = Field(None, description="worker ids whose payrolls failed")
    success_rate: Optional[float] = Field(None, description="Success rate of the payrolls")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")
    run_time: Optional[dict] = Field(None, description="Time taken to run the classification graph")
    llm_usage: Optional[dict] = Field(default_factory=dict, description="LLM usage info: model, input tokens, output tokens")