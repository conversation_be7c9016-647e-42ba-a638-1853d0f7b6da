import asyncio

from typing import <PERSON><PERSON>, List
from openai import BadRequestError

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.graph.states.classification import ClassificationOutputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.classification import (
    IntentTriage, 
    RequestTypeTags, 
    ComplexityTags,
    TRIAGE_FIELDS_TEXT,
    create_knockout_rules_text_for_batch,
    BusinessKnockouts, 
    TechnicalKnockouts,
    BUSINESS_KNOCKOUT_BATCHES, 
    TECHNICAL_KNOCKOUT_BATCHES,
    business_knockout_rules, 
    technical_knockout_rules,
)

from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config
from app.payroll_agent.utils.llm_utils import invoke_structured_llm_with_retry, create_llm_with_structured_output
from app.payroll_agent.utils.logging import log_runtime, log_llm_usage


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("classification")
ROUTERS_CONFIG = load_routers_config('routers_config')['classification']
TRIAGE_ROUTERS_CONFIG = ROUTERS_CONFIG["intent_triage"]
BUSINESS_L1_ROUTERS_CONFIG = ROUTERS_CONFIG["business_knockout"]
TECHNICAL_L1_ROUTERS_CONFIG = ROUTERS_CONFIG["technical_knockout"]
RULES_CONFIG = load_routers_config('rules_config')['classification']
COMPLEXITY_RULES_CONFIG = RULES_CONFIG["complexity_rules"]

# Convert knockout rules to lookup and create rules text for prompt
BUSINESS_KNOCKOUT_RULES_DICT = {rule['id']: rule for rule in business_knockout_rules}
TECHNICAL_KNOCKOUT_RULES_DICT = {rule['id']: rule for rule in technical_knockout_rules}


logger.info(f"Loaded {len(BUSINESS_KNOCKOUT_RULES_DICT)} active business knockout rules: {list(BUSINESS_KNOCKOUT_RULES_DICT.keys())}")
logger.info(f"Loaded {len(TECHNICAL_KNOCKOUT_RULES_DICT)} active technical knockout rules: {list(TECHNICAL_KNOCKOUT_RULES_DICT.keys())}")


@log_runtime("classification_intent_triage", "classification_", "classification_output_state")
async def classification_intent_triage(state: PayrollState) -> PayrollState:
    logger.info("classification_intent_triage called...")

    try:
        # Load and format the prompt with triage fields
        prompt = PROMPTS["intent_triage_classification"].format(
            triage_fields_text=TRIAGE_FIELDS_TEXT
        )
        
        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(),
            schema=IntentTriage,
        )
        
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]

        result, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message, IntentTriage)
        logger.debug(f"intent_triage got structured output: {result}")

        log_llm_usage(
            state=state,
            step_name="classification_intent_triage",
            llm=llm,
            llm_response=result,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            output_state_attr="classification_output_state"
        )
        state.classification_output_state.intent_triage_metadata = result
        return state

    except BadRequestError as e:
        # Look specifically for API calls that fail when harmful content is detected via Azure's OpenAI
        # content filtering. For prompts, this results in a 400 (BadRequestError) response with a code of content_filter. 
        # Log details about the filters that failed, and flag these in the output state.
        # See https://learn.microsoft.com/en-us/azure/ai-foundry/openai/concepts/content-filter for details
        if e.code == "content_filter":
            logger.warning(f"Content filter violation detected: {e}", exc_info=True)

            # Extract content_filter_result if available (this will provide details about
            # the type(s) of filters that are checked and which failed, e.g. jailbreak attempt)
            content_filter_result = None
            filtered_categories = []
            if hasattr(e, 'response') and hasattr(e.response, 'json'):
                err_json = e.response.json()
                content_filter_result = err_json.get('error', {}).get('innererror', {}).get('content_filter_result')
                
                # Only store categories that were filtered or detected
                # The result has a structure like:
                # {"hate": {"filtered": false, "severity": "safe"}, "jailbreak": {"filtered": true, "detected": true}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}
                if content_filter_result:
                    for category, details in content_filter_result.items():
                        if details.get('filtered', False) or details.get('detected', False):
                            filtered_categories.append(category)
            
            state.classification_output_state = ClassificationOutputState(
                intent_triage_content_policy_violation=True,
                intent_triage_content_filter_results=filtered_categories
            )

            return state
        else:
            logger.error(f"BadRequestError in intent_triage: {type(e).__name__} - {e}", exc_info=True)
            raise
    except Exception as e:
        logger.error(f"Error in intent_triage after retries. Error: {type(e).__name__} - {e}", exc_info=True)
        raise


@log_runtime("classification_intent_triage_router", "classification_", "classification_output_state")
def classification_intent_triage_router(state: PayrollState) -> PayrollState:
    logger.info("classification_intent_triage_router called...")

    triage_data = state.classification_output_state.intent_triage_metadata
    failures = []
    
    # First, check for content policy violation
    if getattr(state.classification_output_state, 'intent_triage_content_policy_violation', False):
        failures.append("Harmful content detected")
    else:
        # Only check triage fields if no policy violation
        for field, threshold in TRIAGE_ROUTERS_CONFIG.items():
            value = getattr(triage_data, field, None)
            if value is None:
                logger.warning(f"Field '{field}' not found in triage data.")
                failures.append(f"{field}_missing")
            elif value <= threshold:
                logger.debug(f"Triage check failed: {field} = {value} <= {threshold} (field-specific knockout threshold)")
                failures.append(f"{field}_below_knockout_threshold")
                
    if not failures:
        logger.debug("classification_intent_triage_router: all conditions met, passes triage")
        state.classification_output_state.intent_triage_passes_flag = True
        state.classification_output_state.intent_triage_passes_fail_reasons = []
    else:
        logger.debug(f"classification_intent_triage_router: fails triage due to {failures}")
        state.classification_output_state.intent_triage_passes_flag = False
        state.classification_output_state.intent_triage_passes_fail_reasons = failures
    return state


@log_runtime("classification_knockout_batched", "classification_", "classification_output_state")
async def classification_knockout_batched(
    state: PayrollState,
    batches,
    prompt_key: str,
    model_class,
    knockout_rules,
    routers_config,
    output_attr: str,
) -> PayrollState:
    """
    Generic batched knockout rule evaluation.
    """
    logger.info(f"Starting {prompt_key} classification with batched approach")
    try:
        email_content = state.input_state.EmailContent
        email_sender = state.input_state.SourceAddress
        email_date = state.input_state.timestamp

        all_confidences = {}
        all_detected_rules = []
        total_batches = len(batches)
        logger.info(f"Processing {total_batches} batches in parallel (max 10 concurrent)")

        # Limit concurrent tasks to 10 (or fewer if you have fewer batches)
        semaphore = asyncio.Semaphore(min(total_batches, 10))

        async def process_batch(batch_info, batch_idx):
            batch_model = batch_info['model']
            batch_rules = batch_info['rules']
            batch_number = batch_idx + 1

            knockout_rules_text = create_knockout_rules_text_for_batch(batch_rules)
            scenario_rules_threshold = routers_config.get("KnockoutIntents", 85)
            prompt_text = PROMPTS[prompt_key].format(
                knockout_rules_text=knockout_rules_text,
                ScenarioRulesThreshold=scenario_rules_threshold,
                email_sender=email_sender,
                email_date=email_date
            )

            # # Save prompt for inspection
            # base_dir = Path(__file__).parent
            # PROMPT_SAVE_DIR = base_dir / "batch_prompts"
            # os.makedirs(PROMPT_SAVE_DIR, exist_ok=True)
            # prompt_path = PROMPT_SAVE_DIR / f"{prompt_key}_batch_{batch_number}_prompt.txt"
            # with open(prompt_path, "w", encoding="utf-8") as f:
            #     f.write(prompt_text)
            # logger.info(f"Saved batch {batch_number} prompt to {prompt_path}")

            llm = create_llm_with_structured_output(
                base_llm=settings.LLM(),
                schema=batch_model,
            )
            message = [
                {"role": "system", "content": prompt_text},
                {"role": "user", "content": email_content}
            ]
            batch_result, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message, batch_model)
            log_llm_usage(
                state=state,
                step_name=f"classification_{prompt_key}_batch_{batch_number}",
                llm=llm,
                llm_response=batch_result,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                output_state_attr="classification_output_state"
            )
            batch_dict = batch_result.model_dump()
            return batch_idx, batch_dict

        async def sem_process(batch_info, batch_idx):
            async with semaphore:
                # Process each batch with a semaphore to limit concurrency
                return await process_batch(batch_info, batch_idx)

        # Kick off all batch tasks
        tasks = [
            asyncio.create_task(sem_process(batch_info, batch_idx)) for batch_idx, batch_info in enumerate(batches)
        ]

        # As each completes, merge its results
        for completed in asyncio.as_completed(tasks):
            batch_idx, batch_dict = await completed

            # Merge confidences
            for key, value in batch_dict.items():
                if key != "DetectedKnockoutRules":
                    all_confidences[key] = value

            # Merge detected rules
            batch_detected = batch_dict.get("DetectedKnockoutRules", [])
            all_detected_rules.extend(batch_detected)
            logger.info(f"Batch {batch_idx + 1} detected {len(batch_detected)} rules: {batch_detected}")

        consolidated_data = {
            **all_confidences,
            'DetectedKnockoutRules': list(set(all_detected_rules))
        }
        logger.info(f"Consolidated data has {len(consolidated_data)} fields")
        result = model_class.model_validate(consolidated_data)
        logger.info(f"{prompt_key} classification completed. Total detected rules: {len(result.DetectedKnockoutRules)}")

        # Set the result in the correct output attribute
        setattr(state.classification_output_state, output_attr, result)
        return state

    except Exception as e:
        logger.error(f"Error in {prompt_key} classification: {str(e)}")
        raise


@log_runtime("classification_business_knockout_batched", "classification_", "classification_output_state")
async def classification_business_knockout_batched(state: PayrollState) -> PayrollState:
    return await classification_knockout_batched(
        state=state,
        batches=BUSINESS_KNOCKOUT_BATCHES,
        prompt_key="business_knockout_classification",
        model_class=BusinessKnockouts,
        knockout_rules=business_knockout_rules,
        routers_config=BUSINESS_L1_ROUTERS_CONFIG,
        output_attr="business_knockout_metadata"
    )


@log_runtime("classification_technical_knockout_batched", "classification_", "classification_output_state")
async def classification_technical_knockout_batched(state: PayrollState) -> PayrollState:
    result = await classification_knockout_batched(
        state=state,
        batches=TECHNICAL_KNOCKOUT_BATCHES,
        prompt_key="technical_knockout_classification",
        model_class=TechnicalKnockouts,
        knockout_rules=technical_knockout_rules,
        routers_config=TECHNICAL_L1_ROUTERS_CONFIG,
        output_attr="technical_knockout_metadata"
    )

    return {}


@log_runtime("classification_business_knockout_router", "classification_", "classification_output_state")
def classification_business_knockout_router(state: PayrollState) -> PayrollState:
    logger.info("classification_business_knockout_router called...")

    l1_data = state.classification_output_state.business_knockout_metadata
    failures = []
    knockout_threshold = BUSINESS_L1_ROUTERS_CONFIG.get("KnockoutIntents", 30)
    logger.info(f"Business knockout threshold set to {knockout_threshold}%")

    # Check individual rule confidence scores - knockout if ANY rule is above threshold
    for rule in business_knockout_rules:
        field_name = f"{rule['id']}_confidence"
        score = getattr(l1_data, field_name, 0)
        logger.debug(
            f"Checking rule '{rule['id']}': score={score}, threshold={knockout_threshold}"
        )
        if score >= knockout_threshold:
            logger.debug(
                f"Knockout rule '{rule['id']}' triggered with confidence {score}% >= {knockout_threshold}%"
            )
            failures.append(rule['id'])  # Add the rule ID to failures

    if not failures:
        logger.debug("classification_business_knockout_router: no knockout detected, continuing")
        state.classification_output_state.business_knockout_passes_flag = True
        state.classification_output_state.business_knockout_passes_fail_reasons = []
        #state.classification_output_state.should_continue = True
    else:
        logger.debug(
            f"classification_business_knockout_router: knockout detected due to specific rules: {failures}"
        )
        state.classification_output_state.business_knockout_passes_flag = False
        #state.classification_output_state.should_continue = False
        state.classification_output_state.business_knockout_passes_fail_reasons = failures

    return {}


@log_runtime("classification_technical_knockout_router", "classification_", "classification_output_state")
def classification_technical_knockout_router(state: PayrollState) -> PayrollState:
    logger.info("classification_technical_knockout_router called...")

    technical_data = state.classification_output_state.technical_knockout_metadata
    failures = []
    knockout_threshold = TECHNICAL_L1_ROUTERS_CONFIG.get("KnockoutIntents", 80)

    logger.info(f"Technical knockout threshold set to {knockout_threshold}%")

    # Check displayId count FIRST (programmatically)
    display_ids = state.input_state.displayId
    max_display_ids = RULES_CONFIG.get("technical_knockout", {}).get("max_display_ids", 5)
    
    if isinstance(display_ids, str):
        display_id_count = 1
    elif isinstance(display_ids, list):
        display_id_count = len(display_ids)
    else:
        display_id_count = 0
    
    logger.info(f"Display ID count: {display_id_count}, max allowed: {max_display_ids}")
    
    if display_id_count > max_display_ids:
        logger.info(f"EXCESSIVE_DISPLAY_IDS triggered: {display_id_count} > {max_display_ids}")
        failures.append("EXCESSIVE_DISPLAY_IDS")

    # Get relevant scores for thread logic
    is_thread_score = getattr(technical_data, "IS_THREAD_confidence", 0)
    courtesy_score = getattr(technical_data, "REPLY_TO_COURTESY_REMINDER_EMAIL_confidence", 0)
    recurring_score = getattr(technical_data, "RECURRING_THREAD_confidence", 0)

    # Special thread logic
    if is_thread_score >= knockout_threshold and courtesy_score <= knockout_threshold and recurring_score <= knockout_threshold:
        logger.info(
            f"Unaddressable thread detected: IS_THREAD {is_thread_score} >= {knockout_threshold}, "
            f"REPLY_TO_COURTESY_REMINDER_EMAIL {courtesy_score} <= {knockout_threshold}, "
            f"RECURRING_THREAD {recurring_score} <= {knockout_threshold}"
        )
        failures.append("UNADDRESSABLE_THREAD")
    else:
        logger.info("Thread logic: not an unaddressable thread")

    # All other technical knockout rules (except the ones handled above and EXCESSIVE_DISPLAY_IDS)
    for rule in technical_knockout_rules:
        if rule['id'] in {"IS_THREAD", "REPLY_TO_COURTESY_REMINDER_EMAIL", "RECURRING_THREAD", "EXCESSIVE_DISPLAY_IDS"}:
            continue  # Already handled above or programmatically
        field_name = f"{rule['id']}_confidence"
        score = getattr(technical_data, field_name, 0)
        logger.debug(
            f"Checking technical rule '{rule['id']}': score={score}, threshold={knockout_threshold}"
        )
        if score >= knockout_threshold:
            logger.debug(
                f"Technical knockout rule '{rule['id']}' triggered with confidence {score}% >= {knockout_threshold}%"
            )
            failures.append(rule['id'])

    # Set output state
    if not failures:
        logger.debug("classification_technical_knockout_router: no knockout detected, continuing")
        state.classification_output_state.technical_knockout_passes_flag = True
        state.classification_output_state.technical_knockout_passes_fail_reasons = []
    else:
        logger.debug(
            f"classification_technical_knockout_router: knockout detected due to specific rules: {failures}"
        )
        state.classification_output_state.technical_knockout_passes_flag = False
        state.classification_output_state.technical_knockout_passes_fail_reasons = failures

    return state


def classification_branches_router(state: PayrollState) -> PayrollState:
    """
    This node is used to run the business and technical knockout routers in parallel.
    """
    logger.info("classification_parallel_router called...")

    businness_knockout_passes = state.classification_output_state.business_knockout_passes_flag
    technical_knockout_passes = state.classification_output_state.technical_knockout_passes_flag

    if businness_knockout_passes and technical_knockout_passes:
        logger.debug("Both business and technical knockout routers passed, continuing to request type classification")
        state.classification_output_state.branches_router = True
    else:
        state.classification_output_state.branches_router = False

    # Return the state after both routers have processed
    return state


@log_runtime("classification_request_type", "classification_", "classification_output_state")
async def classification_request_type(state: PayrollState) -> PayrollState:
    logger.info("classification_request_type called...")
    try:
        llm = create_llm_with_structured_output(
            base_llm=settings.LLM(),
            schema=RequestTypeTags,
        )
        
        prompt = PROMPTS["request_type_classification"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]

        result, input_tokens, output_tokens = await invoke_structured_llm_with_retry(llm, message, RequestTypeTags)
        log_llm_usage(
            state=state,
            step_name="classification_request_type",
            llm=llm,
            llm_response=result,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            output_state_attr="classification_output_state"
        )
        logger.debug(f"Request type tagging result: {result}")

        state.classification_output_state.request_type_metadata = result
        return state

    except Exception as e:
        logger.error(f"Error in classification_request_type after retries. Error: {type(e).__name__} - {e}", exc_info=True)
        raise


def _tag_complexity_from_config(tags: RequestTypeTags, config: dict) -> Tuple[str, List[str]]:
    data = tags.dict()
    unknown_count = sum(1 for value in data.values() if value == "unknown")
    # Update UnclearCount in the data
    data["UnclearCount"] = unknown_count
    reasons = []

    # Check hard rules (any match)
    for rule in config.get("hard", {}).get("Any", []):
        if isinstance(rule, dict):
            for field, values in rule.items():
                if field == "UnclearCount":
                    threshold = int(values[0].lstrip(">="))
                    if unknown_count >= threshold:
                        reasons.append(f"Too many unknowns ({unknown_count} >= {threshold})")
                        return "hard", reasons
                elif data.get(field) in values:
                    reasons.append(f"{field}={data.get(field)}")
                    return "hard", reasons

    # Check easy rules (all must match)
    easy_criteria = config.get("easy", {})
    unmatched = []
    for field, values in easy_criteria.items():
        if field in data and data.get(field) not in values:
            unmatched.append(field)

    if not unmatched:
        return "easy", ["All easy criteria matched"]

    # Check medium rules for specific fields that didn't match easy
    medium_criteria = config.get("medium", {})
    medium_matches = []
    
    for field in unmatched:
        if field in medium_criteria and data.get(field) in medium_criteria[field]:
            medium_matches.append(f"{field}={data.get(field)}")
    
    # If all unmatched fields have medium matches, it's medium
    if len(medium_matches) == len(unmatched):
        return "medium", medium_matches
    
    # If not easy or hard, return medium + what triggered medium
    reasons = [f"{field}={data.get(field)} not in easy group" for field in unmatched]
    return "medium", reasons


@log_runtime("classification_complexity", "classification_", "classification_output_state")
def classification_complexity(state: PayrollState) -> PayrollState:
    logger.info("classification_complexity called...")

    try:
        request_tags = state.classification_output_state.request_type_metadata
        if request_tags is None:
            raise ValueError("Missing request_type_metadata in state. Cannot determine complexity.")

        # Apply rules
        complexity_tag, complexity_tag_reasons = _tag_complexity_from_config(request_tags, COMPLEXITY_RULES_CONFIG)
        logger.debug(f"Determined complexity: {complexity_tag}")

        # Package into ComplexityTag model
        complexity_metadata = ComplexityTags(
            ComplexityTag=complexity_tag,
            ComplexityTagReason=complexity_tag_reasons
        )
        # Store in output state
        state.classification_output_state.complexity_metadata = complexity_metadata
        logger.debug(f"classification_complexity returning OutputState: {state}")

        return state

    except Exception as e:
        logger.error(f"Error in classification_complexity: {type(e).__name__} - {e}", exc_info=True)
        raise


@log_runtime("classification_terminate", "classification_", "classification_output_state")
def classification_terminate(state: PayrollState) -> PayrollState:
    """Terminate classification with reason showing which knockout rules triggered."""
    logger.warning("classification_terminate called...")
    
    reasons = []
    
    # Check triage failures
    if hasattr(state.classification_output_state, 'intent_triage_passes_fail_reasons') and state.classification_output_state.intent_triage_passes_fail_reasons:
        reasons.append(f"Triage failed: {', '.join(state.classification_output_state.intent_triage_passes_fail_reasons)}")
    
    # Check business knockout rule failures
    if hasattr(state.classification_output_state, 'business_knockout_passes_fail_reasons') and state.classification_output_state.business_knockout_passes_fail_reasons:
        knockout_threshold = BUSINESS_L1_ROUTERS_CONFIG.get("KnockoutIntents", 80)
        l1_data = getattr(state.classification_output_state, 'business_knockout_metadata', None)
        
        triggered_rules = []
        for rule_id in state.classification_output_state.business_knockout_passes_fail_reasons:
            if rule_id in BUSINESS_KNOCKOUT_RULES_DICT and l1_data:
                score = getattr(l1_data, f"{rule_id}_confidence", 0)
                triggered_rules.append(f"{rule_id} ({score}%)")
        
        if triggered_rules:
            reasons.append(f"Business knockout rules triggered: {', '.join(triggered_rules)}")
    
    # Check technical knockout rule failures
    if hasattr(state.classification_output_state, 'technical_knockout_passes_fail_reasons') and state.classification_output_state.technical_knockout_passes_fail_reasons:
        knockout_threshold = TECHNICAL_L1_ROUTERS_CONFIG.get("KnockoutIntents", 80)
        technical_data = getattr(state.classification_output_state, 'technical_knockout_metadata', None)
        
        triggered_rules = []
        for rule_id in state.classification_output_state.technical_knockout_passes_fail_reasons:
            score = getattr(technical_data, f"{rule_id}_confidence", 0) if technical_data else 0
            triggered_rules.append(f"{rule_id} ({score}%)")
        
        if triggered_rules:
            reasons.append(f"Technical knockout rules triggered: {', '.join(triggered_rules)}")
    
    # Set simple termination reason
    termination_reason = "; ".join(reasons) if reasons else "Classification failed"
    
    state.classification_output_state.termination_reason = termination_reason
    state.classification_output_state.should_continue = False
    state.classification_output_state.termination_node = classification_terminate.__name__
    
    logger.warning(f"Terminated: {termination_reason}")
    return {}


@log_runtime("classification_finish_node", "classification_", "classification_output_state")
def classification_finish_node(state: PayrollState) -> PayrollState:
    logger.info("classification_finishing_node called...")
    return state