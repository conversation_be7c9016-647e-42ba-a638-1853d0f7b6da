import logging

from typing import Any, List, Dict
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log
from langchain_core.messages import convert_to_messages
from langchain_core.language_models import BaseLanguageModel
import tiktoken

from app.cli.logging_utils import setup_logger

logger = setup_logger('payroll_agent.utils.llm_utils')


def pretty_print_message(message, indent=False):
    pretty = message.pretty_repr(html=False)
    if indent:
        for line in pretty.splitlines():
            print(f"\t{line}")
    else:
        print(pretty)


def pretty_print_messages(update, last_message=False):
    """
    Print all node updates in a streamed graph response.
    """
    is_subgraph = False
    # If update comes with namespace info (subgraph), unpack
    if isinstance(update, tuple):
        ns, data = update
        # Skip top-level graph updates if empty namespace
        if len(ns) == 0:
            return
        graph_id = ns[-1].split(':')[0]
        print(f"Update from subgraph {graph_id}:")
        print("\n")
        is_subgraph = True
        update = data

    # Iterate over nodes in this update
    for node_name, node_update in update.items():
        prefix = "\t" if is_subgraph else ""
        print(f"{prefix}Update from node {node_name}:")
        print("\n")
        # Convert LangChain graph messages to standard messages
        msgs = convert_to_messages(node_update.get("messages", []))
        if last_message:
            msgs = msgs[-1:]
        for msg in msgs:
            pretty_print_message(msg, indent=is_subgraph)
        print("\n")


def llm_retry_decorator(
    max_attempts: int = 3,
    min_wait: int = 1,
    max_wait: int = 10,
    multiplier: int = 1
):
    """
    Configurable retry decorator for LLM calls
    
    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries (seconds)
        max_wait: Maximum wait time between retries (seconds)  
        multiplier: Exponential backoff multiplier
    """
    return retry(
        stop=stop_after_attempt(max_attempts),
        wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
        retry=retry_if_exception_type((
            ConnectionError,
            TimeoutError,
            # Add specific OpenAI/LangChain exceptions as needed
            # openai.APIError,
            # openai.RateLimitError,
            Exception  # You can be more specific
        )),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )


async def invoke_llm_with_retry(
    llm: BaseLanguageModel, 
    message: List[Dict[str, str]]
) -> Any:
    """
    Make LLM call with retry logic
    
    Args:
        llm: The language model instance
        message: List of message dictionaries for the LLM
        
    Returns:
        The LLM response
    """
    logger.debug(f"Making LLM call with {len(message)} messages")
    result = await llm.with_retry(
        stop_after_attempt=3,
        retry_if_exception_type=(
            ValueError,
            ConnectionError,
            TimeoutError,
            # Add specific OpenAI/LangChain exceptions as needed
            # openai.APIError,
            # openai.RateLimitError,
            Exception
        ),
        exponential_jitter_params={
            "max": 10,
            "initial": 1
        }
        ).ainvoke(message)
    logger.debug(f"LLM call successful")
    return result


async def invoke_structured_llm_with_retry(
    llm: BaseLanguageModel,
    message: List[Dict[str, str]],
    schema: Any = None
) -> Any:
    """
    Make structured LLM call with retry logic
    
    Args:
        llm: The language model instance (should have structured output configured)
        message: List of message dictionaries for the LLM
        schema: Optional schema for validation
        
    Returns:
        The structured LLM response
    """
    llm = llm.with_retry(
        stop_after_attempt=3,
        retry_if_exception_type=(
            ValueError,
            ConnectionError,
            TimeoutError,
            # Add specific OpenAI/LangChain exceptions as needed
            # openai.APIError,
            # openai.RateLimitError,
            Exception
        ),
        exponential_jitter_params={
            "max": 10,
            "initial": 1
        }
    )
    logger.debug(f"Making structured LLM call with {len(message)} messages")
    if schema:
        logger.debug(f"Using schema: {schema.__name__}")
    

    # Count input tokens
    model_name = getattr(llm, "model_name", None) or getattr(llm, "model", None) or "gpt-4"
    input_text = " ".join([msg["content"] for msg in message])
    input_tokens = count_tokens(input_text, model_name=model_name)

    result = await llm.ainvoke(message)

    # Count output tokens (convert result to string or JSON)
    output_text = str(result)
    output_tokens = count_tokens(output_text, model_name=model_name)

    # You can return or log these values as needed
    return result, input_tokens, output_tokens


def create_llm_with_structured_output(
    base_llm: BaseLanguageModel,
    schema: Any,
) -> BaseLanguageModel:
    """
    Create an LLM instance configured for structured output
    
    Args:
        base_llm: The base language model
        schema: Pydantic model schema for structured output
        
    Returns:
        Configured LLM instance
    """
    logger.debug(f"Creating structured LLM with schema: {schema.__name__}")
    return base_llm.with_structured_output(schema=schema)


def count_tokens(text: str, model_name: str = "gpt-4"):
    try:
        enc = tiktoken.encoding_for_model(model_name)
    except KeyError:
        # Fallback to a default encoding (e.g., cl100k_base for GPT-4/3.5)
        enc = tiktoken.get_encoding("cl100k_base")
    return len(enc.encode(text))