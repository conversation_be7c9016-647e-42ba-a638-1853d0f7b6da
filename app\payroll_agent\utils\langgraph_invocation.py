import os
import asyncio
import random
from datetime import datetime  # Add this import
from langgraph_sdk import get_client
from app.payroll_agent.graph.states.classification import InputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.graph_builder import graph
from app.payroll_agent.utils.funcs import format_payroll_state_response
from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings

logger = setup_logger('langgraph_invocation')

def get_langgraph_client():
    langgraph_api_url = settings.LANGGRAPH_API_URL
    if langgraph_api_url:
        logger.info(f"Connecting to LangGraph API at: {langgraph_api_url}")
        return get_client(url=langgraph_api_url)
    return None

async def invoke_remote_graph(client, prepared_payload):
    try:
        payload_size = len(str(prepared_payload))
        logger.info(f"[{datetime.utcnow().isoformat()}] Prepared payload size: {payload_size} bytes")
        input_state = InputState(**prepared_payload)
        logger.info(f"[{datetime.utcnow().isoformat()}] Generated session ID for API Call tracing: {input_state.x_payx_sid}")
        thread = await client.threads.create()
        thread_id = thread.get("thread_id")
        logger.info(f"[{datetime.utcnow().isoformat()}] Created remote thread: {thread_id} | Thread response: {thread}")

        graph_input = {"input_state": input_state.model_dump()}
        logger.info(f"[{datetime.utcnow().isoformat()}] Invoking remote graph via LangGraph API (non-streaming) with input: {graph_input}")

        max_retries = 3
        base_sleep = 10
        run_timeout = 300

        run_result = None
        for attempt in range(1, max_retries + 1):
            try:
                run_result = await asyncio.wait_for(
                    client.runs.create(
                        thread_id=thread_id,
                        assistant_id="payroll_email_agent",
                        input=graph_input,
                        config={"recursion_limit": 120},
                    ),
                    timeout=run_timeout
                )
                logger.info(f"[{datetime.utcnow().isoformat()}] Run result received on attempt {attempt}: {run_result}")
                break
            except asyncio.TimeoutError:
                logger.warning(f"[{datetime.utcnow().isoformat()}] Attempt {attempt} to run remote graph timed out after {run_timeout} seconds.")
            except Exception as e:
                logger.warning(
                    f"[{datetime.utcnow().isoformat()}] Attempt {attempt} to run remote graph failed: {type(e).__name__}: {e}",
                    exc_info=True
                )
            await asyncio.sleep(base_sleep * attempt)
        else:
            logger.error(f"[{datetime.utcnow().isoformat()}] All attempts to run remote graph failed.")
            return {
                "success": False,
                "processing_method": "remote_langgraph_nonstream",
                "error": f"Failed to run remote graph after {max_retries} attempts",
                "metadata": {"processing_mode": "remote"}
            }

        def is_thread_state_complete(thread_state):
            if not isinstance(thread_state, dict):
                return False
        
            # Must have no further steps
            next_val = thread_state.get("next")
            no_more_steps = isinstance(next_val, list) and len(next_val) == 0
        
            # Must also have actual values
            has_values = thread_state.get("values") not in (None, {}, [])
        
            return no_more_steps and has_values

        # Poll for thread state completion
        logger.info(f"Polling for thread state completion for thread_id: {thread_id}")
        max_poll_retries = 15
        poll_interval = 10
        state_timeout = 30
        thread_state = None
        for attempt in range(1, max_poll_retries + 1):
            try:
                thread_state = await asyncio.wait_for(
                    client.threads.get_state(thread_id),
                    timeout=state_timeout
                )
                logger.debug(f"Thread state response (attempt {attempt}): {thread_state}")
                if is_thread_state_complete(thread_state):
                    logger.info(f"Thread state is complete on attempt {attempt}")
                    break
                else:
                    logger.info(f"Thread state not complete yet (attempt {attempt}), retrying...")
            except asyncio.TimeoutError:
                logger.warning(f"Attempt {attempt} to fetch thread state timed out after {state_timeout} seconds.")
            except Exception as e:
                logger.warning(
                    f"Attempt {attempt} to fetch thread state failed: {type(e).__name__}: {e}"
                )
            await asyncio.sleep(poll_interval)
        else:
            logger.error("All attempts to poll for complete thread state failed.")
            return {
                "success": False,
                "processing_method": "remote_langgraph_nonstream",
                "error": "Failed to fetch complete thread state after multiple attempts",
                "metadata": {"processing_mode": "remote"}
            }

        if is_thread_state_complete(thread_state):
            result_state = PayrollState(**thread_state["values"])
            formatted_result = format_payroll_state_response(result_state)
            logger.info(f"Completed remote graph invocation")
            return {
                "success": True,
                "processing_method": "remote_langgraph_nonstream",
                "result_state": result_state,
                "formatted_result": formatted_result,
                "metadata": {"processing_mode": "remote"}
            }
        logger.error(f"Unexpected response format from LangGraph API: {thread_state}")
        return {
            "success": False,
            "processing_method": "remote_langgraph_nonstream",
            "error": "Unexpected response format from LangGraph API",
            "metadata": {"processing_mode": "remote"}
        }
    except Exception as e:
        logger.error(f"Remote LangGraph invocation failed: {type(e).__name__}: {e}", exc_info=True)
        return {
            "success": False,
            "processing_method": "remote_langgraph_nonstream",
            "error": str(e),
            "metadata": {"processing_mode": "remote"}
        }

async def invoke_local_graph(prepared_payload):
    try:
        input_state = InputState(**prepared_payload)
        logger.info(f"Generated session ID for MCP tracing: {input_state.x_payx_sid}")
        payroll_state = PayrollState(input_state=input_state)
        logger.info("Invoking local LangGraph processing")
        result_state = await graph.ainvoke(payroll_state, config={"recursion_limit": 120})
        formatted_result = format_payroll_state_response(result_state)
        return {
            "success": True,
            "processing_method": "local_langgraph",
            "result_state": result_state,
            "formatted_result": formatted_result,
            "metadata": {"processing_mode": "local"}
        }
    except Exception as e:
        logger.error(f"Local LangGraph processing failed: {e}", exc_info=True)
        return {
            "success": False,
            "processing_method": "local_langgraph",
            "error": str(e),
            "metadata": {"processing_mode": "local"}
        }
