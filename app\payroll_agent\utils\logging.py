import time
import asyncio
from functools import wraps
from app.cli.logging_utils import setup_logger

logger = setup_logger('payroll_agent.utils.logging')

def log_runtime(step_name=None, graph_prefix=None, output_state_attr=None):
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            name = step_name or func.__name__
            start = time.perf_counter()
            result = await func(*args, **kwargs)
            elapsed = time.perf_counter() - start
            logger.debug(f"Step '{name}' took {elapsed:.3f} seconds")
            if args:
                state = args[0]
                output_state = None
                if output_state_attr and hasattr(state, output_state_attr):
                    output_state = getattr(state, output_state_attr, None)
                else:
                    for attr in dir(state):
                        if attr.endswith("_output_state"):
                            output_state = getattr(state, attr, None)
                            break
                if output_state is not None:
                    if not hasattr(output_state, "run_time") or output_state.run_time is None:
                        output_state.run_time = {}
                    # Only store if prefix matches
                    if graph_prefix is None or name.startswith(graph_prefix):
                        output_state.run_time[name] = elapsed
            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            name = step_name or func.__name__
            start = time.perf_counter()
            result = func(*args, **kwargs)
            elapsed = time.perf_counter() - start
            logger.debug(f"Step '{name}' took {elapsed:.3f} seconds")
            if args:
                state = args[0]
                output_state = None
                if output_state_attr and hasattr(state, output_state_attr):
                    output_state = getattr(state, output_state_attr, None)
                else:
                    for attr in dir(state):
                        if attr.endswith("_output_state"):
                            output_state = getattr(state, attr, None)
                            break
                if output_state is not None:
                    if not hasattr(output_state, "run_time") or output_state.run_time is None:
                        output_state.run_time = {}
                    if graph_prefix is None or name.startswith(graph_prefix):
                        output_state.run_time[name] = elapsed
            return result

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    return decorator


def log_llm_usage(state, step_name, llm, llm_response=None, input_tokens=None, output_tokens=None, output_state_attr=None):
    """Log LLM model and token usage to the output state."""
    # Try to get a simple model name
    model_name = None
    for attr in ["model_name", "model"]:
        if hasattr(llm, attr):
            model_name = getattr(llm, attr)
            break
    if not model_name and hasattr(llm, "kwargs"):
        model_name = llm.kwargs.get("model_name")
    if not model_name and hasattr(llm, "bound") and hasattr(llm.bound, "model_name"):
        model_name = getattr(llm.bound, "model_name")
    if not model_name:
        llm_str = str(llm)
        import re
        match = re.search(r"model_name='([^']+)'", llm_str)
        if match:
            model_name = match.group(1)
        else:
            model_name = llm_str

    usage = {
        "model": model_name,
        "input_tokens_estimated": input_tokens,
        "output_tokens_estimated": output_tokens
    }

    # Try to extract actual token usage if available
    if hasattr(llm_response, "usage"):
        usage["input_tokens_actual"] = getattr(llm_response.usage, "prompt_tokens", None)
        usage["output_tokens_actual"] = getattr(llm_response.usage, "completion_tokens", None)
    elif isinstance(llm_response, dict) and "usage" in llm_response:
        usage["input_tokens_actual"] = llm_response["usage"].get("prompt_tokens")
        usage["output_tokens_actual"] = llm_response["usage"].get("completion_tokens")

    # Store in output state
    output_state = None
    if output_state_attr and hasattr(state, output_state_attr):
        output_state = getattr(state, output_state_attr, None)
    else:
        for attr in dir(state):
            if attr.endswith("_output_state"):
                output_state = getattr(state, attr, None)
                break
    if output_state is not None:
        if not hasattr(output_state, "llm_usage") or output_state.llm_usage is None:
            output_state.llm_usage = {}
        output_state.llm_usage[step_name] = usage