import json

from pathlib import Path
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.prebuilt import create_react_agent

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.validation import ValidatedPayrollEntries
from app.payroll_agent.utils.logging import log_runtime, log_llm_usage
from app.payroll_agent.utils.llm_utils import count_tokens
from app.payroll_agent.utils.knockouts import load_knockout_library, create_knockout_model, create_knockout_rules_text_for_batch

from app.payroll_agent.utils.mcp import get_mcp_service


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("validation")
ROUTERS_CONFIG = load_routers_config('routers_config')['validation']
late_stage_knockout_rules = load_knockout_library(Path(__file__).parent.parent / "libraries" / "late_stage_knockout_library.yaml")
lateStageKnockouts = create_knockout_model(late_stage_knockout_rules, model_name="LateStageKnockouts")
late_stage_rules_text = create_knockout_rules_text_for_batch(late_stage_knockout_rules)

# Convert knockout rules to lookup and create rules text for prompt
LATE_STAGE_KNOCKOUT_RULES_DICT = {rule['id']: rule for rule in late_stage_knockout_rules}
logger.info(f"Loaded {len(LATE_STAGE_KNOCKOUT_RULES_DICT)} active business knockout rules: {list(LATE_STAGE_KNOCKOUT_RULES_DICT.keys())}")


async def validation_create_agent(state: PayrollState):
    """
    Initializes and returns a payroll extraction React agent
    """
    logger.info("Starting validation_create_agent")
    try:

        # set mcp client
        logger.debug("Creating MCPService client")
        mcp_service = get_mcp_service()
        logger.debug("MCPService client created")

        # tools
        # get_pay_components = await mcp_service.get_tool(server_name="paychex", tool_name="paychex_get_pay_components")
        validate_working_days_tool = await mcp_service.get_tool(server_name="paychex", tool_name="paychex_validate_check_date")
        validate_salary_tool = await mcp_service.get_tool(server_name="paychex", tool_name="paychex_validate_worker_payment")

        tools = [
            validate_working_days_tool,
            validate_salary_tool,
        ]

        # format prompt
        date = state.input_state.timestamp
        check_date = state.company_worker_lookup_output_state.payperiod.checkDate
        payroll_commands = state.payroll_processing_output_state.processed_payrolls
        payroll_amounts = [
            {"worker_id": d.worker_id, "amount": d.amount} for d in payroll_commands if d.model_dump().get("amount",None) is not None
        ]

        logger.debug("Formatting agent prompt using template")
        base_prompt = PROMPTS["late_stage_knockout"].format(
            date = date,
            check_date = check_date,
            payroll_amounts = json.dumps(payroll_amounts),
            knockout_rules_text = late_stage_rules_text,
            ScenarioRulesThreshold = ROUTERS_CONFIG.get("KnockoutIntents", 85)
        )

        # set llm
        logger.debug("Setting up LLM model")
        llm = settings.LLM(reasoning_effort="low").bind_tools(tools)
        logger.debug(f"LLM initialized: {llm}")

        # create agent
        logger.debug("Creating React agent 'val_agent'")
        val_agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=base_prompt,
            name="val_agent",
            response_format=lateStageKnockouts,
        )

        logger.debug(f"Validation agent created successfully: {val_agent}", )
        return val_agent, llm
    except Exception as e:
        logger.error(f"Failed to create validation agent: {type(e).__name__} - {e}", exc_info=True)
        raise e


@log_runtime("validation_run_agent", "validation_", "validation_output_state")
async def validation_run_agent(state: PayrollState) -> PayrollState:
    logger.info("Starting validation_run_agent")

    # Call the agent
    email_content = state.input_state.EmailContent
    agent, llm = await validation_create_agent(state)

    agent_input = {
        "messages":
            HumanMessage(content=f'Email content: "{email_content}"')
    }

    response = await agent.ainvoke(agent_input)


    for message in response["messages"]:
        message.pretty_print()


    # token count
    input_text = agent_input["messages"].content
    input_tokens = count_tokens(input_text, model_name=getattr(llm, "model_name", "gpt-4"))
    output_text = str(response["structured_response"])
    output_tokens = count_tokens(output_text, model_name=getattr(llm, "model_name", "gpt-4"))

    log_llm_usage(
        state=state,
        step_name="validation_run_agent",
        llm=llm,
        llm_response=response,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        output_state_attr="validation_output_state"
    )


    # Validate with Pydantic
    result = response["structured_response"]

    # Save into your state and return
    state.validation_output_state.validation_output = result

    # detected rules
    result = result.model_dump()
    detected_rules = result.get('DetectedKnockoutRules',[])
    if detected_rules:
        ## check rules above threshold
        rules = {rule :
            dict(
                confidence=result[rule+'_confidence'],
                reason=result[rule+'_confidence_reasoning']
                )
            for rule in detected_rules if result[rule+'_confidence'] >= ROUTERS_CONFIG.get("KnockoutIntents", 86) }

        state.validation_output_state.DetectedKnockoutRules = rules

    return state


@log_runtime("validation_router", "validation_", "validation_output_state")
def validation_router(state: PayrollState) -> PayrollState:
    logger.info("validation_router called, passing through state")

    # checks
    conditions = [
        state.validation_output_state.DetectedKnockoutRules is None,
        ]

    #check all conditions are met
    if all(conditions):
        state.validation_output_state.should_continue = True
        logger.info("Payrolls validated successfully")
        state.validation_output_state.validated_payroll_entries = ValidatedPayrollEntries(
            success=True,
            validated_entries=state.payroll_processing_output_state.processed_payrolls)
    else:
        error_message = f"Payroll validation failed, error: {state.validation_output_state.DetectedKnockoutRules} "
        logger.warning(error_message)
        state.validation_output_state.termination_reason = error_message
        state.validation_output_state.validated_payroll_entries = ValidatedPayrollEntries(success=False,error=error_message)
    return state


@log_runtime("validation_terminate", "validation_", "validation_output_state")
def validation_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("validation_terminate called...")
    reason = state.validation_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.validation_output_state.should_continue = False
    state.validation_output_state.termination_node = (
        validation_terminate.__name__
    )
    logger.debug(f"validation_terminate returning PayrollState: {state}")
    return state


@log_runtime("validation_finishing_node", "validation_",
             "validation_output_state")
def validation_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("validation_finishing_node called...")
    return state
