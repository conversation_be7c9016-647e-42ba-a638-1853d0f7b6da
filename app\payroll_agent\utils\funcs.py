import yaml

from pathlib import Path
from datetime import datetime, timezone
from dateutil.relativedelta import relativedelta
from dateutil.parser import isoparse

from app.api.models.input import UpstreamModel
from app.cli.logging_utils import setup_logger
from app.payroll_agent.graph.states.common import PayrollState

logger = setup_logger(__name__)


def first_day_of_month(date_str: str) -> str:
    """
    Convert an ISO date string to the first day of its month.

    Args:
        date_str: date in "YYYY-MM-DD" format

    Returns:
        A string "YYYY-MM-01"
    """
    dt = datetime.fromisoformat(date_str)
    return dt.replace(day=1).strftime("%Y-%m-%d")


def plus_minus_period_iso(iso_str, months=0, days=0):
    """
    Add/subtract months and days to an ISO datetime string.
    Handles formats:
        - YYYY-MM-DDTHH:MM:SS(.microseconds)?
        - YYYY-MM-DDTHH:MM:SS(.microseconds)?Z
    Returns ISO string in 'YYYY-MM-DDTHH:MM:SSZ' format (UTC, no microseconds).
    """
    # Use dateutil to parse both naive and Z/offset formats
    dt = isoparse(iso_str)
    # Convert to UTC if timezone-aware
    if dt.tzinfo is not None:
        dt = dt.astimezone(timezone.utc).replace(tzinfo=None)
    # Add months/days
    dt_new = dt + relativedelta(months=months, days=days)
    # Format as 'YYYY-MM-DDTHH:MM:SSZ' (this is the format expected by EAPI)
    return dt_new.strftime('%Y-%m-%dT%H:%M:%SZ')


def days_between(d1_str: str, d2_str: str) -> int:
    """
    Return the number of days between two ISO date strings (YYYY-MM-DD).
    """
    d1 = datetime.fromisoformat(d1_str).date()
    d2 = datetime.fromisoformat(d2_str).date()
    return abs((d2 - d1).days)


def convert_to_iso_date(date_str: str) -> str:
    """
    Convert a date string to ISO format (YYYY-MM-DD).

    Args:
        date_str: Date string in any format that can be parsed by datetime

    Returns:
        str: Date in ISO format (YYYY-MM-DD)
    """
    try:
        dt = datetime.fromisoformat(date_str)
        return dt.strftime("%Y-%m-%d")
    except ValueError as e:
        logger.error(f"Failed to convert date '{date_str}' to ISO format: {e}")
        raise ValueError(f"Invalid date format: {date_str}") from e


def create_name_mapping(state: PayrollState) -> dict:
    """
    Create mapping from email names (extracted_name) to full CA names (worker_name).
    
    Args:
        state: PayrollState containing company worker lookup results
        
    Returns:
        dict: Mapping of extracted names to full worker names
        Example: {"myself": "Paul A Reese", "John": "John Smith"}
    """
    logger.debug("Creating name mapping from company worker lookup results")
    name_mapping = {}
    
    # Get the LLM workers match results
    llm_workers_match = state.company_worker_lookup_output_state.llm_workers_match
    
    if llm_workers_match and llm_workers_match.workers:
        for worker in llm_workers_match.workers:
            if worker.closest_match_successful:
                name_mapping[worker.extracted_name] = worker.worker_name
                logger.debug(f"Mapped '{worker.extracted_name}' -> '{worker.worker_name}'")
    
    logger.debug(f"Created name mapping with {len(name_mapping)} entries: {name_mapping}")
    return name_mapping


def format_payroll_state_response(result_state):
    logger.info("Starting to format payroll state response")

    # suppose result_state is the AddableValuesDict you got back:
    state_map = dict(result_state)
    logger.debug(f"Converted result_state to dict: {state_map}")

    # Pydantic v2: use `model_validate` to parse a mapping into your model
    try:
        output_model = PayrollState.model_validate(state_map)
        logger.debug(f"Validated PayrollState model: {output_model}")
    except Exception as e:
        logger.error(f"Failed to validate PayrollState: {type(e).__name__} - {e}", exc_info=True)
        raise

    # now you can dump to a plain dict (recursively converts all nested BaseModels):
    plain_dict = output_model.model_dump(by_alias=True, exclude_none=True)
    logger.debug(f"Dumped model to plain dict: {plain_dict}")

    # add completion timestamp, and ticket id
    started_at = plain_dict['input_state'].get('received_at')
    finished_at = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    ticket_id = plain_dict['input_state'].get('IMSEPCID')

    plain_dict['started_at'] = started_at
    plain_dict['finished_at'] = finished_at
    plain_dict['ticket_id'] = ticket_id
    logger.info(f"Timestamps and ticket added: started_at={started_at}, finished_at={finished_at}, ticket_id={ticket_id}")

    ## Add termination reason at level
    termination_dict = {}
    
    # Check if this is a skeleton/failed state (empty states indicate failure)
    is_skeleton_failure = (
        (not plain_dict.get('classification_output_state') or plain_dict.get('classification_output_state') == {}) and
        (not plain_dict.get('company_worker_lookup_output_state') or plain_dict.get('company_worker_lookup_output_state') == {}) and
        (not plain_dict.get('payroll_processing_output_state') or plain_dict.get('payroll_processing_output_state') == {})
    )
    
    logger.debug(f"Skeleton failure check: classification={plain_dict.get('classification_output_state')}, company_lookup={plain_dict.get('company_worker_lookup_output_state')}, payroll={plain_dict.get('payroll_processing_output_state')}")
    logger.debug(f"Is skeleton failure: {is_skeleton_failure}")
    
    if is_skeleton_failure:
        # This is a skeleton failure case - set termination dict and skip normal termination check
        termination_dict['termination_graph'] = 'processing_error'
        termination_dict['termination_reason'] = 'Processing failed before completion'
        termination_dict['termination_node'] = 'orchestrator'
        logger.info("Skeleton failure detected - setting failure flags")
    else:
        # Check for normal termination in processing states ONLY if not skeleton failure
        for key, value in plain_dict.items():
            logger.debug(f"Checking for termination in section '{key}': {value}")
            if isinstance(value, dict) and value.get('should_continue') is False:
                termination_dict['termination_graph'] = key
                termination_dict['termination_reason'] = value.get('termination_reason')
                termination_dict['termination_node'] = value.get('termination_node')
                logger.info(f"Termination detected in graph '{key}': reason={termination_dict['termination_reason']}, node={termination_dict['termination_node']}")
                break

    # Set flags based on termination or failure
    # Check for ANY termination indicator, not just termination_node
    has_termination = bool(termination_dict.get("termination_graph")) or bool(termination_dict.get("termination_node"))
    termination_flag = has_termination or is_skeleton_failure
    successful_flag = not termination_flag
    
    # Log the termination detection logic
    logger.info(f"Termination detection: termination_graph={termination_dict.get('termination_graph')}, "
                f"termination_node={termination_dict.get('termination_node')}, "
                f"has_termination={has_termination}, is_skeleton_failure={is_skeleton_failure}")
    
    plain_dict['termination_flag'] = termination_flag
    plain_dict['successful_flag'] = successful_flag

    if termination_dict:
        plain_dict['termination_dict'] = termination_dict
    else:
        plain_dict['termination_dict'] = {}
        logger.info("No termination detected in any subgraph")

    logger.info(f"Final flags set: successful_flag={successful_flag}, termination_flag={termination_flag}")
    logger.debug("Finished formatting payroll state response")
    return plain_dict


def load_routers_config(name: str) -> dict:
    logger.debug("Loading routers configuration")
    try:
        # Build path to the YAML config file
        path = Path(__file__).resolve().parents[1] / "config" / f"{name}.yml"
        logger.debug(f"Resolved config path for '{name}': {path}")

        # Read and parse the YAML
        with open(path, "r") as f:
            config = yaml.safe_load(f)
            logger.debug(f"Successfully loaded config '{name}' ({len(config)} top-level keys)")
            return config

    except FileNotFoundError as e:
        logger.error(f"Config file not found for '{name}' at {path}: {type(e).__name__} - {e}", exc_info=True)
        raise
    except yaml.YAMLError as ye:
        logger.error(f"Error parsing YAML for '{name}': {ye}: {type(ye).__name__} - {ye}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error loading config '{name}': {e}: {type(e).__name__} - {e}", exc_info=True)
        raise


def parse_upstream_model(upstream_model: UpstreamModel):
    """
    Parse the upstream model dictionary into a PayrollState instance.
    This is used to convert the input model from the API into the state format used by the graph.
    """
    model_dict = dict()
    logger.debug("Parsing upstream model...")
    try:
        model_dict['displayId'] = upstream_model.displayId
        model_dict['ingestId'] = upstream_model.get('ingestId', '')
        model_dict['uid'] = upstream_model.get('comment',{}).get('uid')
        model_dict['id'] = upstream_model.get('comment',{}).get('id')
        model_dict['timestamp'] = upstream_model.get('comment',{}).get('timestamp')
        model_dict['CustomerDomain'] = upstream_model.get('comment',{}).get('user_properties',{}).get('string:Sender Domain', '')
        model_dict['EmailContent'] = upstream_model.get('comment',{}).messages[0].body.text
        model_dict['SourceAddress'] = upstream_model.get('comment',{}).get('user_properties',{}).get('string:Sender', '')
        model_dict['DestinationAddress'] = upstream_model.get('comment',{}).messages[0].get('to', [])
        model_dict['Subject'] = upstream_model.get('comment',{}).messages[0].get('subject',{}).get('text')
        model_dict['Attachments'] = upstream_model.get('comment',{}).get('attachments', [])
        model_dict['received_at'] = upstream_model.received_at

        return model_dict
    except Exception as e:
        logger.error(f"Failed to parse upstream model: {type(e).__name__} - {e}", exc_info=True)
        raise


def name_map(llm_workers_match):
    """
    Map a name to a worker ID using the provided list of workers.

    Args:
        name (str): The name to map.
        llm_workers_match (List[Dict[str, Any]]): List of workers with their details.

    Returns:
        str: The worker ID if found, otherwise None.
    """
    llm_workers_match = llm_workers_match.workers if hasattr(llm_workers_match, 'workers') else []
    mapping = dict()
    for worker in llm_workers_match:
        worker_name = worker.extracted_name
        worker_id = worker.worker_number
        db_name = worker.worker_name
        if worker_name and worker_id and db_name:
            mapping[worker_name] = dict(worker_id=worker_id, db_name=db_name)
    return mapping


def full_name(x):
    if x['name'].get('middleName',''):
        name = x['name'].get('givenName', '') + ' ' + x['name'].get('middleName', '') + ' ' + x['name'].get('familyName', '')
    else:
        name = x['name'].get('givenName', '') + ' ' + x['name'].get('familyName', '')
    return name

