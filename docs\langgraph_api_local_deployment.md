# How to Test LangGraph API Deployment Locally

This guide explains how to build, deploy, and test the LangGraph API locally using <PERSON><PERSON> and docker-compose.

---

## Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- `langgraph` CLI installed ([Install Guide](https://langchain-ai.github.io/langgraph/cloud/reference/cli/#installation))
- `langgraph.json` present in your project root (see example below)

---

## 1. Prepare `langgraph.json`

Example `langgraph.json`:
```json
{
  "dependencies": ["."],
  "graphs": {
    "payroll_email_agent": "./app/payroll_agent/graph/graph_builder.py:graph",
    "account_lookup": "./app/payroll_agent/graph/graph_builder.py:account_lookup",
    "classification": "./app/payroll_agent/graph/graph_builder.py:classification",
    "payroll_extraction": "./app/payroll_agent/graph/graph_builder.py:payroll_extraction",
    "validation": "./app/payroll_agent/graph/graph_builder.py:validation",
    "execution": "./app/payroll_agent/graph/graph_builder.py:execution",
    "release": "./app/payroll_agent/graph/graph_builder.py:release"
  },
  "pip_installer": "uv",
  "python_version": "3.11",
  "env": ".env"
}
```
- See [LangGraph CLI Reference](https://langchain-ai.github.io/langgraph/cloud/reference/cli/#build) for more details.

---

## 2. Building the LangGraph API Container

You can build your local image using Docker Compose (which uses your Dockerfile and includes your graphs and dependencies):

```bash
docker compose -f docker-compose-langgraph-api.yml build
```

This ensures your local image includes your graphs and dependencies as defined in your repository.

## Updating the LangGraph API Dockerfile

To create or update the Dockerfile for the LangGraph API, run:

```bash
langgraph dockerfile Dockerfile
```

This command will update `Dockerfile` for both local and cloud deployments, using your `langgraph.json` for graph configuration.

**Note:** Your `docker-compose-langgraph-api.yml` now uses the `build:` directive for `langgraph-api`:

```yaml
langgraph-api:
  build: .
  ports:
    - "8123:8000"
  depends_on:
    ...
```

This ensures any code or dependency changes are included in the build.

---

## 3. Start the Local Deployment

From your project root, run:

```bash
docker-compose -f docker-compose-langgraph-api.yml up
```

This will start the API and make it accessible at:
- **http://localhost:8123**

---

## 4. Test the Deployment

Once the API is running, you can access the following endpoints and tools:

**Quick Access:**
- **API:** [http://localhost:8123](http://localhost:8123)
- **Docs:** [http://localhost:8123/docs](http://localhost:8123/docs)
- **LangGraph Studio:** [Open in Studio](https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:8123)

You can use these links to verify the API is up, explore the documentation, and connect with LangGraph Studio for interactive testing.

You can now send requests to the API using `curl`, Postman, or your preferred HTTP client. Example:

```bash
curl http://localhost:8123/docs
```

This should return the OpenAPI/Swagger UI for the LangGraph API.

---

## References
- [LangGraph CLI Reference](https://langchain-ai.github.io/langgraph/cloud/reference/cli/#build)
- [Deployment Options](https://github.com/langchain-ai/langgraph/blob/9b9bf88aeec379fa25ec84d004de04349d21e69c/docs/docs/concepts/deployment_options.md)
- [LangChain Academy Example](https://github.com/langchain-ai/langchain-academy/blob/main/module-6/creating.ipynb)

---

If you encounter issues, check the logs from Docker Compose and ensure your `langgraph.json` is valid and all dependencies are installed correctly.
