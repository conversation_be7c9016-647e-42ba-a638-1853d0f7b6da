import json
from unittest.mock import AsyncMock, patch

import pytest

from app.payroll_agent.models.company_worker_lookup import MatchedPayPeriod
from app.payroll_agent.nodes.company_worker_lookup import (
    company_worker_lookup_get_payperiodID,
)


class DummyOutputState:
    def __init__(self):
        self.pay_periods = None
        self.payperiod = None
        self.payperiodID = None


class DummyInputState:
    def __init__(self):
        self.timestamp = "2025-09-30T14:14:34.363000"
        self.companyID = "12345"
        self.EmailContent = "Test email content"
        self.x_payx_sid = "test-session-id-123"  # Add test session ID


class DummyState:
    def __init__(self):
        self.input_state = DummyInputState()
        self.company_worker_lookup_output_state = DummyOutputState()


@pytest.mark.asyncio
@patch("app.payroll_agent.nodes.company_worker_lookup.get_mcp_service")
@patch(
    "app.payroll_agent.nodes.company_worker_lookup.company_worker_lookup_get_pay_period",
    new_callable=AsyncMock,
)
async def test_company_worker_lookup_get_payperiodID(
    mock_get_pay_period, mock_get_mcp_service
):
    # Mock MCP service response
    mock_service = AsyncMock()
    mock_service.call_mcp_tool.return_value = json.dumps(
        {"ok": True, "content": [{"payPeriodId": "p1", "checkDate": "2025-09-30"}]}
    )
    mock_get_mcp_service.return_value = mock_service

    # Mock get_pay_period response
    mock_get_pay_period.return_value = MatchedPayPeriod(
        payPeriodId="p1", status="SUCCESS", checkDate="2025-09-30", reason="Test reason"
    )

    state = DummyState()
    result = await company_worker_lookup_get_payperiodID(state)

    output = state.company_worker_lookup_output_state
    assert output.pay_periods is not None
    assert output.payperiod is not None
    assert output.payperiodID == "p1"
    assert result == {}

    # Check the arguments passed to call_mcp_tool
    args, kwargs = mock_service.call_mcp_tool.call_args
    tool_input = kwargs.get("tool_input") if kwargs else args[2]
    from_date = tool_input["kwargs"]["from_"]
    to_date = tool_input["kwargs"]["to"]
    assert from_date == "2025-08-01T00:00:00Z"  # Verify exact from_date value
    assert to_date == "2025-11-14T14:14:34Z"  # Verify exact to_date value
    assert tool_input["kwargs"]["status"] == "INITIAL"
    
    # Verify session ID is passed correctly
    assert "session_id" in kwargs, "Session ID should be passed to call_mcp_tool"
    assert kwargs["session_id"] == state.input_state.x_payx_sid


# Second test: timestamp in different format
@pytest.mark.asyncio
@patch("app.payroll_agent.nodes.company_worker_lookup.get_mcp_service")
@patch(
    "app.payroll_agent.nodes.company_worker_lookup.company_worker_lookup_get_pay_period",
    new_callable=AsyncMock,
)
async def test_company_worker_lookup_get_payperiodID_iso_z(
    mock_get_pay_period, mock_get_mcp_service
):
    mock_service = AsyncMock()
    mock_service.call_mcp_tool.return_value = json.dumps(
        {"ok": True, "content": [{"payPeriodId": "p1", "checkDate": "2025-09-30"}]}
    )
    mock_get_mcp_service.return_value = mock_service

    mock_get_pay_period.return_value = MatchedPayPeriod(
        payPeriodId="p1", status="SUCCESS", checkDate="2025-09-30", reason="Test reason"
    )

    state = DummyState()
    state.input_state.timestamp = "2025-09-30T14:14:34Z"  # ISO format with Z
    await company_worker_lookup_get_payperiodID(state)

    args, kwargs = mock_service.call_mcp_tool.call_args
    tool_input = kwargs.get("tool_input") if kwargs else args[2]
    from_date = tool_input["kwargs"]["from_"]
    to_date = tool_input["kwargs"]["to"]
    assert from_date == "2025-08-01T00:00:00Z"
    assert to_date == "2025-11-14T14:14:34Z"
    
    # Verify session ID is passed correctly
    assert "session_id" in kwargs, "Session ID should be passed to call_mcp_tool"
    assert kwargs["session_id"] == state.input_state.x_payx_sid
