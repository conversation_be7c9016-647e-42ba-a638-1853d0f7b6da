from typing import Dict, List, Optional, Union,  Literal
from pydantic import BaseModel, Field

class SenderEmailMetadata(BaseModel):
    """Key details that appear in an automated payroll-notification email."""
    sender_name: Optional[str] = Field(default='', description="Human-readable name, e.g. Acme Payroll Bot")
    company_name: Optional[str] = Field(default='', description="Company issuing the email")
    sender_phone_number: Optional[str] = Field(default='', description="Sender's phone number")
    sender_address: Optional[str] = Field(default='', description="Sender's address")
    employees_names: list[str] = Field(default=[], description="List of employees names present in the email")


class ValidateSenderMetadata(BaseModel):
    """Key details that appear in an automated payroll-notification email."""
    valid: bool = Field(..., description="Whether the sender's info is validated")
    errors: Optional[List[Dict[str, str]]] = Field(default_factory=list, description="List of any errors found")


class Worker(BaseModel):
    """
    Represents a worker extraction and matching result from email content.
    """
    extracted_name: str = Field(
        ...,
    
        description="Name as extracted from the email text (e.g., 'myself', '<PERSON>', 'J. <PERSON>')"
    )
    closest_match: str = Field(
        ..., 
        description="Closest matching worker name from database or 'No Match'"
    )
    closest_match_confidence: int = Field(
        ..., 
        ge=0, 
        le=100,
        description="Confidence level 0-100 based on name matching algorithm"
    )
    closest_match_successful: bool = Field(
        ..., 
        description="True if confidence >= threshold (usually 70%), False otherwise"
    )
    worker_name: str = Field(
        ..., 
        description="Full worker name from database (same as closest_match for successful matches)"
    )
    worker_number: Optional[Union[str, int]] = Field(
        None, 
        description="Company worker ID (can be string or integer), null if no match"
    )


class WorkerMatchSummary(BaseModel):
    """Summary statistics for worker matching results."""
    total_extracted: int = Field(..., description="Total number of names extracted from email")
    successful_matches: int = Field(..., description="Number of matches above confidence threshold")
    confidence_threshold: int = Field(default=70, description="Confidence threshold used for success determination")


class WorkersMatch(BaseModel):
    """
    Complete result of worker name extraction and matching process.
    
    Contains all workers found in email with their matching details and summary statistics.
    """
    workers: List[Worker] = Field(
        default_factory=list, 
        description="List of all workers mentioned in email with matching details"
    )
    summary: WorkerMatchSummary = Field(
        default_factory=WorkerMatchSummary,
        description="Summary statistics for the matching process"
    )


class PayPeriod(BaseModel):
    pay_period_id: Optional[str] = Field(None, alias="payPeriodId")
    start_date: Optional[str] = Field(None, alias="startDate")
    end_date: Optional[str] = Field(None, alias="endDate")
    check_date: Optional[str] = Field(None, alias="checkDate")
    submit_by_date: Optional[str] = Field(None, alias="submitByDate")

    class Config:
        validate_by_name = True
        extra = "ignore"

class CheckDate(BaseModel):
    """Date to release the check."""
    checkDate: Optional[str] = Field(None, description="Date to release/process the check")
    startDate: Optional[str] = Field(None, description="Start date of the pay period")
    endDate: Optional[str] = Field(None, description="End date of the pay period")
    confidence: int = Field(..., ge=0, le=100,description="Confidence level 0-100 based on name matching algorithm")
    reason: str = Field(..., description="Reason for choosing this date")


class MatchedPayPeriod(BaseModel):
    status: Literal["SUCCESS", "FAILURE"] = Field(..., description="Status of the matched pay period")
    payPeriodId: Optional[str] = Field(None, description="Unique identifier for the matched pay period")
    checkDate: Optional[str] = Field(None, description="Check date of the matched pay period in ISO format (YYYY-MM-DD)")
    startDate: Optional[str] = Field(None, description="Start date of the matched pay period in ISO format (YYYY-MM-DD)")
    endDate: Optional[str] = Field(None, description="End date of the matched pay period in ISO format (YYYY-MM-DD)")
    submitByDate: Optional[str] = Field(None, description="Submit by date of the matched pay period in ISO format (YYYY-MM-DD)")
    reason: str = Field(..., description="Reason for choosing this pay period")

class CompanyScore(BaseModel):
    company_id: str = Field(..., description="Company ID being scored")
    reasoning: str = Field(..., description="Reasoning for this confidence score")
    confidence: int = Field(..., description="Confidence level (0-100) that this company matches the email")

class CompanyScoreList(BaseModel):
    scores: list[CompanyScore] = Field(..., description="List of company scores")
    scores: list[CompanyScore] = Field(..., description="List of company scores")

class AttachmentHandwrittenAnalysis(BaseModel):
    """Result returned by the LLM when analyzing an attachment for handwritten content."""
    file: str = Field(..., description="Filename or page identifier for the analyzed attachment")
    reasoning: Optional[str] = Field("", description="Concise reasoning/explanation for the confidence score")
    confidence: int = Field(..., ge=0, le=100, description="Confidence (0-100) that the attachment contains handwritten content")