import uuid
import asyncio

from typing import List, Dict, Any

from langchain_core.tools import Tool
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.tools.structured import StructuredTool

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt, format_base_agent_prompt
from app.payroll_agent.utils.funcs import load_routers_config
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.payroll_processing import PayrollResponse, PayAmountInput, PayAmountOutput
from app.payroll_agent.utils.logging import log_runtime, log_llm_usage
from app.payroll_agent.utils.llm_utils import count_tokens, pretty_print_messages

from app.payroll_agent.utils.mcp import get_mcp_service


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("payroll_processing")
ROUTERS_CONFIG = load_routers_config('routers_config')['payroll_processing']


@log_runtime("create_payroll_agent", "payroll_processing_", "payroll_processing_output_state")
async def create_payroll_agent(state: PayrollState, agent_number: int = 1):
    """
    Creates a payroll extraction agent with the specified agent number.
    This function is used to create agents for payroll extraction tasks.
    """

    # Get MCP tools
    mcp_service = get_mcp_service()
    company_checks = await mcp_service.get_tool(server_name="paychex", tool_name="paychex_company_checks")

    tools = [
        company_checks,
    ]

    llm_with_tools = settings.LLM(model=settings.LLM_MODEL_NON_REASONING).bind_tools(tools)
    memory = MemorySaver()

    # format prompt
    prompt = format_base_agent_prompt(PROMPTS[f"payroll_agent_{agent_number}"])

    logger.info(f"Creating payroll extraction agent {agent_number}")
    return create_react_agent(
        model=llm_with_tools,
        tools=tools,
        prompt=prompt,
        response_format=PayrollResponse,
        checkpointer=memory
    ), llm_with_tools


@log_runtime("payroll_processing_agent_1_run", "payroll_processing_", "payroll_processing_output_state")
async def payroll_processing_agent_run(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_agent_run called...")

    agent = asyncio.create_task(create_payroll_agent(state=state, agent_number=1))
    email_content = state.input_state.EmailContent
    company_id = state.input_state.companyID
    context = state.company_worker_lookup_output_state.agent_context
    workers = list(state.company_worker_lookup_output_state.workers_matched.keys())

    agent_input = {
        "messages": [
            SystemMessage(content=f'company_id (string): "{company_id}"'),
            SystemMessage(content=f'Worker_names: "{workers}"'),
            SystemMessage(content=f'Context: "{context}"'),
            HumanMessage(content=email_content)
        ]
    }

    agent, llm = await agent

    # Stream through the agent's execution and print each step
    final_chunk = None
    async for chunk in agent.astream(agent_input, config={"configurable": {"thread_id": str(uuid.uuid4())}}):
        final_chunk = chunk

    # The last streamed chunk contains the full response graph
    output = final_chunk['generate_structured_response']['structured_response']

    # token count
    input_text = " ".join([msg.content for msg in agent_input["messages"]])
    input_tokens = count_tokens(input_text, model_name=getattr(llm, "model_name", "gpt-4"))
    output_text = str(output)
    output_tokens = count_tokens(output_text, model_name=getattr(llm, "model_name", "gpt-4"))

    log_llm_usage(
        state=state,
        step_name="payroll_processing_agent_run",
        llm=llm,
        llm_response=output,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        output_state_attr="payroll_processing_output_state"
    )

    payroll_commands: PayrollResponse = output
    
    # No need to apply name mapping here - the agent already did it
    state.payroll_processing_output_state.payroll_commands_agent_1 = payroll_commands.commands
    return state


@log_runtime("payroll_processing_agent_2_run", "payroll_processing_", "payroll_processing_output_state")
async def payroll_processing_agent_2_run(state: PayrollState) -> Dict:
    logger.info("payroll_processing_agent_2_run called...")

    agent = asyncio.create_task(create_payroll_agent(state=state, agent_number=2))
    email_content = state.input_state.EmailContent
    company_id = state.input_state.companyID
    context = state.company_worker_lookup_output_state.agent_context

    agent_input = {
        "messages": [
            SystemMessage(content=f'company_id (string): "{company_id}"'),
            SystemMessage(content=f'Context: "{context}"'),
            HumanMessage(content=email_content)
        ]
    }

    agent, llm = await agent

    # Stream through the agent's execution and print each step
    final_chunk = None
    async for chunk in agent.astream(agent_input, config={"configurable": {"thread_id": str(uuid.uuid4())}}):
        pretty_print_messages(chunk, last_message=True)
        final_chunk = chunk

    # The last streamed chunk contains the full response graph
    output = final_chunk['generate_structured_response']['structured_response']

    # token count
    input_text = " ".join([msg.content for msg in agent_input["messages"]])
    input_tokens = count_tokens(input_text, model_name=getattr(llm, "model_name", "gpt-4"))
    output_text = str(output)
    output_tokens = count_tokens(output_text, model_name=getattr(llm, "model_name", "gpt-4"))

    log_llm_usage(
        state=state,
        step_name="payroll_processing_agent_2_run",
        llm=llm,
        llm_response=output,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        output_state_attr="payroll_processing_output_state"
    )

    payroll_commands: PayrollResponse = output

    # No need to apply name mapping here - the agent already did it
    state.payroll_processing_output_state.payroll_commands_agent_2 = payroll_commands.commands
    return {}


@log_runtime("payroll_processing_router", "payroll_processing_", "payroll_processing_output_state")
def payroll_processing_router(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_router called...")

    payrolls_agent_1 = state.payroll_processing_output_state.payroll_commands_agent_1
    payrolls_agent_2 = state.payroll_processing_output_state.payroll_commands_agent_2

    failures = []

    # If one agent returns [], use the other if it's non-empty
    if payrolls_agent_1 == [] and payrolls_agent_2:
        logger.warning("Agent 1 returned empty, using Agent 2's results.")
        state.payroll_processing_output_state.processed_payrolls = payrolls_agent_2
        state.payroll_processing_output_state.agents_matched = True
        state.payroll_processing_output_state.should_continue = True
        return state
    elif payrolls_agent_2 == [] and payrolls_agent_1:
        logger.warning("Agent 2 returned empty, using Agent 1's results.")
        state.payroll_processing_output_state.processed_payrolls = payrolls_agent_1
        state.payroll_processing_output_state.agents_matched = True
        state.payroll_processing_output_state.should_continue = True
        return state

    if len(payrolls_agent_1) != len(payrolls_agent_2): ## Only testing length for now
        error_message = f"Payroll commands mismatch, agent_1: {payrolls_agent_1}, agent_2: {payrolls_agent_2}"
        failures.append(error_message)
        logger.warning(error_message)

    if not failures:
        logger.info("Payroll extraction successful")
        state.payroll_processing_output_state.processed_payrolls = payrolls_agent_1
        state.payroll_processing_output_state.agents_matched = True
        state.payroll_processing_output_state.should_continue = True
    else:
        error_message = "; ".join(failures)
        logger.warning(f"payroll_extraction failure: {error_message}")
        state.payroll_processing_output_state.termination_reason = error_message

    return state


@log_runtime("payroll_processing_terminate", "payroll_processing_", "payroll_processing_output_state")
def payroll_processing_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("payroll_processing_terminate called...")
    reason = state.payroll_processing_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.payroll_processing_output_state.should_continue = False
    state.payroll_processing_output_state.termination_node = payroll_processing_terminate.__name__
    logger.debug(f"payroll_processing_terminate returning PayrollState: {state}")
    return state


@log_runtime("payroll_processing_finishing_node", "payroll_processing_", "payroll_processing_output_state")
def payroll_processing_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_finishing_node called...")
    return state


def _map_name_to_worker_id(name: str, mapping: Dict[str, str]) -> str | None:
    """
    Map a name to a worker ID using the provided mapping.

    Args:
        name (str): The name to map.
        mapping (Dict[str, str]): Mapping of names to worker IDs.

    Returns:
        str: The worker ID if found, otherwise None.
    """
    return mapping[name]['worker_id']


def _map_name_to_registered_name(name: str, mapping: Dict[str, str]) -> str | None:
    """
    Map a name to a worker ID using the provided mapping.

    Args:
        name (str): The name to map.
        mapping (Dict[str, str]): Mapping of names to worker IDs.

    Returns:
        str: The worker ID if found, otherwise None.
    """
    return mapping[name]['db_name']


def _get_amount(inputs: PayAmountInput) -> PayAmountOutput:
    """
    Calculate the pay amount based on hours worked and rate.

    Args:
        hours (float): The number of hours worked.
        rate (float): The pay rate per hour.

    Returns:
        float: The total pay amount.
    """
    return PayAmountOutput(amount=inputs.hours * inputs.rate)