classification:
  intent_triage: # value <= Threshold
    isWorkRelated: 69
    RequiresWork: 69
    isAboutPayroll: 79
    EnterPayroll: 69
   # isStandaloneRequest: 80
  business_knockout:  #  Value >= Threshold
    KnockoutIntents: 86
  technical_knockout: # Value >= Threshold
    KnockoutIntents: 86
company_worker_lookup:
  company_selection_confidence_threshold: 79 
  handwritten_confidence_threshold: 79
  max_days_between_check_date_and_pay_period: 30
  llm_name_matching_confidence: 40
  min_num_of_active_employees: 1
  number_of_account_ids: 1
  lookup_status: "success"
  account_status: "Active"
payroll_processing:
  extraction_success_flag: True
validation:
  invalid_values: ["", null, " ", "null"]
  KnockoutIntents: 86
execution:
  min_payrolls: 1
release:
  placeholder: 100
