{"cells": [{"cell_type": "code", "execution_count": 1, "id": "96a16e68-e52f-4ace-b8ff-8e7e8f4353ca", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:23.918180Z", "start_time": "2025-08-06T22:33:22.529378Z"}}, "outputs": [], "source": ["import os\n", "import re\n", "import time\n", "import json\n", "import requests\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "\n", "import email\n", "\n", "from tqdm import tqdm\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n"]}, {"cell_type": "markdown", "id": "6f43ecb8-693f-4195-a2a0-2b05f258987e", "metadata": {}, "source": ["## Functions for parsing the cases from the excel file"]}, {"cell_type": "code", "execution_count": 2, "id": "e9fbbe52-1394-493a-b378-bd6b2f5c685b", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:24.081973Z", "start_time": "2025-08-06T22:33:24.074620Z"}}, "outputs": [], "source": ["def map_case_to_placeholder(row):\n", "    \"\"\"Maps a test case to the placeholder structure.\"\"\"\n", "    return {\n", "      \"displayId\": f\"{row['displayID']}\" if len(str(row['displayID'])) == 8 else f\"{'0'*(8-len(str(row['displayID'])))}{row['displayID']}\",\n", "      \"comment\": {\n", "        \"uid\": f\"{row['New GT ID']}\",\n", "        \"id\":  f\"{row['Email ID from source']}\",\n", "        \"timestamp\": row['TImeStamp'].isoformat(),\n", "        \"messages\": [\n", "          {\n", "            \"body\": {\n", "              \"text\": row['Email to use'],\n", "            },\n", "            \"subject\": {\n", "              \"text\":  \"Subject\",\n", "            }\n", "          }\n", "        ]\n", "      }\n", "    }\n", "\n", "def create_upstream_tests(df, time_col= 'TImeStamp'):\n", "    df[time_col] = pd.to_datetime(df[time_col])\n", "    js = [map_case_to_placeholder(case) for i, case in df.iterrows()]\n", "    return js\n", "\n", "def call_api(case, max_retries=3, delay=3):\n", "    last_exception = None\n", "    for attempt in range(max_retries):\n", "        try:\n", "            response = requests.post(ENDPOINT_URL, json=case, timeout=700)\n", "            response.raise_for_status()\n", "            return response.json().get(\"response\", \"{}\")\n", "        except Exception as e:\n", "            last_exception = e\n", "            if attempt < max_retries - 1:\n", "                print(f\"Retry attempt: {attempt}\")\n", "                time.sleep(delay)\n", "    return {\"error\": f\"API call failed after {max_retries} attempts: {str(last_exception)}\"}\n", "\n", "# Parallelize API calls using ThreadPoolExecutor\n", "def run_evaluation(test_cases, max_workers=10):\n", "    results= list()\n", "    with ThreadPoolExecutor(max_workers=max_workers) as executor:\n", "        future_to_case = {executor.submit(call_api, case): case for case in test_cases}\n", "        for future in tqdm(as_completed(future_to_case), total=len(test_cases), desc=\"Evaluating\"):\n", "            case = future_to_case[future]\n", "            try:\n", "                result = future.result()\n", "            except Exception as e:\n", "                result = {\"error\": str(e)}\n", "            results.append({\n", "                'New GT ID': case.get(\"comment\").get(\"uid\"),\n", "                \"input\": case,\n", "                \"output\": result\n", "            })\n", "    return results\n", "\n", "def save_json(path, data):\n", "    with open(path, 'w', encoding='utf-8') as f:\n", "      json.dump(data, f, indent=2, ensure_ascii=False)\n", "\n", "def load_test_cases(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "def evaluation_parser(case):\n", "    parsed_rows = []\n", "    resp = case.get(\"output\",{})\n", "    pay_extraction = resp.get(\"payroll_processing_output_state\", {}).get(\"processed_payrolls\", [])\n", "    if not pay_extraction:\n", "        row = {\n", "                'New GT ID': case.get(\"input\", {}).get('comment',{}).get(\"uid\", \"\"),\n", "                'Email ID from source': case[\"input\"].get(\"comment\").get(\"id\", \"\"),\n", "                \"EmailContent\": resp.get(\"input_state\", {}).get(\"EmailContent\", \"\"),\n", "                \"companyID\": resp.get(\"input_state\", {}).get(\"companyID\", \"\"),\n", "                \"Worker Name\": pd.NA,\n", "                \"Worker ID\": pd.NA,\n", "                \"Legal Name\": pd.NA,\n", "                \"payType\": pd.NA,\n", "                \"payAmount\" : pd.NA,\n", "                \"payHours\": pd.NA,\n", "                \"payRate\": pd.NA,\n", "                \"rateType\": pd.NA,\n", "                \"rate_overwrite\": pd.NA,\n", "                \"Commands in email\": 0,\n", "                \"run_time\": resp.get(\"run_time\", pd.NA),\n", "\n", "                \"successful_flag\" : resp.get(\"successful_flag\", False),\n", "                \"termination_graph\": resp.get(\"termination_dict\", {}).get(\"termination_graph\", \"\"),\n", "                \"termination_reason\": resp.get(\"termination_dict\", {}).get(\"termination_reason\", \"\"),\n", "            }\n", "        parsed_rows.append(row)\n", "    else:\n", "        for cmd in pay_extraction:\n", "            row = {\n", "                    'New GT ID': case.get(\"input\", {}).get('comment',{}).get(\"uid\", \"\"),\n", "                    'Email ID from source': resp.get(\"input\", {}).get('comment',{}).get(\"id\", \"\"),\n", "                    \"EmailContent\": resp.get(\"input_state\", {}).get(\"EmailContent\", \"\"),\n", "                    \"companyID\": resp.get(\"input_state\", {}).get(\"companyID\", \"\"),\n", "                    \"Worker Name\": cmd.get(\"name\", ''),\n", "                    \"Worker ID\": cmd.get(\"worker_id\", pd.NA),\n", "                    \"Legal Name\": cmd.get(\"registered_name\", pd.NA),\n", "                    \"payType\": cmd.get(\"payType\", pd.NA),\n", "                    \"payAmount\" : cmd.get(\"payAmount\", pd.NA),\n", "                    \"payHours\": cmd.get(\"payHours\", pd.NA),\n", "                    \"payRate\": cmd.get(\"payRate\", pd.NA),\n", "                    \"rateType\": cmd.get(\"rateType\", pd.NA),\n", "                    \"rate_overwrite\": cmd.get(\"rate_overwrite\", pd.NA),\n", "                    \"Commands in email\": len(pay_extraction),\n", "                    \"run_time\": resp.get(\"run_time\", pd.NA),\n", "\n", "                    \"successful_flag\" : resp.get(\"successful_flag\", False),\n", "                    \"termination_graph\": resp.get(\"termination_dict\", {}).get(\"termination_graph\", \"\"),\n", "                    \"termination_reason\": resp.get(\"termination_dict\", {}).get(\"termination_reason\", \"\"),\n", "                }\n", "            parsed_rows.append(row)\n", "    return parsed_rows\n", "\n", "\n", "# Remove illegal Excel characters\n", "_illegal_chars_re = re.compile(r\"[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]\")\n", "\n", "def remove_illegal_chars(val):\n", "    if isinstance(val, str):\n", "        return _illegal_chars_re.sub(\"\", val)\n", "    return val\n"]}, {"cell_type": "markdown", "id": "b9a3b86f-1499-4fe1-8b75-21515c874462", "metadata": {}, "source": ["### Read Golden Set Excel File"]}, {"cell_type": "code", "execution_count": 3, "id": "d0d53de0-dd75-457e-9308-ef22bf7b18e2", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:24.289997Z", "start_time": "2025-08-06T22:33:24.096022Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email source</th>\n", "      <th>Email ID from source</th>\n", "      <th>Testing Case</th>\n", "      <th>Testing category</th>\n", "      <th>Cases covered</th>\n", "      <th>Simplified cases covered</th>\n", "      <th>Status</th>\n", "      <th>Full email</th>\n", "      <th>Email to use</th>\n", "      <th>Email subject</th>\n", "      <th>TImeStamp</th>\n", "      <th>To_email</th>\n", "      <th>From_email</th>\n", "      <th>displayID</th>\n", "      <th>Client note to consider</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Original 100</td>\n", "      <td>5</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-15 17:51:06.753</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>11111181</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   New GT ID  Email source  Email ID from source         Testing Case  \\\n", "0          1  Original 100                     5  Standard Processing   \n", "\n", "   Testing category          Cases covered Simplified cases covered  \\\n", "0  Standard Payroll  REGULAR_HOURS, AMOUNT    REGULAR_HOURS, AMOUNT   \n", "\n", "                        Status  \\\n", "0  Confirmed test case with GT   \n", "\n", "                                          Full email  \\\n", "0  Good morning:\\n\\nPlease make and process all p...   \n", "\n", "                                        Email to use Email subject  \\\n", "0  Good morning:\\n\\nPlease make and process all p...           NaN   \n", "\n", "                 TImeStamp               To_email  \\\n", "0  2025-04-15 17:51:06.753  <EMAIL>   \n", "\n", "                     From_email displayID Client note to consider  \n", "0  <EMAIL>  11111181                     NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_excel(r\"C://Users/<USER>/Downloads/Payroll_processing_golden_set.xlsx\", sheet_name=\"Golden_set\")\n", "\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": 4, "id": "148f9fc403a84aad", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:24.302445Z", "start_time": "2025-08-06T22:33:24.300270Z"}}, "outputs": [{"data": {"text/plain": ["np.int64(2)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#df = df[df.index<=36]\n", "\n", "df.From_email.isnull().sum()"]}, {"cell_type": "code", "execution_count": 5, "id": "e305f9cb03ef8597", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:25.226388Z", "start_time": "2025-08-06T22:33:25.221826Z"}}, "outputs": [{"data": {"text/plain": ["Testing Case\n", "Standard Processing    105\n", "Knockout                55\n", "Name: count, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Testing Case'].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "id": "b641861cbfef98d4", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:26.066969Z", "start_time": "2025-08-06T22:33:26.062696Z"}}, "outputs": [{"data": {"text/plain": ["valid_cases\n", "True     105\n", "False     55\n", "Name: count, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df['valid_cases'] = df['Testing Case'] == \"Standard Processing\"\n", "df['valid_cases'].value_counts()"]}, {"cell_type": "markdown", "id": "fc0daae7-968b-47e9-bdb3-1f06f69c6a63", "metadata": {}, "source": ["### Create cases"]}, {"cell_type": "code", "execution_count": 7, "id": "a77ddfbd-6709-4a7f-929f-82700f9319f4", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:27.678816Z", "start_time": "2025-08-06T22:33:27.669649Z"}}, "outputs": [], "source": ["cases = create_upstream_tests(df)\n", "\n", "# write cases to file\n", "save_json(\"ground_truth/golden_cases.json\", cases)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "f9b7b287f20c5df8", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:27.962629Z", "start_time": "2025-08-06T22:33:27.960955Z"}}, "outputs": [], "source": ["#json.dumps(cases[9])"]}, {"cell_type": "markdown", "id": "7e219391-2cca-4c49-9e3d-0878a5b17fbe", "metadata": {}, "source": ["### Run Cases"]}, {"cell_type": "code", "execution_count": 9, "id": "a15a4997-8a94-4f56-8a90-f00bc101dd41", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:29.146314Z", "start_time": "2025-08-06T22:33:29.144077Z"}}, "outputs": [], "source": ["# Config\n", "HOST = \"http://localhost:8000\"\n", "API_PREFIX = \"/api/v1\"\n", "ROUTE = \"/process-email\"\n", "ENDPOINT_URL = f\"{HOST}{API_PREFIX}{ROUTE}\""]}, {"cell_type": "code", "execution_count": 10, "id": "003487e5-75ec-4960-8a7a-087e38e598c5", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T22:33:29.515420Z", "start_time": "2025-08-06T22:33:29.513796Z"}}, "outputs": [], "source": ["#call_api(cases[9])"]}, {"cell_type": "code", "execution_count": 11, "id": "30762439-1910-44f4-a7a1-e73c7b00cf42", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:00:45.756293Z", "start_time": "2025-08-06T22:33:30.903317Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Evaluating:  43%|█████████████████████████████▊                                       | 69/160 [15:22<14:10,  9.34s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:  69%|██████████████████████████████████████████████▊                     | 110/160 [23:49<10:15, 12.32s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n", "Retry attempt: 0\n", "Retry attempt: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|████████████████████████████████████████████████████████████████████| 160/160 [28:53<00:00, 10.84s/it]\n"]}], "source": ["results = run_evaluation(cases)"]}, {"cell_type": "markdown", "id": "6c3618331c7fb38e", "metadata": {}, "source": ["## re run failed cases"]}, {"cell_type": "code", "execution_count": 12, "id": "53b79ba9f3f293a4", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:02:56.962872Z", "start_time": "2025-08-06T23:02:56.959619Z"}}, "outputs": [], "source": ["failed = {i:case[\"input\"] for i, case in enumerate(results) if \"error\" in case[\"output\"]}"]}, {"cell_type": "code", "execution_count": 13, "id": "e31b55ff9f33f9f7", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:02:57.361685Z", "start_time": "2025-08-06T23:02:57.358597Z"}}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["len(failed)"]}, {"cell_type": "code", "execution_count": 14, "id": "25ee9d9d442c8f8a", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:02:58.286452Z", "start_time": "2025-08-06T23:02:58.282763Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running case 27 with ID 31\n", "Running case 29 with ID 43\n", "Running case 30 with ID 39\n", "Retry attempt: 0\n", "Retry attempt: 1\n", "Running case 31 with ID 38\n", "Retry attempt: 0\n", "Retry attempt: 1\n", "Running case 42 with ID 51\n", "Running case 45 with ID 50\n", "Running case 49 with ID 56\n", "Running case 97 with ID 127\n", "Retry attempt: 0\n", "Retry attempt: 1\n"]}], "source": ["for i, case in failed.items():\n", "    print(f\"Running case {i} with ID {case['comment']['uid']}\")\n", "    results[i] = {\n", "        'New GT ID': case.get(\"comment\").get(\"uid\"),\n", "        \"input\": cases[i],\n", "        \"output\": call_api(cases[i])\n", "    }"]}, {"cell_type": "code", "execution_count": 17, "id": "5cc8fbdc7e2a1548", "metadata": {"ExecuteTime": {"end_time": "2025-08-05T21:19:10.971098Z", "start_time": "2025-08-05T21:19:10.969007Z"}}, "outputs": [], "source": ["failed = {i:case[\"input\"] for i, case in enumerate(results) if \"error\" in case[\"output\"]}"]}, {"cell_type": "code", "execution_count": 18, "id": "f060c8ccbe921394", "metadata": {"ExecuteTime": {"end_time": "2025-08-05T21:19:34.984528Z", "start_time": "2025-08-05T21:19:34.982182Z"}, "scrolled": true}, "outputs": [{"data": {"text/plain": ["{30: {'displayId': '14102976',\n", "  'comment': {'uid': '43',\n", "   'id': '2483',\n", "   'timestamp': '2025-04-29T06:09:35.493000',\n", "   'messages': [{'body': {'text': 'Hellohere is the April payrollGreg Ray had 31 hours.\\xa0Enzo Ray had 21.5 hours\\xa0Thank you\\xa0Gary '},\n", "     'subject': {'text': 'Subject'}}]}},\n", " 31: {'displayId': '0020PT20',\n", "  'comment': {'uid': '44',\n", "   'id': '2509',\n", "   'timestamp': '2025-04-28T15:26:03.987000',\n", "   'messages': [{'body': {'text': '<PERSON><PERSON><PERSON> <PERSON>,\\n\\nHere is the report for St James employees:\\n\\nMaurice Christopher - 45 hours\\nCharlotte Harris - 30 hours\\nDawn Gold  - 20 hours\\n\\nRev.  <PERSON><PERSON><PERSON> - his usual salaried pay and Housing allowance.\\n\\nEnjoy the rest of your day,\\n\\nBW/Finance Warden\\nSt. James Church, Kingsessing\\nCell - ************'},\n", "     'subject': {'text': 'Subject'}}]}},\n", " 97: {'displayId': '18107014',\n", "  'comment': {'uid': '139',\n", "   'id': '3465',\n", "   'timestamp': '2025-04-14T16:31:26.473000',\n", "   'messages': [{'body': {'text': ' For this pay period:\\n\\n<PERSON><PERSON><PERSON><PERSON>: I am paying her the usual salary. . I am also paying her $300.00 under the category “Exp Reimb Non Tax.”\\n\\nMyself: I am paying myself $6,000.00.\\n\\n\\nSincerely,\\nR<PERSON><PERSON>, Esq.\\nNightingale Law Firm\\n853 Main Street, Suite A\\nSafety Harbor, FL 34695\\nT: ************\\nF: ************'},\n", "     'subject': {'text': 'Subject'}}]}}}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["failed"]}, {"cell_type": "markdown", "id": "2e6e4f88-0282-4344-86b0-7945232d295a", "metadata": {}, "source": ["### Save results"]}, {"cell_type": "code", "execution_count": 14, "id": "3971a7c1a3be654f", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:04.638524Z", "start_time": "2025-08-06T23:03:04.635783Z"}}, "outputs": [], "source": ["from datetime import datetime"]}, {"cell_type": "code", "execution_count": 15, "id": "825523e275e6868", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:06.229434Z", "start_time": "2025-08-06T23:03:06.226191Z"}}, "outputs": [{"data": {"text/plain": ["'2025-08-07T11:54:36.515425'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["datetime.now().isoformat(\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "b756a457-a10a-4238-9898-7124c0b23ce0", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:06.502416Z", "start_time": "2025-08-06T23:03:06.433282Z"}}, "outputs": [], "source": ["save_json(\"results_gpt4.1.json\", results)"]}, {"cell_type": "markdown", "id": "bc23a80b-23b9-42c3-a580-8ce86e195677", "metadata": {}, "source": ["### Load results"]}, {"cell_type": "code", "execution_count": 17, "id": "eac36ae2-d54b-40c8-9980-e8ac933d4328", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:09.293711Z", "start_time": "2025-08-06T23:03:09.291477Z"}}, "outputs": [], "source": ["#results = load_test_cases(\"results.json\")"]}, {"cell_type": "markdown", "id": "fc1a6e68-b253-4e43-becc-71678e340911", "metadata": {}, "source": ["### Convert to df"]}, {"cell_type": "code", "execution_count": 18, "id": "a8cf14c6-1498-42a6-9d0b-2c0997aba32f", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:10.641553Z", "start_time": "2025-08-06T23:03:10.637469Z"}}, "outputs": [], "source": ["\n", "data = list()\n", "for case in results:\n", "    tmp = evaluation_parser(case)\n", "    data.extend(tmp)"]}, {"cell_type": "code", "execution_count": 19, "id": "4d300114-2eae-4c8b-88e9-104e42d5b6f1", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:11.299858Z", "start_time": "2025-08-06T23:03:11.284238Z"}}, "outputs": [], "source": ["runs = pd.DataFrame(data)\n", "runs = runs.map(remove_illegal_chars)\n", "runs['New GT ID'] = runs['New GT ID'].astype(int)\n", "runs = runs.merge(df[['New GT ID', 'valid_cases']], on='New GT ID', how='left')"]}, {"cell_type": "code", "execution_count": 20, "id": "8845b4f8d36ec87a", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:14.193380Z", "start_time": "2025-08-06T23:03:14.188278Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email ID from source</th>\n", "      <th>EmailContent</th>\n", "      <th>companyID</th>\n", "      <th>Worker Name</th>\n", "      <th>Worker ID</th>\n", "      <th>Legal Name</th>\n", "      <th>payType</th>\n", "      <th>payAmount</th>\n", "      <th>payHours</th>\n", "      <th>payRate</th>\n", "      <th>rateType</th>\n", "      <th>rate_overwrite</th>\n", "      <th>Commands in email</th>\n", "      <th>run_time</th>\n", "      <th>successful_flag</th>\n", "      <th>termination_graph</th>\n", "      <th>termination_reason</th>\n", "      <th>valid_cases</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7</td>\n", "      <td>11</td>\n", "      <td>&gt; From: <EMAIL>\\n&gt; To: j<PERSON>lan...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>37.77</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Technical knockout rules triggered: UNADDRESSA...</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   New GT ID Email ID from source  \\\n", "0          7                   11   \n", "\n", "                                        EmailContent companyID Worker Name  \\\n", "0  > From: <EMAIL>\\n> To: j<PERSON>lan...                  <NA>   \n", "\n", "  Worker ID Legal Name payType payAmount payHours payRate rateType  \\\n", "0      <NA>       <NA>    <NA>      <NA>     <NA>    <NA>     <NA>   \n", "\n", "  rate_overwrite  Commands in email  run_time  successful_flag  \\\n", "0           <NA>                  0     37.77            False   \n", "\n", "             termination_graph  \\\n", "0  classification_output_state   \n", "\n", "                                  termination_reason  valid_cases  \n", "0  Technical knockout rules triggered: UNADDRESSA...         True  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.head(1)"]}, {"cell_type": "code", "execution_count": 21, "id": "205e25bd3f749e66", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:15.115544Z", "start_time": "2025-08-06T23:03:15.112615Z"}}, "outputs": [{"data": {"text/plain": ["160"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# total eamils\n", "df.shape[0]"]}, {"cell_type": "code", "execution_count": 22, "id": "aa964ed84f360510", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:15.705955Z", "start_time": "2025-08-06T23:03:15.702216Z"}}, "outputs": [{"data": {"text/plain": ["Testing Case\n", "Standard Processing    105\n", "Knockout                55\n", "Name: count, dtype: int64"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Testing Case'].value_counts()"]}, {"cell_type": "code", "execution_count": 23, "id": "e570a681b95c0551", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:19.871364Z", "start_time": "2025-08-06T23:03:19.866163Z"}}, "outputs": [{"data": {"text/plain": ["valid_cases\n", "True     105\n", "False     55\n", "Name: count, dtype: int64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.drop_duplicates('New GT ID')['valid_cases'].value_counts()"]}, {"cell_type": "code", "execution_count": 24, "id": "8952ea3836edd000", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:23.960655Z", "start_time": "2025-08-06T23:03:23.958119Z"}}, "outputs": [{"data": {"text/plain": ["(160, 19)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.drop_duplicates('New GT ID').shape"]}, {"cell_type": "code", "execution_count": 31, "id": "8432bc2285fd0c16", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:26.053761Z", "start_time": "2025-08-06T23:03:25.976220Z"}}, "outputs": [{"ename": "OSError", "evalue": "[<PERSON><PERSON><PERSON> 22] Invalid argument: 'runs_2025-08-06T17:43:20.797763.xlsx'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[31]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mruns\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_excel\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mruns_\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mdatetime\u001b[49m\u001b[43m.\u001b[49m\u001b[43mnow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43misoformat\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m.xlsx\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\util\\_decorators.py:333\u001b[39m, in \u001b[36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    327\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) > num_allow_args:\n\u001b[32m    328\u001b[39m     warnings.warn(\n\u001b[32m    329\u001b[39m         msg.format(arguments=_format_argument_list(allow_args)),\n\u001b[32m    330\u001b[39m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[32m    331\u001b[39m         stacklevel=find_stack_level(),\n\u001b[32m    332\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m333\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\generic.py:2436\u001b[39m, in \u001b[36mNDFrame.to_excel\u001b[39m\u001b[34m(self, excel_writer, sheet_name, na_rep, float_format, columns, header, index, index_label, startrow, startcol, engine, merge_cells, inf_rep, freeze_panes, storage_options, engine_kwargs)\u001b[39m\n\u001b[32m   2423\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mformats\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mexcel\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ExcelFormatter\n\u001b[32m   2425\u001b[39m formatter = ExcelFormatter(\n\u001b[32m   2426\u001b[39m     df,\n\u001b[32m   2427\u001b[39m     na_rep=na_rep,\n\u001b[32m   (...)\u001b[39m\u001b[32m   2434\u001b[39m     inf_rep=inf_rep,\n\u001b[32m   2435\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m2436\u001b[39m \u001b[43mformatter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2437\u001b[39m \u001b[43m    \u001b[49m\u001b[43mexcel_writer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2438\u001b[39m \u001b[43m    \u001b[49m\u001b[43msheet_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43msheet_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2439\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstartrow\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstartrow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2440\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstartcol\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstartcol\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2441\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfreeze_panes\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfreeze_panes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2442\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2443\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2444\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2445\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\io\\formats\\excel.py:943\u001b[39m, in \u001b[36mExcelFormatter.write\u001b[39m\u001b[34m(self, writer, sheet_name, startrow, startcol, freeze_panes, engine, storage_options, engine_kwargs)\u001b[39m\n\u001b[32m    941\u001b[39m     need_save = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    942\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m943\u001b[39m     writer = \u001b[43mExcelWriter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mwriter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mengine\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    947\u001b[39m \u001b[43m        \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    948\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    949\u001b[39m     need_save = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    951\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py:61\u001b[39m, in \u001b[36mOpenpyxlWriter.__init__\u001b[39m\u001b[34m(self, path, engine, date_format, datetime_format, mode, storage_options, if_sheet_exists, engine_kwargs, **kwargs)\u001b[39m\n\u001b[32m     57\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mopenpyxl\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mworkbook\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Workbook\n\u001b[32m     59\u001b[39m engine_kwargs = combine_kwargs(engine_kwargs, kwargs)\n\u001b[32m---> \u001b[39m\u001b[32m61\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m     62\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     63\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     64\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     65\u001b[39m \u001b[43m    \u001b[49m\u001b[43mif_sheet_exists\u001b[49m\u001b[43m=\u001b[49m\u001b[43mif_sheet_exists\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     66\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     67\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     69\u001b[39m \u001b[38;5;66;03m# ExcelWriter replaced \"a\" by \"r+\" to allow us to first read the excel file from\u001b[39;00m\n\u001b[32m     70\u001b[39m \u001b[38;5;66;03m# the file and later write to it\u001b[39;00m\n\u001b[32m     71\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._mode:  \u001b[38;5;66;03m# Load from existing workbook\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\io\\excel\\_base.py:1246\u001b[39m, in \u001b[36mExcelWriter.__init__\u001b[39m\u001b[34m(self, path, engine, date_format, datetime_format, mode, storage_options, if_sheet_exists, engine_kwargs)\u001b[39m\n\u001b[32m   1242\u001b[39m \u001b[38;5;28mself\u001b[39m._handles = IOHandles(\n\u001b[32m   1243\u001b[39m     cast(IO[\u001b[38;5;28mbytes\u001b[39m], path), compression={\u001b[33m\"\u001b[39m\u001b[33mcompression\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28;01mNone\u001b[39;00m}\n\u001b[32m   1244\u001b[39m )\n\u001b[32m   1245\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path, ExcelWriter):\n\u001b[32m-> \u001b[39m\u001b[32m1246\u001b[39m     \u001b[38;5;28mself\u001b[39m._handles = \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1247\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\n\u001b[32m   1248\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1249\u001b[39m \u001b[38;5;28mself\u001b[39m._cur_sheet = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1251\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m date_format \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\io\\common.py:882\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    873\u001b[39m         handle = \u001b[38;5;28mopen\u001b[39m(\n\u001b[32m    874\u001b[39m             handle,\n\u001b[32m    875\u001b[39m             ioargs.mode,\n\u001b[32m   (...)\u001b[39m\u001b[32m    878\u001b[39m             newline=\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    879\u001b[39m         )\n\u001b[32m    880\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    881\u001b[39m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m882\u001b[39m         handle = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    883\u001b[39m     handles.append(handle)\n\u001b[32m    885\u001b[39m \u001b[38;5;66;03m# Convert BytesIO or file objects passed with an encoding\u001b[39;00m\n", "\u001b[31mOSError\u001b[39m: [<PERSON><PERSON><PERSON> 22] Invalid argument: 'runs_2025-08-06T17:43:20.797763.xlsx'"]}], "source": ["runs.to_excel(f\"runs_{datetime.now().isoformat()}.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 25, "id": "49ce3758f73ed8f0", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:26.926498Z", "start_time": "2025-08-06T23:03:26.922386Z"}}, "outputs": [{"data": {"text/plain": ["successful_flag\n", "True     81\n", "False    79\n", "Name: count, dtype: int64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.drop_duplicates('New GT ID')[\"successful_flag\"].value_counts()"]}, {"cell_type": "code", "execution_count": 26, "id": "e602132e67a094bf", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:30.965746Z", "start_time": "2025-08-06T23:03:30.962874Z"}}, "outputs": [], "source": ["condition = runs['valid_cases']"]}, {"cell_type": "code", "execution_count": 27, "id": "d2da8bb2b366bdcd", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:31.708885Z", "start_time": "2025-08-06T23:03:31.703559Z"}}, "outputs": [{"data": {"text/plain": ["count    160.000000\n", "mean      98.429375\n", "std       67.634651\n", "min        6.210000\n", "25%       32.810000\n", "50%      104.295000\n", "75%      148.662500\n", "max      262.360000\n", "Name: run_time, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.drop_duplicates('New GT ID')['run_time'].describe()"]}, {"cell_type": "code", "execution_count": 28, "id": "6d646b8b499d597d", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:35.706299Z", "start_time": "2025-08-06T23:03:35.702085Z"}}, "outputs": [{"data": {"text/plain": ["(105, 19)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[condition].drop_duplicates('New GT ID').shape"]}, {"cell_type": "code", "execution_count": 29, "id": "399c9358ccc511f4", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T23:03:37.252517Z", "start_time": "2025-08-06T23:03:37.248884Z"}}, "outputs": [{"data": {"text/plain": ["termination_graph\n", "                                      65\n", "company_worker_lookup_output_state    17\n", "classification_output_state           14\n", "payroll_processing_output_state        6\n", "validation_output_state                3\n", "Name: count, dtype: int64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[condition].drop_duplicates('New GT ID')[\"termination_graph\"].value_counts().head(10)"]}, {"cell_type": "code", "execution_count": 68, "id": "34824423911fb277", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T00:06:31.709944Z", "start_time": "2025-08-06T00:06:31.695420Z"}}, "outputs": [{"data": {"text/plain": ["termination_reason\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         57\n", "May be some workers terminated since LLM name matching confidence too low for 2/5 workers: Thai (0%), I (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              2\n", "May be some workers terminated since LLM name matching confidence too low for 1/14 workers: Madison Brook (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON><PERSON>it<PERSON> Hadinata', worker_id='004UWBZQJ3UBXHXVEG0Q', registered_name='Prajitno Hadinata', hours=66.0, rate=32.0, rate_type='hourly', rate_overwrite=True, amount=2112.0, payment_type='salary'), WorkerCommand(name='<PERSON><PERSON>rado', worker_id='00M9LQF7LPX2AJXJ9JO6', registered_name='Richelle Anne Manalad Prado', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2340.0, payment_type='salary'), WorkerCommand(name='<PERSON><PERSON> Anne Manalad Prado', worker_id='00M9LQF7LPX2AJXJ9JO6', registered_name='<PERSON><PERSON> Anne Manalad Prado', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='allowance'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKR2AR6OA493N', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2400.0, payment_type='salary'), WorkerCommand(name='Christine Kusnowo', worker_id='004UWBZQKR2AR6OA493N', registered_name='Christine Kusnowo', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=363.5, payment_type='stipend'), WorkerCommand(name='Julyana Tan', worker_id='004UWBZQJ3UBXHXVEG2I', registered_name='Julyana Tan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2600.0, payment_type='salary'), WorkerCommand(name='Jojie Aguilar', worker_id='004UWBZQJ3UBWNZIBG3K', registered_name='Jojie Aguilar Q Elgincolin', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2600.0, payment_type='salary'), WorkerCommand(name='Jojie Aguilar', worker_id='004UWBZQJ3UBWNZIBG3K', registered_name='Jojie Aguilar Q Elgincolin', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='allowance'), WorkerCommand(name='Jennifer Chang', worker_id='004UWBZQJ3UBXHXVEG3E', registered_name='Jennifer W Chang', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3000.0, payment_type='salary'), WorkerCommand(name='Jennifer Chang', worker_id='004UWBZQJ3UBXHXVEG3E', registered_name='Jennifer W Chang', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='allowance'), WorkerCommand(name='Gloria Lan', worker_id='004UWBZQJ3UBXHXVEG0I', registered_name='Gloria W Lan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3780.0, payment_type='salary'), WorkerCommand(name='Gloria Lan', worker_id='004UWBZQJ3UBXHXVEG0I', registered_name='Gloria W Lan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=569.0, payment_type='stipend'), WorkerCommand(name='Frances Lam', worker_id='004UWBZQJ3UBXHXVEG16', registered_name='Frances S Lam', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3600.0, payment_type='salary')], agent_2: [WorkerCommand(name='Prayitno Hadinata', worker_id='Prayitno Hadinata', registered_name='Prayitno Hadinata', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2112.0, payment_type='Bi-monthly'), WorkerCommand(name='Prayitno Hadinata', worker_id='Prayitno Hadinata', registered_name='Prayitno Hadinata', hours=66.0, rate=32.0, rate_type='Hourly', rate_overwrite=True, amount=None, payment_type='Per hr. pay'), WorkerCommand(name='Richelle Anne Manalad Prado', worker_id='Richelle Anne Manalad Prado', registered_name='Richelle Anne Manalad Prado', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2340.0, payment_type='Bi-monthly'), WorkerCommand(name='Richelle Anne Manalad Prado', worker_id='Richelle Anne Manalad Prado', registered_name='Richelle Anne Manalad Prado', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='Allowance'), WorkerCommand(name='Christine Kusnowo', worker_id='Christine Kusnowo', registered_name='Christine Kusnowo', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=363.5, payment_type='Stipend'), WorkerCommand(name='Christine Kusnowo', worker_id='Christine Kusnowo', registered_name='Christine Kusnowo', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2400.0, payment_type='Bi-monthly'), WorkerCommand(name='Julyana Tan', worker_id='Julyana Tan', registered_name='Julyana Tan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2600.0, payment_type='Bi-monthly'), WorkerCommand(name='Jojie Aguilar', worker_id='Jojie Aguilar', registered_name='Jojie Aguilar', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2600.0, payment_type='Bi-monthly'), WorkerCommand(name='Jojie Aguilar', worker_id='Jojie Aguilar', registered_name='Jojie Aguilar', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='Allowance'), WorkerCommand(name='Jennifer Chang', worker_id='Jennifer Chang', registered_name='Jennifer Chang', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3000.0, payment_type='Bi-monthly'), WorkerCommand(name='Jennifer Chang', worker_id='Jennifer Chang', registered_name='Jennifer Chang', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=50.0, payment_type='Allowance'), WorkerCommand(name='Gloria Lan', worker_id='Gloria Lan', registered_name='Gloria Lan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=569.0, payment_type='Stipend'), WorkerCommand(name='Gloria Lan', worker_id='Gloria Lan', registered_name='Gloria Lan', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3780.0, payment_type='Bi-monthly'), WorkerCommand(name='Frances Lam', worker_id='Frances Lam', registered_name='Frances Lam', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3600.0, payment_type='Bi-monthly')]     1\n", "Payrolls requires validation, status: Client explicitly requests confirmation before processing                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LGEA0ZBRUZUJ', registered_name='<PERSON>', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='hourly'), WorkerCommand(name='<PERSON>', worker_id='001GJICFLY5SZKJOC3ST', registered_name='<PERSON>', hours=3.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=105.0, payment_type='hourly'), WorkerCommand(name='<PERSON><PERSON>', worker_id='00M9LQF7LFU3SLIDDR00', registered_name='<PERSON><PERSON>', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='hourly'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LFTSRA9HCUX1', registered_name='<PERSON>', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='hourly'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LGKVLCO20M7N', registered_name='<PERSON> Pritchard', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='hourly'), WorkerCommand(name='Mackenzie', worker_id='00FWDQEWM8IUP86FMSSO', registered_name='Mackenzie S Bartlett', hours=3.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=45.0, payment_type='hourly'), WorkerCommand(name='Rylee', worker_id='004WOHNWMBZ796K3NW36', registered_name='Rylee Wilkerson', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='hourly'), WorkerCommand(name='Emma', worker_id='001GJICFLY5SVXHDC3PI', registered_name='Emma I Jaggers', hours=17.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=255.0, payment_type='hourly'), WorkerCommand(name='Mackenzie', worker_id='00FWDQEWM8IUP86FMSSO', registered_name='Mackenzie S Bartlett', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7.25, payment_type='tips'), WorkerCommand(name='Emma', worker_id='001GJICFLY5SVXHDC3PI', registered_name='Emma I Jaggers', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=166.63, payment_type='tips')], agent_2: [WorkerCommand(name='John', worker_id='John_id', registered_name='John', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Tom', worker_id='Tom_id', registered_name='Tom', hours=3.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=105.0, payment_type='REG'), WorkerCommand(name='Tamie', worker_id='Tamie_id', registered_name='Tamie', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Anthony', worker_id='Anthony_id', registered_name='Anthony', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Hannah', worker_id='Hannah_id', registered_name='Hannah', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Mackenzie', worker_id='Mackenzie_id', registered_name='Mackenzie', hours=3.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=52.25, payment_type='REG'), WorkerCommand(name='Rylee', worker_id='Rylee_id', registered_name='Rylee', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Emma', worker_id='Emma_id', registered_name='Emma', hours=17.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=421.63, payment_type='REG')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LGEA0ZBRUZUJ', registered_name='<PERSON>', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='001GJICFLY5SZKJOC3ST', registered_name='<PERSON>', hours=8.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='001GJICFLY5SZKJOC3ST', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=17.5, payment_type='TIPS'), WorkerCommand(name='<PERSON><PERSON>', worker_id='00M9LQF7LFU3SLIDDR00', registered_name='<PERSON><PERSON>', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LFTSRA9HCUX1', registered_name='<PERSON> <PERSON> Antolak', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='Hannah', worker_id='00M9LQF7LGKVLCO20M7N', registered_name='Hannah Pritchard', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='Mackenzie', worker_id='00FWDQEWM8IUP86FMSSO', registered_name='Mackenzie S Bartlett', hours=16.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='Mackenzie', worker_id='00FWDQEWM8IUP86FMSSO', registered_name='Mackenzie S Bartlett', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7.0, payment_type='TIPS'), WorkerCommand(name='Rylee', worker_id='004WOHNWMBZ796K3NW36', registered_name='Rylee Wilkerson', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='Emma', worker_id='001GJICFLY5SVXHDC3PI', registered_name='Emma I Jaggers', hours=7.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=None, payment_type='REGULAR'), WorkerCommand(name='Emma', worker_id='001GJICFLY5SVXHDC3PI', registered_name='Emma I Jaggers', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=35.0, payment_type='TIPS')], agent_2: [WorkerCommand(name='John', worker_id='id_John', registered_name='John', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='John', worker_id='id_John', registered_name='John', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='TIP'), WorkerCommand(name='Tom', worker_id='id_Tom', registered_name='Tom', hours=8.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=280.0, payment_type='REG'), WorkerCommand(name='Tom', worker_id='id_Tom', registered_name='Tom', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=17.5, payment_type='TIP'), WorkerCommand(name='Tamie', worker_id='id_Tamie', registered_name='Tamie', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Tamie', worker_id='id_Tamie', registered_name='Tamie', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='TIP'), WorkerCommand(name='Anthony', worker_id='id_Anthony', registered_name='Anthony', hours=0.0, rate=35.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Anthony', worker_id='id_Anthony', registered_name='Anthony', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='TIP'), WorkerCommand(name='Hannah', worker_id='id_Hannah', registered_name='Hannah', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Hannah', worker_id='id_Hannah', registered_name='Hannah', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='TIP'), WorkerCommand(name='Mackenzie', worker_id='id_Mackenzie', registered_name='Mackenzie', hours=16.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=240.0, payment_type='REG'), WorkerCommand(name='Mackenzie', worker_id='id_Mackenzie', registered_name='Mackenzie', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7.0, payment_type='TIP'), WorkerCommand(name='Rylee', worker_id='id_Rylee', registered_name='Rylee', hours=0.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=0.0, payment_type='REG'), WorkerCommand(name='Rylee', worker_id='id_Rylee', registered_name='Rylee', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='TIP'), WorkerCommand(name='Emma', worker_id='id_Emma', registered_name='Emma', hours=7.0, rate=15.0, rate_type='hourly', rate_overwrite=True, amount=105.0, payment_type='REG'), WorkerCommand(name='Emma', worker_id='id_Emma', registered_name='Emma', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=35.0, payment_type='TIP')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          1\n", "Business knockout rules triggered: CHANGES_UPDATES_TO_LABOR_DIST_JOB_COSTING (95%); Technical knockout rules triggered: MULTI_ID_REQUEST (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "May be some workers terminated since LLM name matching confidence too low for 2/4 workers: <PERSON> (0%), <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       1\n", "Business knockout rules triggered: MANUAL_CHECKS (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "May be some workers terminated since LLM name matching confidence too low for 3/7 workers: <PERSON> (0%), <PERSON> (0%), <PERSON><PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             1\n", "Business knockout rules triggered: BILLING_OVERRIDES (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='004UWBZQLDKHYLS7M2F7', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=300.0, payment_type='REIMBURSEMENT'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQLDKHYLS7M2F7', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='SALARY')], agent_2: [WorkerCommand(name='<PERSON>', worker_id='worker_id_for_<PERSON>_<PERSON>', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Salary'), WorkerCommand(name='<PERSON>', worker_id='worker_id_for_<PERSON>', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=300.0, payment_type='Exp Reimb Non Tax'), WorkerCommand(name='<PERSON>', worker_id='worker_id_for_<PERSON>_<PERSON>', registered_name='<PERSON> <PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=6000.0, payment_type='Salary')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   1\n", "May be some workers terminated since LLM name matching confidence too low for 1/1 workers: <PERSON><PERSON><PERSON> (0%); No matched workers found                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1\n", "May be some workers terminated since LLM name matching confidence too low for 1/6 workers: My<PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON><PERSON>, <PERSON>', worker_id='00JD49W7J1KPU92KVR3F', registered_name='<PERSON>', hours=25.0, rate=49.18, rate_type='HOURLY_RATE', rate_overwrite=False, amount=None, payment_type='REGULAR'), WorkerCommand(name='<PERSON>, <PERSON>', worker_id='00UJ9ANUJ1KPU73QXLEB', registered_name='<PERSON> T Law', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='SALARY'), WorkerCommand(name='<PERSON><PERSON>, <PERSON>', worker_id='00JD49W7J1KPU92KVR2Z', registered_name='<PERSON>', hours=15.0, rate=50.6, rate_type='HOURLY_RATE', rate_overwrite=True, amount=None, payment_type='REGULAR')], agent_2: [WorkerCommand(name='<PERSON>', worker_id='worker_ErbSandra', registered_name='<PERSON>', hours=25.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='HOURLY'), WorkerCommand(name='Melissa Law', worker_id='worker_LawMelissa', registered_name='Melissa Law', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='SALARY'), WorkerCommand(name='Margaret Pinkard', worker_id='worker_PinkardMargaret', registered_name='Margaret Pinkard', hours=15.0, rate=50.6, rate_type=None, rate_overwrite=True, amount=759.0, payment_type='HOURLY'), WorkerCommand(name='Richard L. DiCicco', worker_id='worker_DiCiccoRichardL', registered_name='Richard L. DiCicco', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='NO_PAY')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LJPL933DGNJV', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='salary'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LJPMAC3YFJZ1', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='salary'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=45.0, rate=37.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1687.5, payment_type='hours'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='bonus'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='bonus'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=47.5, rate=32.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1543.75, payment_type='hours'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='bonus'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='bonus'), WorkerCommand(name='Sydney', worker_id='004UWBZQK2AIX72P5H34', registered_name='Sydney Stacey', hours=18.5, rate=32.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=601.25, payment_type='hours'), WorkerCommand(name='Sydney', worker_id='004UWBZQK2AIX72P5H34', registered_name='Sydney Stacey', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='bonus')], agent_2: [WorkerCommand(name='Ross', worker_id='Ross', registered_name='Ross', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Salary'), WorkerCommand(name='Michele', worker_id='Michele', registered_name='Michele', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Salary'), WorkerCommand(name='Danny', worker_id='Danny', registered_name='Danny', hours=45.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Athlete Stipend'), WorkerCommand(name='Danny', worker_id='Danny', registered_name='Danny', hours=45.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Programming Stipend'), WorkerCommand(name='Abby', worker_id='Abby', registered_name='Abby', hours=47.5, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Social Media Stipend'), WorkerCommand(name='Abby', worker_id='Abby', registered_name='Abby', hours=47.5, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Retention Stipend'), WorkerCommand(name='Sydney', worker_id='Sydney', registered_name='Sydney', hours=18.5, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Reign Stipend')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   1\n", "May be some workers terminated since LLM name matching confidence too low for 1/8 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "May be some workers terminated since LLM name matching confidence too low for 1/2 workers: <PERSON><PERSON><PERSON>, <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "Business knockout rules triggered: BILLING_OVERRIDES (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                1\n", "Technical knockout rules triggered: REQUESTS_PRE_PROCESSING_CONFIRMATION (88%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "May be some workers terminated since LLM name matching confidence too low for 1/13 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     1\n", "Payroll validation failed, error: Missing required fields: name, worker_id, and registered_name for the payroll entry.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "Payroll validation failed, error: Missing rate information for <PERSON> (no payRate or rateType provided); missing compensation amount for <PERSON> (no salary amount or payHours provided).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                1\n", "May be some workers terminated since LLM name matching confidence too low for 1/10 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     1\n", "May be some workers terminated since LLM name matching confidence too low for 1/6 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      1\n", "Technical knockout rules triggered: UNADDRESSABLE_THREAD (0%), REQUESTS_PRE_PROCESSING_CONFIRMATION (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 1\n", "Business knockout rules triggered: CHANGES_UPDATES_TO_LABOR_DIST_JOB_COSTING (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='004UWBZQJFNYP1PCKPRT', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='regular'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQJFNYP1PCKPRZ', registered_name='<PERSON><PERSON>', hours=34.5, rate=18.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=None, payment_type='regular'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQJFNYP1PCKPRZ', registered_name='<PERSON><PERSON>', hours=40.0, rate=18.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=None, payment_type='vacation'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQJFNYP1PC<PERSON><PERSON><PERSON>', registered_name='<PERSON><PERSON>', hours=5.5, rate=18.5, rate_type='HOURLY_RATE', rate_overwrite=False, amount=None, payment_type='sick')], agent_2: [<PERSON><PERSON>ommand(name='<PERSON>', worker_id='Joe_worker_id', registered_name='Joe_registered_name', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='regular'), WorkerCommand(name='Jill', worker_id='Jill_worker_id', registered_name='Jill_registered_name', hours=34.5, rate=20.0, rate_type='hourly', rate_overwrite=False, amount=690.0, payment_type='regular'), WorkerCommand(name='Jill', worker_id='Jill_worker_id', registered_name='Jill_registered_name', hours=40.0, rate=20.0, rate_type='hourly', rate_overwrite=False, amount=800.0, payment_type='vacation'), WorkerCommand(name='Jill', worker_id='Jill_worker_id', registered_name='Jill_registered_name', hours=5.5, rate=20.0, rate_type='hourly', rate_overwrite=False, amount=110.0, payment_type='sick'), WorkerCommand(name='Elizabeth', worker_id='Elizabeth_worker_id', registered_name='Elizabeth_registered_name', hours=41.5, rate=20.0, rate_type='hourly', rate_overwrite=False, amount=830.0, payment_type='regular')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      1\n", "Technical knockout rules triggered: HAS_ATTACHMENT (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1\n", "May be some workers terminated since LLM name matching confidence too low for 1/3 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "Business knockout rules triggered: CALL_REQUESTED (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   1\n", "Technical knockout rules triggered: UNADDRESSABLE_THREAD (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             1\n", "Business knockout rules triggered: START_OR_RESUME_BACK_DATED_PAY_PERIOD (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "Technical knockout rules triggered: HAS_ATTACHMENT (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1\n", "Business knockout rules triggered: CHECK_DATE_LT_1_BUSINESS_DAY (90%); Technical knockout rules triggered: HAS_ATTACHMENT (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON><PERSON>', worker_id='004UWBZQJ4926M72WLO8', registered_name='<PERSON><PERSON> L Walker', hours=85.0, rate=20.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1700.0, payment_type='hours'), WorkerCommand(name='<PERSON><PERSON>', worker_id='004UWBZQJ4926M72WLO8', registered_name='Troi L Walker', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=360.0, payment_type='bonus'), WorkerCommand(name='<PERSON><PERSON><PERSON>', worker_id='004UWBZQJ4926M72WLMO', registered_name='Leron<PERSON> F Jackson', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7000.0, payment_type='salary')], agent_2: [WorkerCommand(name='<PERSON><PERSON>', worker_id='TW001', registered_name='T<PERSON> Walker', hours=85.0, rate=None, rate_type=None, rate_overwrite=False, amount=360.0, payment_type='sales bonus'), WorkerCommand(name='<PERSON><PERSON>da <PERSON>', worker_id='LJ001', registered_name='Leronda <PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7000.0, payment_type='regular pay')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 1\n", "Business knockout rules triggered: UPDATE_CLIENT_OR_EE_PAY_COMPONENTS (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               1\n", "No company ID found the CA data for the given display ID: Y6333699, 11023537                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='001GJICFLLCGTHZ2YB9Q', registered_name='<PERSON>', hours=18.0, rate=15.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=270.0, payment_type='hourly'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LHANDYMFZR8X', registered_name='<PERSON>', hours=36.0, rate=15.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=540.0, payment_type='hourly'), WorkerCommand(name='<PERSON>', worker_id='001GJICFLLCGMEQKY7LL', registered_name='<PERSON>', hours=44.0, rate=17.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=748.0, payment_type='hourly'), WorkerCommand(name='<PERSON><PERSON> <PERSON>', worker_id='00M9LQF7LRSLP45C98EU', registered_name='<PERSON><PERSON><PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='salary'), WorkerCommand(name='Rev. <PERSON>', worker_id='00M9LQF7LRSLP45C98EU', registered_name='MacIvan Rogers', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='housing allowance')], agent_2: [WorkerCommand(name='Dawn Gould', worker_id='DawnGould', registered_name='Dawn Gould', hours=18.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='hourly'), WorkerCommand(name='Charlotte Harris', worker_id='CharlotteHarris', registered_name='Charlotte Harris', hours=36.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='hourly'), WorkerCommand(name='Maurice Christopher', worker_id='MauriceChristopher', registered_name='Maurice Christopher', hours=44.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='hourly'), WorkerCommand(name='Rev. Rogers', worker_id='RevRogers', registered_name='Rev. Rogers', hours=None, rate=None, rate_type='salary', rate_overwrite=False, amount=None, payment_type='salary')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     1\n", "Business knockout rules triggered: UPDATE_EE_RATE_OF_PAY (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1\n", "Business knockout rules triggered: CHANGES_UPDATES_TO_LABOR_DIST_JOB_COSTING (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        1\n", "No workers found in LLM match; No matched workers found; No employees detected in the email, got []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       1\n", "Business knockout rules triggered: VENDOR_CHECKS (95%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "Business knockout rules triggered: MANUAL_CHECKS (90%); Technical knockout rules triggered: REQUESTS_PRE_PROCESSING_CONFIRMATION (90%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON><PERSON>', worker_id='00UJ9ANUIZW3XMO1MQGI', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7091.08, payment_type='REGULAR'), WorkerCommand(name='<PERSON><PERSON>', worker_id='00UJ9ANUIZW3XMO1MQ<PERSON>', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=400.0, payment_type='_403_B_'), WorkerCommand(name='<PERSON>', worker_id='00UJ9ANUIZW3XMO2MQGY', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3478.41, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='00UJ9ANUIZW3XMO2MQGY', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=173.92, payment_type='_403_B_'), WorkerCommand(name='<PERSON><PERSON>slin', worker_id='001GJICFLORNH73G3M38', registered_name='Christiana C Goslin', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2362.5, payment_type='REGULAR'), WorkerCommand(name='Eleena Gelfgatt', worker_id='001GJICFLORN5ZBX3LBR', registered_name='Eleena Gelfgatt', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=1190.0, payment_type='REGULAR')], agent_2: [WorkerCommand(name='Rev. Clyde Elledge', worker_id='19', registered_name='Rev. Clyde Elledge', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=7091.08, payment_type='REG'), WorkerCommand(name='Rev. Clyde Elledge', worker_id='19', registered_name='Rev. Clyde Elledge', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=400.0, payment_type='DED'), WorkerCommand(name='Patricia Hahn', worker_id='25', registered_name='Patricia Hahn', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=3478.41, payment_type='REG'), WorkerCommand(name='Patricia Hahn', worker_id='25', registered_name='Patricia Hahn', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=173.92, payment_type='DED'), WorkerCommand(name='Christiana Goslin', worker_id='35', registered_name='Christiana Goslin', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=2362.5, payment_type='REG'), WorkerCommand(name='Christiana Goslin', worker_id='35', registered_name='Christiana Goslin', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='DED'), WorkerCommand(name='Eleena Gelfgatt', worker_id='34', registered_name='Eleena Gelfgatt', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=1190.0, payment_type='REG'), WorkerCommand(name='Eleena Gelfgatt', worker_id='34', registered_name='Eleena Gelfgatt', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=0.0, payment_type='DED')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   1\n", "May be some workers terminated since LLM name matching confidence too low for 1/6 workers: <PERSON> (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON><PERSON><PERSON><PERSON>', worker_id='001GJICFLO3277FHGT7P', registered_name='<PERSON><PERSON><PERSON><PERSON> D N Jones', hours=48.0, rate=70.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=3360.0, payment_type='REGULAR'), WorkerCommand(name='<PERSON><PERSON>', worker_id='00UJ9ANUJ1LS3JS25EVY', registered_name='<PERSON><PERSON>', hours=80.0, rate=24.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1920.0, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='00UJ9ANUJ1LS3JS25EWS', registered_name='<PERSON>', hours=80.0, rate=17.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1360.0, payment_type='REGULAR'), WorkerCommand(name='<PERSON>', worker_id='00UJ9ANUJ1LS3JS25EW6', registered_name='<PERSON>', hours=74.0, rate=25.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=1850.0, payment_type='REGULAR'), WorkerCommand(name='Sharon Shelton', worker_id='00UJ9ANUJ1LS3JS25EW6', registered_name='Sharon L <PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=250.0, payment_type='SUPPLEMENTAL'), WorkerCommand(name='Nicole Wames', worker_id='004UWBZQKIONOMDCQLB3', registered_name='Nicole A Wames', hours=44.0, rate=65.0, rate_type='HOURLY_RATE', rate_overwrite=False, amount=2860.0, payment_type='REGULAR')], agent_2: [WorkerCommand(name='Brittney Jones', worker_id='00UJ9ANUIB6DJDDEE7NA_brittney_jones', registered_name='Brittney Jones', hours=48.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='REG'), WorkerCommand(name='Terri Keller', worker_id='00UJ9ANUIB6DJDDEE7NA_terri_keller', registered_name='Terri Keller', hours=80.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='REG'), WorkerCommand(name='Tara Moore', worker_id='00UJ9ANUIB6DJDDEE7NA_tara_moore', registered_name='Tara Moore', hours=80.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='REG'), WorkerCommand(name='Sharon Shelton', worker_id='00UJ9ANUIB6DJDDEE7NA_sharon_shelton', registered_name='Sharon Shelton', hours=74.0, rate=None, rate_type=None, rate_overwrite=None, amount=250.0, payment_type='REG+BONUS'), WorkerCommand(name='Nicole Wames', worker_id='00UJ9ANUIB6DJDDEE7NA_nicole_wames', registered_name='Nicole Wames', hours=44.0, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='REG')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           1\n", "May be some workers terminated since LLM name matching confidence too low for 1/5 workers: myself (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "May be some workers terminated since LLM name matching confidence too low for 1/4 workers: myself (0%)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1\n", "Payroll commands mismatch, agent_1: [WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LJPL933DGNJV', registered_name='<PERSON>', hours=None, rate=None, rate_type='salary', rate_overwrite=None, amount=None, payment_type='salary'), WorkerCommand(name='<PERSON>', worker_id='00M9LQF7LJPMAC3YFJZ1', registered_name='<PERSON>', hours=None, rate=None, rate_type='salary', rate_overwrite=None, amount=None, payment_type='salary'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=45.0, rate=37.5, rate_type='hourly', rate_overwrite=False, amount=1687.5, payment_type='hours'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='Athlete Stipend'), WorkerCommand(name='<PERSON>', worker_id='004UWBZQKGLA307ICDCD', registered_name='<PERSON>', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='Programming Stipend'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=36.0, rate=32.5, rate_type='hourly', rate_overwrite=False, amount=1170.0, payment_type='hours'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='Social Media Stipend'), WorkerCommand(name='Abby', worker_id='001GJICFLZ7EIXJ74K2R', registered_name='Abigail R Meronek', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='Retention Stipend'), WorkerCommand(name='Sydney', worker_id='004UWBZQK2AIX72P5H34', registered_name='Sydney Stacey', hours=12.0, rate=32.5, rate_type='hourly', rate_overwrite=False, amount=390.0, payment_type='hours'), WorkerCommand(name='Sydney', worker_id='004UWBZQK2AIX72P5H34', registered_name='Sydney Stacey', hours=None, rate=None, rate_type=None, rate_overwrite=None, amount=None, payment_type='Reign Stipend')], agent_2: [WorkerCommand(name='Ross', worker_id='ROSS', registered_name='Ross', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Salary'), WorkerCommand(name='Michele', worker_id='MICHELE', registered_name='Michele', hours=None, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Salary'), WorkerCommand(name='Danny', worker_id='DANNY', registered_name='Danny', hours=45.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Athlete Stipend'), WorkerCommand(name='Danny', worker_id='DANNY', registered_name='Danny', hours=45.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Programming Stipend'), WorkerCommand(name='Abby', worker_id='ABBY', registered_name='Abby', hours=36.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Social Media Stipend'), WorkerCommand(name='Abby', worker_id='ABBY', registered_name='Abby', hours=36.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Retention Stipend'), WorkerCommand(name='Sydney', worker_id='SYDNEY', registered_name='Sydney', hours=12.0, rate=None, rate_type=None, rate_overwrite=False, amount=None, payment_type='Reign Stipend')]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 1\n", "Name: count, dtype: int64"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[condition].drop_duplicates('New GT ID')[\"termination_reason\"].value_counts()"]}, {"cell_type": "code", "execution_count": 69, "id": "2803acc350b8e215", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T00:06:32.233290Z", "start_time": "2025-08-06T00:06:32.229709Z"}}, "outputs": [{"data": {"text/plain": ["(49, 19)"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[~condition].drop_duplicates('New GT ID').drop_duplicates('New GT ID').shape"]}, {"cell_type": "code", "execution_count": 70, "id": "a108b9f9632693bf", "metadata": {"ExecuteTime": {"end_time": "2025-08-06T00:06:33.567058Z", "start_time": "2025-08-06T00:06:33.562442Z"}}, "outputs": [{"data": {"text/plain": ["termination_graph\n", "classification_output_state    46\n", "                                3\n", "Name: count, dtype: int64"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[~condition].drop_duplicates('New GT ID')[\"termination_graph\"].value_counts().head(10)"]}, {"cell_type": "code", "execution_count": 71, "id": "6d9e3a03aca35160", "metadata": {"ExecuteTime": {"end_time": "2025-08-05T19:37:29.963033Z", "start_time": "2025-08-05T19:37:29.951164Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email ID from source</th>\n", "      <th>EmailContent</th>\n", "      <th>companyID</th>\n", "      <th>Worker Name</th>\n", "      <th>Worker ID</th>\n", "      <th>Legal Name</th>\n", "      <th>payType</th>\n", "      <th>payAmount</th>\n", "      <th>payHours</th>\n", "      <th>payRate</th>\n", "      <th>rateType</th>\n", "      <th>rate_overwrite</th>\n", "      <th>Commands in email</th>\n", "      <th>run_time</th>\n", "      <th>successful_flag</th>\n", "      <th>termination_graph</th>\n", "      <th>termination_reason</th>\n", "      <th>valid_cases</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>55</td>\n", "      <td>2763</td>\n", "      <td>Hi Tri<PERSON>\\n\\nI\"M going to make checks inhouse ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>23.02</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CANCEL_DELE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>54</td>\n", "      <td>2759</td>\n", "      <td>Hi <PERSON><PERSON>\\n\\nPlease just send me stubs only an...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>32.56</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: MANUAL_CHEC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>106</td>\n", "      <td>1888</td>\n", "      <td>hi vanessa\\n \\ncan you help me enter a manual ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>19.35</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>107</td>\n", "      <td>151</td>\n", "      <td>Good morning,\\nPlease see attached forms for n...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>14.65</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: ADD_WORKER ...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>108</td>\n", "      <td>1722</td>\n", "      <td>Hello, \\n \\nPlease process <PERSON>'s te...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>18.60</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: TERMINATE_W...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>111</td>\n", "      <td>5989</td>\n", "      <td><PERSON>,\\n\\nHere is the payroll for Signet Pro...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>22.94</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: TERMINATE_W...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>109</td>\n", "      <td>1912</td>\n", "      <td>&gt; Good afternoon, <PERSON>!\\n&gt;\\n&gt; I have terminate...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>34.88</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>110</td>\n", "      <td>4719</td>\n", "      <td><PERSON>,\\n \\nGood morning.\\nPlease run a pay...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>32.51</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_CLIE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>112</td>\n", "      <td>2868</td>\n", "      <td><PERSON><PERSON> - Regular salary\\nTheodore Foster...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>29.26</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_R...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>115</td>\n", "      <td>6434</td>\n", "      <td>\\nHi Dean,\\nTheresa's payroll may process as n...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>12.91</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>113</td>\n", "      <td>5940</td>\n", "      <td>Hi Precious\\n\\nPlease see April Payroll data. ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>27.98</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_R...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>27</td>\n", "      <td>2326</td>\n", "      <td>Good morning, <PERSON><PERSON>,\\n\\n\\nI am at my daughter...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>27.55</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Technical knockout rules triggered: REQUESTS_P...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>114</td>\n", "      <td></td>\n", "      <td>\\n\\n\\n\\nPayroll for the week is below.\\n \\n1. ...</td>\n", "      <td>00Z1IQF9IBN6K6CMTBFN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>004UWBZQKGTRZ42S8JGA</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>SALARY</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>False</td>\n", "      <td>3</td>\n", "      <td>264.69</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>347</th>\n", "      <td>93</td>\n", "      <td>3559</td>\n", "      <td><PERSON> <PERSON>,\\n\\nPlease see below and let me know ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>27.68</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_R...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>348</th>\n", "      <td>116</td>\n", "      <td>2452</td>\n", "      <td>*Hello -- below is our payroll. *PLEASE* send ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>24.83</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: MANUAL_CHEC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>350</th>\n", "      <td>179</td>\n", "      <td>5127</td>\n", "      <td>From:  <EMAIL>\\nTo:  gippolito@pa...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>10.09</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>352</th>\n", "      <td>178</td>\n", "      <td>3330</td>\n", "      <td>Hello <PERSON>,\\n\\nHere is the payroll data for...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>21.47</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: ADDING_MULT...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>353</th>\n", "      <td>182</td>\n", "      <td>4631</td>\n", "      <td>Good morning, <PERSON><PERSON>.\\n\\nI will be out of the...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>6.63</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354</th>\n", "      <td>180</td>\n", "      <td>2457</td>\n", "      <td>&gt; *From: * <EMAIL>\\n&gt;...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>25.10</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: MANUAL_CHEC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>181</td>\n", "      <td>5087</td>\n", "      <td>Hello,\\n\\n  Can you please enter a #2 check: M...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>24.09</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: ADDING_MULT...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>177</td>\n", "      <td>828</td>\n", "      <td>DHK Property Three LLC \\nPay period  5 / 02 /2...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>43.58</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: ADDING_MULT...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>184</td>\n", "      <td>1388</td>\n", "      <td>From:  br<PERSON><PERSON><PERSON>@gmail.com\\nTo:  prlee1@payc...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>5.24</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>185</td>\n", "      <td>7218</td>\n", "      <td>Yes, we don't have employees to pay for that ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>9.58</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>183</td>\n", "      <td>5029</td>\n", "      <td>Hello <PERSON>,\\n\\nI work with <PERSON> at Qu...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>21.85</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CALL_REQUES...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>365</th>\n", "      <td>188</td>\n", "      <td>1181</td>\n", "      <td>Hello, <PERSON>,\\n\\nCould you please set up a s...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>5.58</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>366</th>\n", "      <td>186</td>\n", "      <td>6708</td>\n", "      <td><PERSON>,\\n\\nAn issue discovered today...\\n\\...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>10.96</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>367</th>\n", "      <td>190</td>\n", "      <td>2098</td>\n", "      <td>Hello\\n\\nA rehired associates hours did not po...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>9.77</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>369</th>\n", "      <td>187</td>\n", "      <td>1110</td>\n", "      <td>Hello <PERSON>: Kindly see the attached Time Sh...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>29.38</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>191</td>\n", "      <td>2631</td>\n", "      <td><PERSON>,\\n\\nPlease run two off-cycle checks...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>23.25</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>189</td>\n", "      <td>2017</td>\n", "      <td>Hello,\\n\\nI have a terminated associate that s...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>25.84</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>193</td>\n", "      <td>4513</td>\n", "      <td><PERSON>,\\n\\nHappy Monday! I hope you had a ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>17.58</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: NET_TO_GROS...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>192</td>\n", "      <td>2495</td>\n", "      <td><PERSON>,\\n\\nI was suppose to give <PERSON><PERSON> a r...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>29.29</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_R...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td>195</td>\n", "      <td>816</td>\n", "      <td>Hello <PERSON><PERSON>,\\nCan i get a final check cut f...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>20.79</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: CREATE_OFF_...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376</th>\n", "      <td>196</td>\n", "      <td>3537</td>\n", "      <td>Riverside Family Dentistry, P.C.\\nClient #005...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>28.45</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: MANUAL_CHEC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>377</th>\n", "      <td>198</td>\n", "      <td>3359</td>\n", "      <td>Hello <PERSON>,\\nPayroll is as follows.\\n\\nMike- ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>25.12</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: NET_TO_GROS...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>197</td>\n", "      <td>2953</td>\n", "      <td>payroll\\neleahjean nilo 83;45 HReunica serafi...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>27.14</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: NET_TO_GROS...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>379</th>\n", "      <td>194</td>\n", "      <td>4514</td>\n", "      <td><PERSON>,\\n\\nI hope you are well and had a ni...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>41.81</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: NET_TO_GROS...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>380</th>\n", "      <td>200</td>\n", "      <td>7199</td>\n", "      <td>Hello Malia\\n\\nNo, we're actually excluding t...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>19.19</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: NET_TO_GROS...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>381</th>\n", "      <td>206</td>\n", "      <td>1583</td>\n", "      <td>Can you please change the name on the check o...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>6.58</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Triage failed: EnterPayroll_below_knockout_thr...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>382</th>\n", "      <td>201</td>\n", "      <td>1397</td>\n", "      <td><PERSON> and I have reviewed your backda...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>24.17</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: START_OR_RE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>383</th>\n", "      <td>203</td>\n", "      <td>6539</td>\n", "      <td><PERSON>, \\n \\nPlease see our withholding ...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>26.13</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_CLIE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>384</th>\n", "      <td>205</td>\n", "      <td>5829</td>\n", "      <td>Just submitted payroll please let me know when...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>26.53</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_CLIE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>385</th>\n", "      <td>202</td>\n", "      <td>3791</td>\n", "      <td>From: <EMAIL>&lt;mailto:morgan@ful...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>29.14</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: START_OR_RE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>390</th>\n", "      <td>204</td>\n", "      <td>1479</td>\n", "      <td>Good Morning Rose,\\n\\nThis is for pay period e...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>29.06</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_CLIE...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>391</th>\n", "      <td>207</td>\n", "      <td>5696</td>\n", "      <td>Hello,  Attached is the Galaxy Payroll for WE...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>23.47</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_D...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>392</th>\n", "      <td>208</td>\n", "      <td>3016</td>\n", "      <td>Good morning, <PERSON> needs the bank inf...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>26.65</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_D...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>393</th>\n", "      <td>209</td>\n", "      <td>5689</td>\n", "      <td>Hello <PERSON>,\\n\\nKindly add the below listed ne...</td>\n", "      <td></td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>0</td>\n", "      <td>24.78</td>\n", "      <td>False</td>\n", "      <td>classification_output_state</td>\n", "      <td>Business knockout rules triggered: UPDATE_EE_S...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>395</th>\n", "      <td>25</td>\n", "      <td></td>\n", "      <td>Hello <PERSON>, here is payroll for this pay per...</td>\n", "      <td>001GJICFLVY1N10DI05C</td>\n", "      <td><PERSON></td>\n", "      <td>001GJICFLWTAT95FC6QB</td>\n", "      <td><PERSON></td>\n", "      <td>Hourly</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>25.79</td>\n", "      <td>19.0</td>\n", "      <td>HOURLY_RATE</td>\n", "      <td>False</td>\n", "      <td>7</td>\n", "      <td>424.09</td>\n", "      <td>False</td>\n", "      <td>release_output_state</td>\n", "      <td>Payrolls requires validation, status: Client e...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>402</th>\n", "      <td>199</td>\n", "      <td></td>\n", "      <td>Payroll for 4/30/25 is as follows:\\nCheri Jenk...</td>\n", "      <td>00JD49W7IB6HSVP7G2L3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>004UWBZQJ3IHKI9ZAJRX</td>\n", "      <td>Ch<PERSON></td>\n", "      <td>hours</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>68.0</td>\n", "      <td>60.0</td>\n", "      <td>HOURLY_RATE</td>\n", "      <td>False</td>\n", "      <td>6</td>\n", "      <td>352.24</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     New GT ID Email ID from source  \\\n", "31          55                 2763   \n", "32          54                 2759   \n", "33         106                 1888   \n", "34         107                  151   \n", "38         108                 1722   \n", "39         111                 5989   \n", "40         109                 1912   \n", "41         110                 4719   \n", "43         112                 2868   \n", "44         115                 6434   \n", "46         113                 5940   \n", "52          27                 2326   \n", "75         114                        \n", "347         93                 3559   \n", "348        116                 2452   \n", "350        179                 5127   \n", "352        178                 3330   \n", "353        182                 4631   \n", "354        180                 2457   \n", "360        181                 5087   \n", "361        177                  828   \n", "362        184                 1388   \n", "363        185                 7218   \n", "364        183                 5029   \n", "365        188                 1181   \n", "366        186                 6708   \n", "367        190                 2098   \n", "369        187                 1110   \n", "370        191                 2631   \n", "371        189                 2017   \n", "372        193                 4513   \n", "373        192                 2495   \n", "374        195                  816   \n", "376        196                 3537   \n", "377        198                 3359   \n", "378        197                 2953   \n", "379        194                 4514   \n", "380        200                 7199   \n", "381        206                 1583   \n", "382        201                 1397   \n", "383        203                 6539   \n", "384        205                 5829   \n", "385        202                 3791   \n", "390        204                 1479   \n", "391        207                 5696   \n", "392        208                 3016   \n", "393        209                 5689   \n", "395         25                        \n", "402        199                        \n", "\n", "                                          EmailContent             companyID  \\\n", "31   Hi Tricia\\n\\nI\"M going to make checks inhouse ...                         \n", "32   Hi Tricia\\n\\nPlease just send me stubs only an...                         \n", "33   hi vanessa\\n \\ncan you help me enter a manual ...                         \n", "34   Good morning,\\nPlease see attached forms for n...                         \n", "38   Hello, \\n \\nPlease process <PERSON>'s te...                         \n", "39   Hi <PERSON>,\\n\\nHere is the payroll for Signet Pro...                         \n", "40   > Good afternoon, <PERSON>!\\n>\\n> I have terminate...                         \n", "41   Hi <PERSON>,\\n \\nGood morning.\\nPlease run a pay...                         \n", "43   <PERSON><PERSON> - Regular salary\\nTheodore <PERSON>...                         \n", "44   \\nHi Dean,\\nTheresa's payroll may process as n...                         \n", "46   Hi Precious\\n\\nPlease see April Payroll data. ...                         \n", "52   Good morning, <PERSON><PERSON>,\\n\\n\\nI am at my daughter...                         \n", "75   \\n\\n\\n\\nPayroll for the week is below.\\n \\n1. ...  00Z1IQF9IBN6K6CMTBFN   \n", "347  <PERSON>,\\n\\nPlease see below and let me know ...                         \n", "348  *Hello -- below is our payroll. *PLEASE* send ...                         \n", "350   From:  <EMAIL>\\nTo:  gippolito@pa...                         \n", "352  Hello <PERSON>,\\n\\nHere is the payroll data for...                         \n", "353  Good morning, <PERSON><PERSON>.\\n\\nI will be out of the...                         \n", "354   > *From: * <EMAIL>\\n>...                         \n", "360  Hello,\\n\\n  Can you please enter a #2 check: M...                         \n", "361  DHK Property Three LLC \\nPay period  5 / 02 /2...                         \n", "362   From:  br<PERSON><PERSON><PERSON>@gmail.com\\nTo:  prlee1@payc...                         \n", "363   Yes, we don't have employees to pay for that ...                         \n", "364  <PERSON>,\\n\\nI work with <PERSON> at Qu...                         \n", "365  Hello, <PERSON>,\\n\\nCould you please set up a s...                         \n", "366  <PERSON>,\\n\\nAn issue discovered today...\\n\\...                         \n", "367  Hello\\n\\nA rehired associates hours did not po...                         \n", "369  Hello <PERSON>: Kindly see the attached Time Sh...                         \n", "370  Hi <PERSON>,\\n\\nPlease run two off-cycle checks...                         \n", "371  Hello,\\n\\nI have a terminated associate that s...                         \n", "372   <PERSON>,\\n\\nHappy Monday! I hope you had a ...                         \n", "373  <PERSON>,\\n\\nI was suppose to give <PERSON><PERSON> a r...                         \n", "374  <PERSON>,\\nCan i get a final check cut f...                         \n", "376   Riverside Family Dentistry, P.C.\\nClient #005...                         \n", "377  Hello <PERSON>,\\nPayroll is as follows.\\n\\nMike- ...                         \n", "378   payroll\\neleahjean nilo 83;45 HReunica serafi...                         \n", "379  <PERSON>,\\n\\nI hope you are well and had a ni...                         \n", "380   Hello Malia\\n\\nNo, we're actually excluding t...                         \n", "381   Can you please change the name on the check o...                         \n", "382  <PERSON> and I have reviewed your backda...                         \n", "383   <PERSON>, \\n \\nPlease see our withholding ...                         \n", "384  Just submitted payroll please let me know when...                         \n", "385   From: <EMAIL><mailto:morgan@ful...                         \n", "390  Good Morning Rose,\\n\\nThis is for pay period e...                         \n", "391   Hello,  Attached is the Galaxy Payroll for WE...                         \n", "392  Good morning, <PERSON> needs the bank inf...                         \n", "393  Hello <PERSON>,\\n\\n<PERSON><PERSON><PERSON> add the below listed ne...                         \n", "395  Hello Jeremy, here is payroll for this pay per...  001GJICFLVY1N10DI05C   \n", "402  Payroll for 4/30/25 is as follows:\\nCheri Jenk...  00JD49W7IB6HSVP7G2L3   \n", "\n", "              Worker Name             Worker ID                    Legal Name  \\\n", "31                   <NA>                  <NA>                          <NA>   \n", "32                   <NA>                  <NA>                          <NA>   \n", "33                   <NA>                  <NA>                          <NA>   \n", "34                   <NA>                  <NA>                          <NA>   \n", "38                   <NA>                  <NA>                          <NA>   \n", "39                   <NA>                  <NA>                          <NA>   \n", "40                   <NA>                  <NA>                          <NA>   \n", "41                   <NA>                  <NA>                          <NA>   \n", "43                   <NA>                  <NA>                          <NA>   \n", "44                   <NA>                  <NA>                          <NA>   \n", "46                   <NA>                  <NA>                          <NA>   \n", "52                   <NA>                  <NA>                          <NA>   \n", "75   Nicolette Washington  004UWBZQKGTRZ42S8JGA  Nicolette De Anna Washington   \n", "347                  <NA>                  <NA>                          <NA>   \n", "348                  <NA>                  <NA>                          <NA>   \n", "350                  <NA>                  <NA>                          <NA>   \n", "352                  <NA>                  <NA>                          <NA>   \n", "353                  <NA>                  <NA>                          <NA>   \n", "354                  <NA>                  <NA>                          <NA>   \n", "360                  <NA>                  <NA>                          <NA>   \n", "361                  <NA>                  <NA>                          <NA>   \n", "362                  <NA>                  <NA>                          <NA>   \n", "363                  <NA>                  <NA>                          <NA>   \n", "364                  <NA>                  <NA>                          <NA>   \n", "365                  <NA>                  <NA>                          <NA>   \n", "366                  <NA>                  <NA>                          <NA>   \n", "367                  <NA>                  <NA>                          <NA>   \n", "369                  <NA>                  <NA>                          <NA>   \n", "370                  <NA>                  <NA>                          <NA>   \n", "371                  <NA>                  <NA>                          <NA>   \n", "372                  <NA>                  <NA>                          <NA>   \n", "373                  <NA>                  <NA>                          <NA>   \n", "374                  <NA>                  <NA>                          <NA>   \n", "376                  <NA>                  <NA>                          <NA>   \n", "377                  <NA>                  <NA>                          <NA>   \n", "378                  <NA>                  <NA>                          <NA>   \n", "379                  <NA>                  <NA>                          <NA>   \n", "380                  <NA>                  <NA>                          <NA>   \n", "381                  <NA>                  <NA>                          <NA>   \n", "382                  <NA>                  <NA>                          <NA>   \n", "383                  <NA>                  <NA>                          <NA>   \n", "384                  <NA>                  <NA>                          <NA>   \n", "385                  <NA>                  <NA>                          <NA>   \n", "390                  <NA>                  <NA>                          <NA>   \n", "391                  <NA>                  <NA>                          <NA>   \n", "392                  <NA>                  <NA>                          <NA>   \n", "393                  <NA>                  <NA>                          <NA>   \n", "395     <PERSON>  001GJICFLWTAT95FC6QB           <PERSON>   \n", "402         Cheri <PERSON>  004UWBZQJ3IHKI9ZAJRX               Cheri <PERSON>   \n", "\n", "    payType payAmount payHours payRate     rateType rate_overwrite  \\\n", "31     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "32     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "33     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "34     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "38     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "39     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "40     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "41     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "43     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "44     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "46     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "52     <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "75   SALARY      <NA>     <NA>    <NA>         <NA>          False   \n", "347    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "348    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "350    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "352    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "353    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "354    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "360    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "361    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "362    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "363    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "364    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "365    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "366    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "367    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "369    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "370    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "371    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "372    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "373    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "374    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "376    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "377    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "378    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "379    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "380    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "381    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "382    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "383    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "384    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "385    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "390    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "391    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "392    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "393    <NA>      <NA>     <NA>    <NA>         <NA>           <NA>   \n", "395  Hourly      <NA>    25.79    19.0  HOURLY_RATE          False   \n", "402   hours      <NA>     68.0    60.0  HOURLY_RATE          False   \n", "\n", "     Commands in email  run_time  successful_flag  \\\n", "31                   0     23.02            False   \n", "32                   0     32.56            False   \n", "33                   0     19.35            False   \n", "34                   0     14.65            False   \n", "38                   0     18.60            False   \n", "39                   0     22.94            False   \n", "40                   0     34.88            False   \n", "41                   0     32.51            False   \n", "43                   0     29.26            False   \n", "44                   0     12.91            False   \n", "46                   0     27.98            False   \n", "52                   0     27.55            False   \n", "75                   3    264.69             True   \n", "347                  0     27.68            False   \n", "348                  0     24.83            False   \n", "350                  0     10.09            False   \n", "352                  0     21.47            False   \n", "353                  0      6.63            False   \n", "354                  0     25.10            False   \n", "360                  0     24.09            False   \n", "361                  0     43.58            False   \n", "362                  0      5.24            False   \n", "363                  0      9.58            False   \n", "364                  0     21.85            False   \n", "365                  0      5.58            False   \n", "366                  0     10.96            False   \n", "367                  0      9.77            False   \n", "369                  0     29.38            False   \n", "370                  0     23.25            False   \n", "371                  0     25.84            False   \n", "372                  0     17.58            False   \n", "373                  0     29.29            False   \n", "374                  0     20.79            False   \n", "376                  0     28.45            False   \n", "377                  0     25.12            False   \n", "378                  0     27.14            False   \n", "379                  0     41.81            False   \n", "380                  0     19.19            False   \n", "381                  0      6.58            False   \n", "382                  0     24.17            False   \n", "383                  0     26.13            False   \n", "384                  0     26.53            False   \n", "385                  0     29.14            False   \n", "390                  0     29.06            False   \n", "391                  0     23.47            False   \n", "392                  0     26.65            False   \n", "393                  0     24.78            False   \n", "395                  7    424.09            False   \n", "402                  6    352.24             True   \n", "\n", "               termination_graph  \\\n", "31   classification_output_state   \n", "32   classification_output_state   \n", "33   classification_output_state   \n", "34   classification_output_state   \n", "38   classification_output_state   \n", "39   classification_output_state   \n", "40   classification_output_state   \n", "41   classification_output_state   \n", "43   classification_output_state   \n", "44   classification_output_state   \n", "46   classification_output_state   \n", "52   classification_output_state   \n", "75                                 \n", "347  classification_output_state   \n", "348  classification_output_state   \n", "350  classification_output_state   \n", "352  classification_output_state   \n", "353  classification_output_state   \n", "354  classification_output_state   \n", "360  classification_output_state   \n", "361  classification_output_state   \n", "362  classification_output_state   \n", "363  classification_output_state   \n", "364  classification_output_state   \n", "365  classification_output_state   \n", "366  classification_output_state   \n", "367  classification_output_state   \n", "369  classification_output_state   \n", "370  classification_output_state   \n", "371  classification_output_state   \n", "372  classification_output_state   \n", "373  classification_output_state   \n", "374  classification_output_state   \n", "376  classification_output_state   \n", "377  classification_output_state   \n", "378  classification_output_state   \n", "379  classification_output_state   \n", "380  classification_output_state   \n", "381  classification_output_state   \n", "382  classification_output_state   \n", "383  classification_output_state   \n", "384  classification_output_state   \n", "385  classification_output_state   \n", "390  classification_output_state   \n", "391  classification_output_state   \n", "392  classification_output_state   \n", "393  classification_output_state   \n", "395         release_output_state   \n", "402                                \n", "\n", "                                    termination_reason  valid_cases  \n", "31   Business knockout rules triggered: CANCEL_DELE...        False  \n", "32   Business knockout rules triggered: MANUAL_CHEC...        False  \n", "33   Business knockout rules triggered: CREATE_OFF_...        False  \n", "34   Business knockout rules triggered: ADD_WORKER ...        False  \n", "38   Business knockout rules triggered: TERMINATE_W...        False  \n", "39   Business knockout rules triggered: TERMINATE_W...        False  \n", "40   Business knockout rules triggered: CREATE_OFF_...        False  \n", "41   Business knockout rules triggered: UPDATE_CLIE...        False  \n", "43   Business knockout rules triggered: UPDATE_EE_R...        False  \n", "44   Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "46   Business knockout rules triggered: UPDATE_EE_R...        False  \n", "52   Technical knockout rules triggered: REQUESTS_P...        False  \n", "75                                                            False  \n", "347  Business knockout rules triggered: UPDATE_EE_R...        False  \n", "348  Business knockout rules triggered: MANUAL_CHEC...        False  \n", "350  Triage failed: EnterPayroll_below_knockout_thr...        False  \n", "352  Business knockout rules triggered: ADDING_MULT...        False  \n", "353  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "354  Business knockout rules triggered: MANUAL_CHEC...        False  \n", "360  Business knockout rules triggered: ADDING_MULT...        False  \n", "361  Business knockout rules triggered: ADDING_MULT...        False  \n", "362  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "363  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "364  Business knockout rules triggered: CALL_REQUES...        False  \n", "365  Triage failed: EnterPayroll_below_knockout_thr...        False  \n", "366  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "367  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "369  Business knockout rules triggered: CREATE_OFF_...        False  \n", "370  Business knockout rules triggered: CREATE_OFF_...        False  \n", "371  Business knockout rules triggered: CREATE_OFF_...        False  \n", "372  Business knockout rules triggered: NET_TO_GROS...        False  \n", "373  Business knockout rules triggered: UPDATE_EE_R...        False  \n", "374  Business knockout rules triggered: CREATE_OFF_...        False  \n", "376  Business knockout rules triggered: MANUAL_CHEC...        False  \n", "377  Business knockout rules triggered: NET_TO_GROS...        False  \n", "378  Business knockout rules triggered: NET_TO_GROS...        False  \n", "379  Business knockout rules triggered: NET_TO_GROS...        False  \n", "380  Business knockout rules triggered: NET_TO_GROS...        False  \n", "381  Triage failed: EnterPayroll_below_knockout_thr...        <PERSON><PERSON>e  \n", "382  Business knockout rules triggered: START_OR_RE...        False  \n", "383  Business knockout rules triggered: UPDATE_CLIE...        False  \n", "384  Business knockout rules triggered: UPDATE_CLIE...        False  \n", "385  Business knockout rules triggered: START_OR_RE...        False  \n", "390  Business knockout rules triggered: UPDATE_CLIE...        False  \n", "391  Business knockout rules triggered: UPDATE_EE_D...        False  \n", "392  Business knockout rules triggered: UPDATE_EE_D...        False  \n", "393  Business knockout rules triggered: UPDATE_EE_S...        False  \n", "395  Payrolls requires validation, status: Client e...        False  \n", "402                                                           False  "]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.loc[~condition].drop_duplicates('New GT ID')"]}, {"cell_type": "markdown", "id": "6bc9609f9d29fe1a", "metadata": {}, "source": ["### Load runs"]}, {"cell_type": "code", "execution_count": 27, "id": "1df5731011c45468", "metadata": {"ExecuteTime": {"end_time": "2025-07-25T07:08:57.757415Z", "start_time": "2025-07-25T07:08:57.737787Z"}}, "outputs": [], "source": ["runs = pd.read_excel(\"runs.xlsx\")"]}, {"cell_type": "code", "execution_count": 28, "id": "d335e1fe407a4872", "metadata": {"ExecuteTime": {"end_time": "2025-07-25T07:09:01.549346Z", "start_time": "2025-07-25T07:09:01.545036Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email ID from source</th>\n", "      <th>EmailContent</th>\n", "      <th>companyID</th>\n", "      <th>Worker Name</th>\n", "      <th>Worker ID</th>\n", "      <th>Legal Name</th>\n", "      <th>payType</th>\n", "      <th>payAmount</th>\n", "      <th>payHours</th>\n", "      <th>payRate</th>\n", "      <th>rate_overwrite</th>\n", "      <th>Commands in email</th>\n", "      <th>run_time</th>\n", "      <th>successful_flag</th>\n", "      <th>termination_graph</th>\n", "      <th>termination_reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9</td>\n", "      <td>28.0</td>\n", "      <td>Hello <PERSON><PERSON> had 32 hours. <PERSON></td>\n", "      <td>VTA10793</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>15.13</td>\n", "      <td>False</td>\n", "      <td>company_worker_lookup_output_state</td>\n", "      <td>May be some workers terminated since LLM name ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   New GT ID  Email ID from source                             EmailContent  \\\n", "0          9                  28.0  <PERSON> had 32 hours. <PERSON>   \n", "\n", "  companyID Worker Name Worker ID Legal Name payType  payAmount  payHours  \\\n", "0  VTA10793         NaN       NaN        NaN     NaN        NaN       NaN   \n", "\n", "   payRate  rate_overwrite  Commands in email  run_time  successful_flag  \\\n", "0      NaN             NaN                  0     15.13            False   \n", "\n", "                    termination_graph  \\\n", "0  company_worker_lookup_output_state   \n", "\n", "                                  termination_reason  \n", "0  May be some workers terminated since LLM name ...  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["runs.head(1)"]}, {"cell_type": "code", "execution_count": 72, "id": "a979b2c79dd88826", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T01:58:06.628880Z", "start_time": "2025-07-31T01:58:06.626218Z"}}, "outputs": [{"data": {"text/plain": ["(19, 14)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"New GT ID\"] = df[\"New GT ID\"].astype(int)\n", "df[df[\"New GT ID\"]<= 40].shape"]}, {"cell_type": "code", "execution_count": 73, "id": "4dabe21b8cad6d2e", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T01:58:45.909771Z", "start_time": "2025-07-31T01:58:45.903240Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New GT ID</th>\n", "      <th>Email source</th>\n", "      <th>Email ID from source</th>\n", "      <th>Testing Case</th>\n", "      <th>Testing category</th>\n", "      <th>Cases covered</th>\n", "      <th>Status</th>\n", "      <th>Full email</th>\n", "      <th>Email to use</th>\n", "      <th>Email subject</th>\n", "      <th>TImeStamp</th>\n", "      <th>To_email</th>\n", "      <th>From_email</th>\n", "      <th>Customer ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Original 100</td>\n", "      <td>5</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS, AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>Good morning:\\n\\nPlease make and process all p...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-15 17:51:06.753</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>11111181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Original 100</td>\n", "      <td>7</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>70126790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Original 100</td>\n", "      <td>7</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>Thank you payroll w/e 4/15, checks dated 4/18\\...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-16 14:14:34.363</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>70126790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Original 100</td>\n", "      <td>8</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>Please pay clayton <PERSON> 1000 check date 4/2...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 11:43:09.923</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>18143909</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>Original 100</td>\n", "      <td>9</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>Hello,\\n\\nPlease see payroll below from last w...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-21 17:47:02.330</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>Y6463960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8</td>\n", "      <td>Original 100</td>\n", "      <td>12</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT, ZERO, REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Nelson Overstreet $3200.00\\nShari Overstreet $...</td>\n", "      <td>Nelson Overstreet $3200.00\\nShari Overstreet $...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-22 16:01:03.827</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>18163405</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>9</td>\n", "      <td>Original 100</td>\n", "      <td>28</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello <PERSON><PERSON> had 32 hours. <PERSON>310.614...</td>\n", "      <td>Hello <PERSON><PERSON> had 32 hours. <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>2025-04-09 17:00:20.227</td>\n", "      <td><EMAIL></td>\n", "      <td>er<PERSON><PERSON><PERSON>@icloud.com</td>\n", "      <td>VTA10793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16</td>\n", "      <td>Original 100</td>\n", "      <td>69</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>STANDARD_SALARY</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Stacy Group, Inc.\\n0064-9813\\nPaycheck Date - ...</td>\n", "      <td>Stacy Group, Inc.\\n0064-9813\\nPaycheck Date - ...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-24 13:37:45.667</td>\n", "      <td><EMAIL></td>\n", "      <td>l<PERSON><EMAIL></td>\n", "      <td>649813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>19</td>\n", "      <td>Original 100</td>\n", "      <td>79</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>,\\nEmpire Energy Specialists Inc pa...</td>\n", "      <td><PERSON>,\\nEmpire Energy Specialists Inc pa...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-28 12:15:03.630</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>16044551</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>22</td>\n", "      <td>Original 100</td>\n", "      <td>82</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>AMOUNT</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>,\\n\\nPlease see below for the April ...</td>\n", "      <td><PERSON>,\\n\\nPlease see below for the April ...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-28 14:24:11.100</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>19008831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>24</td>\n", "      <td>Original 100</td>\n", "      <td>96</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>  40hrs  akron\\nJohn <PERSON>    40...</td>\n", "      <td><PERSON>  40hrs  akron\\nJohn <PERSON>    40...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-30 13:05:08.377</td>\n", "      <td><EMAIL></td>\n", "      <td>gerringand<PERSON>@gmail.com</td>\n", "      <td>14031804</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>35</td>\n", "      <td>7K emails</td>\n", "      <td>2399</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello...\\nMy hours for VUEA for the weeks of A...</td>\n", "      <td>Hello...\\nMy hours for VUEA for the weeks of A...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-28 04:14:55.483</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>A8316031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>36</td>\n", "      <td>7K emails</td>\n", "      <td>2400</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning!\\nMy hours for VUEA for the weeks...</td>\n", "      <td>Good morning!\\nMy hours for VUEA for the weeks...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-14 13:37:16.010</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>A8316031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>39</td>\n", "      <td>7K emails</td>\n", "      <td>2425</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE, ZERO</td>\n", "      <td>Revisit; potential knockout</td>\n", "      <td>Hello <PERSON><PERSON>,\\n\\nYour payroll has been process...</td>\n", "      <td>Payroll hours for the pay period 4/1/25 - 4/1...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-14 19:14:41.313</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>176167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>47</td>\n", "      <td>7K emails</td>\n", "      <td>2615</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>*Client ID - 18102126*\\n*Pay Period ending 03/...</td>\n", "      <td>*Client ID - 18102126*\\n*Pay Period ending 03/...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-01 12:17:09.660</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>18102126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>72</td>\n", "      <td>7K emails</td>\n", "      <td>4734</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>,\\n\\nI hope that you are doing well.\\n...</td>\n", "      <td><PERSON>,\\n\\nI hope that you are doing well.\\n...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-24 19:52:08.413</td>\n", "      <td>kl<PERSON><PERSON>@paychex.com</td>\n", "      <td><EMAIL></td>\n", "      <td>12046200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>30</td>\n", "      <td>7K emails</td>\n", "      <td>2344</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good Morning, Please run payroll \\nK<PERSON>in <PERSON>...</td>\n", "      <td>Good Morning, Please run payroll \\nK<PERSON>in <PERSON>...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-28 16:10:31.440</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>17153621</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>32</td>\n", "      <td>7K emails</td>\n", "      <td>2362</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Client # 0919-1708-8351\\n\\nHey Payroll Team,\\n...</td>\n", "      <td>Client # 0919-1708-8351\\n\\nHey Payroll Team,\\n...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-25 10:02:03.857</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>17088351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>33</td>\n", "      <td>7K emails</td>\n", "      <td>2387</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> worked 78 hours at $22...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> worked 78 hours at $22...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-09 03:14:11.507</td>\n", "      <td><EMAIL></td>\n", "      <td>cat<PERSON><PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>14090468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>34</td>\n", "      <td>7K emails</td>\n", "      <td>2388</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>HOURS_WITH_RATE</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello <PERSON>,\\n \\nYour payroll has been process...</td>\n", "      <td>Hello, <PERSON><PERSON>\\nNice to meet you.\\nCatherine Bor...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-23 01:41:14.260</td>\n", "      <td><EMAIL></td>\n", "      <td>cat<PERSON><PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>14090468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>40</td>\n", "      <td>7K emails</td>\n", "      <td>2459</td>\n", "      <td>Standard Processing</td>\n", "      <td>Standard Payroll</td>\n", "      <td>STANDARD_SALARY, REGULAR_HOURS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON> had 50 regular hours this pay period an...</td>\n", "      <td><PERSON> had 50 regular hours this pay period an...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-23 15:43:09.413</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>A8008898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>54</td>\n", "      <td>7K emails</td>\n", "      <td>2759</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>MANUAL_CHECKS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hi <PERSON><PERSON>\\n\\nPlease just send me stubs only an...</td>\n", "      <td>Hi <PERSON><PERSON>\\n\\nPlease just send me stubs only an...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-23 18:17:50.353</td>\n", "      <td><EMAIL></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON>@allstate.com</td>\n", "      <td>A8205638</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>55</td>\n", "      <td>7K emails</td>\n", "      <td>2763</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>MANUAL_CHECKS</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hi Tri<PERSON>\\n\\nI\"M going to make checks inhouse ...</td>\n", "      <td>Hi Tri<PERSON>\\n\\nI\"M going to make checks inhouse ...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-10 16:18:52.807</td>\n", "      <td><EMAIL></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON>@allstate.com</td>\n", "      <td>A8205638</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>106</td>\n", "      <td>7K emails</td>\n", "      <td>1888</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>MA<PERSON><PERSON>_CHECKS,START_OR_RESUME_BACK_DATED_PAY_P...</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello,\\nDid you want to backdate the check dat...</td>\n", "      <td>hi vanessa\\n \\ncan you help me enter a manual ...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-23 16:01:47.630</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>11067189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>107</td>\n", "      <td>7K emails</td>\n", "      <td>151</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>ADD_WORKER</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning,\\nPlease see attached forms for n...</td>\n", "      <td>Good morning,\\nPlease see attached forms for n...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-10 16:33:07.470</td>\n", "      <td>jlwin<PERSON><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>A8320498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>108</td>\n", "      <td>7K emails</td>\n", "      <td>1722</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>TERMINATE_WORKER</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good morning,\\nI will begin working on this fo...</td>\n", "      <td>Hello, \\n \\nPlease process <PERSON>'s te...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-30 15:38:56.390</td>\n", "      <td>j<PERSON><PERSON><PERSON>@paychex.com</td>\n", "      <td><EMAIL></td>\n", "      <td>ng028494</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>109</td>\n", "      <td>7K emails</td>\n", "      <td>1912</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>TERMINATE_WORKER</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Good afternoon,\\n\\nThis is for the Harmony Pro...</td>\n", "      <td>&gt; Good afternoon, <PERSON>!\\n&gt;\\n&gt; I have terminate...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-22 00:07:43.690</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>A8300959</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>110</td>\n", "      <td>7K emails</td>\n", "      <td>4719</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>UPDATE_EE_RATE_OF_PAY</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>,\\n \\nI reviewed it online and submit...</td>\n", "      <td><PERSON>,\\n \\nGood morning.\\nPlease run a pay...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-09 13:33:29.223</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>70198120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>111</td>\n", "      <td>7K emails</td>\n", "      <td>5989</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>TERMINATE_WORKER</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td><PERSON>,\\n\\nHere is the payroll for Signet Pro...</td>\n", "      <td><PERSON>,\\n\\nHere is the payroll for Signet Pro...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-29 13:13:50.067</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>11070618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>112</td>\n", "      <td>7K emails</td>\n", "      <td>2868</td>\n", "      <td>Knockout</td>\n", "      <td>Knockout</td>\n", "      <td>UPDATE_EE_RATE_OF_PAY</td>\n", "      <td>Confirmed test case with GT</td>\n", "      <td>Hello Kimico,\\nGood day!\\nYour payroll has bee...</td>\n", "      <td><PERSON><PERSON> - Regular salary\\nTheodore Foster...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-23 17:58:26.837</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>17077719</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    New GT ID  Email source  Email ID from source         Testing Case  \\\n", "0           1  Original 100                     5  Standard Processing   \n", "1           2  Original 100                     7  Standard Processing   \n", "2           3  Original 100                     7  Standard Processing   \n", "3           4  Original 100                     8  Standard Processing   \n", "4           6  Original 100                     9  Standard Processing   \n", "5           8  Original 100                    12  Standard Processing   \n", "6           9  Original 100                    28  Standard Processing   \n", "7          16  Original 100                    69  Standard Processing   \n", "8          19  Original 100                    79  Standard Processing   \n", "9          22  Original 100                    82  Standard Processing   \n", "10         24  Original 100                    96  Standard Processing   \n", "11         35     7K emails                  2399  Standard Processing   \n", "12         36     7K emails                  2400  Standard Processing   \n", "13         39     7K emails                  2425  Standard Processing   \n", "14         47     7K emails                  2615  Standard Processing   \n", "15         72     7K emails                  4734  Standard Processing   \n", "16         30     7K emails                  2344  Standard Processing   \n", "17         32     7K emails                  2362  Standard Processing   \n", "18         33     7K emails                  2387  Standard Processing   \n", "19         34     7K emails                  2388  Standard Processing   \n", "20         40     7K emails                  2459  Standard Processing   \n", "21         54     7K emails                  2759             Knockout   \n", "22         55     7K emails                  2763             Knockout   \n", "23        106     7K emails                  1888             Knockout   \n", "24        107     7K emails                   151             Knockout   \n", "25        108     7K emails                  1722             Knockout   \n", "26        109     7K emails                  1912             Knockout   \n", "27        110     7K emails                  4719             Knockout   \n", "28        111     7K emails                  5989             Knockout   \n", "29        112     7K emails                  2868             Knockout   \n", "\n", "    Testing category                                      Cases covered  \\\n", "0   Standard Payroll                              REGULAR_HOURS, AMOUNT   \n", "1   Standard Payroll                                    HOURS_WITH_RATE   \n", "2   Standard Payroll                                    HOURS_WITH_RATE   \n", "3   Standard Payroll                                             AMOUNT   \n", "4   Standard Payroll                     STANDARD_SALARY, REGULAR_HOURS   \n", "5   Standard Payroll                        AMOUNT, ZERO, REGULAR_HOURS   \n", "6   Standard Payroll                                      REGULAR_HOURS   \n", "7   Standard Payroll                                    STANDARD_SALARY   \n", "8   Standard Payroll                                             AMOUNT   \n", "9   Standard Payroll                                             AMOUNT   \n", "10  Standard Payroll                                      REGULAR_HOURS   \n", "11  Standard Payroll                                      REGULAR_HOURS   \n", "12  Standard Payroll                                      REGULAR_HOURS   \n", "13  Standard Payroll                              HOURS_WITH_RATE, ZERO   \n", "14  Standard Payroll                                      REGULAR_HOURS   \n", "15  Standard Payroll                                    HOURS_WITH_RATE   \n", "16  Standard Payroll                                    HOURS_WITH_RATE   \n", "17  Standard Payroll                                      REGULAR_HOURS   \n", "18  Standard Payroll                                    HOURS_WITH_RATE   \n", "19  Standard Payroll                                    HOURS_WITH_RATE   \n", "20  Standard Payroll                     STANDARD_SALARY, REGULAR_HOURS   \n", "21          Knockout                                      MANUAL_CHECKS   \n", "22          Knockout                                      MANUAL_CHECKS   \n", "23          Knockout  MANUAL_CHECKS,START_OR_RESUME_BACK_DATED_PAY_P...   \n", "24          Knockout                                         ADD_WORKER   \n", "25          Knockout                                   TERMINATE_WORKER   \n", "26          Knockout                                   TERMINATE_WORKER   \n", "27          Knockout                              UPDATE_EE_RATE_OF_PAY   \n", "28          Knockout                                   TERMINATE_WORKER   \n", "29          Knockout                              UPDATE_EE_RATE_OF_PAY   \n", "\n", "                         Status  \\\n", "0   Confirmed test case with GT   \n", "1   Confirmed test case with GT   \n", "2   Confirmed test case with GT   \n", "3   Confirmed test case with GT   \n", "4   Confirmed test case with GT   \n", "5   Confirmed test case with GT   \n", "6   Confirmed test case with GT   \n", "7   Confirmed test case with GT   \n", "8   Confirmed test case with GT   \n", "9   Confirmed test case with GT   \n", "10  Confirmed test case with GT   \n", "11  Confirmed test case with GT   \n", "12  Confirmed test case with GT   \n", "13  Revisit; potential knockout   \n", "14  Confirmed test case with GT   \n", "15  Confirmed test case with GT   \n", "16  Confirmed test case with GT   \n", "17  Confirmed test case with GT   \n", "18  Confirmed test case with GT   \n", "19  Confirmed test case with GT   \n", "20  Confirmed test case with GT   \n", "21  Confirmed test case with GT   \n", "22  Confirmed test case with GT   \n", "23  Confirmed test case with GT   \n", "24  Confirmed test case with GT   \n", "25  Confirmed test case with GT   \n", "26  Confirmed test case with GT   \n", "27  Confirmed test case with GT   \n", "28  Confirmed test case with GT   \n", "29  Confirmed test case with GT   \n", "\n", "                                           Full email  \\\n", "0   Good morning:\\n\\nPlease make and process all p...   \n", "1   Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "2   Thank you payroll w/e 4/15, checks dated 4/18\\...   \n", "3   Please pay clayton <PERSON> 1000 check date 4/2...   \n", "4   Hello,\\n\\nPlease see payroll below from last w...   \n", "5   Nelson Overstreet $3200.00\\nShari Overstreet $...   \n", "6   <PERSON> Dinora had 32 hours. <PERSON>310.614...   \n", "7   Stacy Group, Inc.\\n0064-9813\\nPaycheck Date - ...   \n", "8   <PERSON> <PERSON>,\\nEmpire Energy Specialists Inc pa...   \n", "9   <PERSON> <PERSON>,\\n\\nPlease see below for the April ...   \n", "10  <PERSON>  40hrs  akron\\nJ<PERSON><PERSON>    40...   \n", "11  Hello...\\nMy hours for VUEA for the weeks of A...   \n", "12  Good morning!\\nMy hours for VUEA for the weeks...   \n", "13  Hello <PERSON><PERSON>,\\n\\nYour payroll has been process...   \n", "14  *Client ID - 18102126*\\n*Pay Period ending 03/...   \n", "15  Hi <PERSON><PERSON>,\\n\\nI hope that you are doing well.\\n...   \n", "16  Good Morning, Please run payroll \\nK<PERSON><PERSON>...   \n", "17  Client # 0919-1708-8351\\n\\nHey Payroll Team,\\n...   \n", "18  <PERSON><PERSON><PERSON><PERSON> worked 78 hours at $22...   \n", "19  Hello <PERSON>,\\n \\nYour payroll has been process...   \n", "20  <PERSON> had 50 regular hours this pay period an...   \n", "21  Hi Tricia\\n\\nPlease just send me stubs only an...   \n", "22  Hi Tricia\\n\\nI\"M going to make checks inhouse ...   \n", "23  Hello,\\nDid you want to backdate the check dat...   \n", "24  Good morning,\\nPlease see attached forms for n...   \n", "25  Good morning,\\nI will begin working on this fo...   \n", "26  Good afternoon,\\n\\nThis is for the Harmony Pro...   \n", "27  <PERSON>,\\n \\nI reviewed it online and submit...   \n", "28  Hi <PERSON>,\\n\\nHere is the payroll for Signet Pro...   \n", "29  Hello Kimico,\\nGood day!\\nYour payroll has bee...   \n", "\n", "                                         Email to use  Email subject  \\\n", "0   Good morning:\\n\\nPlease make and process all p...            NaN   \n", "1   Thank you payroll w/e 4/15, checks dated 4/18\\...            NaN   \n", "2   Thank you payroll w/e 4/15, checks dated 4/18\\...            NaN   \n", "3   Please pay clayton <PERSON> 1000 check date 4/2...            NaN   \n", "4   Hello,\\n\\nPlease see payroll below from last w...            NaN   \n", "5   Nelson Overstreet $3200.00\\nShari Overstreet $...            NaN   \n", "6             <PERSON> Dinora had 32 hours. <PERSON>   \n", "7   Stacy Group, Inc.\\n0064-9813\\nPaycheck Date - ...            NaN   \n", "8   <PERSON> <PERSON>,\\nEmpire Energy Specialists Inc pa...            NaN   \n", "9   <PERSON> <PERSON>,\\n\\nPlease see below for the April ...            NaN   \n", "10  <PERSON>  40hrs  akron\\nJ<PERSON><PERSON>    40...            NaN   \n", "11  Hello...\\nMy hours for VUEA for the weeks of A...            NaN   \n", "12  Good morning!\\nMy hours for VUEA for the weeks...            NaN   \n", "13   Payroll hours for the pay period 4/1/25 - 4/1...            NaN   \n", "14  *Client ID - 18102126*\\n*Pay Period ending 03/...            NaN   \n", "15  Hi <PERSON><PERSON>,\\n\\nI hope that you are doing well.\\n...            NaN   \n", "16  Good Morning, Please run payroll \\nK<PERSON><PERSON>...            NaN   \n", "17  Client # 0919-1708-8351\\n\\nHey Payroll Team,\\n...            NaN   \n", "18  <PERSON><PERSON><PERSON><PERSON> worked 78 hours at $22...            NaN   \n", "19  Hello, <PERSON><PERSON>\\nNice to meet you.\\nCatherine Bor...            NaN   \n", "20  Olivia had 50 regular hours this pay period an...            NaN   \n", "21  Hi Tricia\\n\\nPlease just send me stubs only an...            NaN   \n", "22  Hi Tricia\\n\\nI\"M going to make checks inhouse ...            NaN   \n", "23  hi vanessa\\n \\ncan you help me enter a manual ...            NaN   \n", "24  Good morning,\\nPlease see attached forms for n...            NaN   \n", "25  Hello, \\n \\nPlease process <PERSON>'s te...            NaN   \n", "26  > Good afternoon, <PERSON>!\\n>\\n> I have terminate...            NaN   \n", "27  Hi <PERSON>,\\n \\nGood morning.\\nPlease run a pay...            NaN   \n", "28  Hi <PERSON>,\\n\\nHere is the payroll for Signet Pro...            NaN   \n", "29  <PERSON><PERSON> Myers - Regular salary\\nTheodore <PERSON>...            NaN   \n", "\n", "                 TImeStamp                       To_email  \\\n", "0  2025-04-15 17:51:06.753          <EMAIL>   \n", "1  2025-04-16 14:14:34.363            <EMAIL>   \n", "2  2025-04-16 14:14:34.363            <EMAIL>   \n", "3  2025-04-21 11:43:09.923         <EMAIL>   \n", "4  2025-04-21 17:47:02.330           <EMAIL>   \n", "5  2025-04-22 16:01:03.827         <EMAIL>   \n", "6  2025-04-09 17:00:20.227           <EMAIL>   \n", "7  2025-04-24 13:37:45.667         <EMAIL>   \n", "8  2025-04-28 12:15:03.630            <EMAIL>   \n", "9  2025-04-28 14:24:11.100           <EMAIL>   \n", "10 2025-04-30 13:05:08.377         <EMAIL>   \n", "11 2025-04-28 04:14:55.483          <EMAIL>   \n", "12 2025-04-14 13:37:16.010          <EMAIL>   \n", "13 2025-04-14 19:14:41.313        <EMAIL>   \n", "14 2025-04-01 12:17:09.660             <EMAIL>   \n", "15 2025-04-24 19:52:08.413         <EMAIL>   \n", "16 2025-04-28 16:10:31.440           <EMAIL>   \n", "17 2025-04-25 10:02:03.857        <EMAIL>   \n", "18 2025-04-09 03:14:11.507          <EMAIL>   \n", "19 2025-04-23 01:41:14.260     <EMAIL>   \n", "20 2025-04-23 15:43:09.413       <EMAIL>   \n", "21 2025-04-23 18:17:50.353           <EMAIL>   \n", "22 2025-04-10 16:18:52.807           <EMAIL>   \n", "23 2025-04-23 16:01:47.630             <EMAIL>   \n", "24 2025-04-10 16:33:07.470         <EMAIL>   \n", "25 2025-04-30 15:38:56.390         <EMAIL>   \n", "26 2025-04-22 00:07:43.690     <EMAIL>   \n", "27 2025-04-09 13:33:29.223  <EMAIL>   \n", "28 2025-04-29 13:13:50.067           <EMAIL>   \n", "29 2025-04-23 17:58:26.837     <EMAIL>   \n", "\n", "                      From_email Customer ID  \n", "0   <EMAIL>    11111181  \n", "1          <EMAIL>    70126790  \n", "2          <EMAIL>    70126790  \n", "3         <EMAIL>    18143909  \n", "4             <EMAIL>    Y6463960  \n", "5           <EMAIL>    18163405  \n", "6         <EMAIL>    VTA10793  \n", "7       <EMAIL>      649813  \n", "8                <EMAIL>    16044551  \n", "9            <EMAIL>    19008831  \n", "10      <EMAIL>    14031804  \n", "11           <EMAIL>    A8316031  \n", "12           <EMAIL>    A8316031  \n", "13     <EMAIL>      176167  \n", "14  <EMAIL>    18102126  \n", "15       <EMAIL>    12046200  \n", "16   <EMAIL>    17153621  \n", "17        <EMAIL>    17088351  \n", "18      cathy<PERSON><PERSON><PERSON>@gmail.com    14090468  \n", "19      cathyin<PERSON><PERSON>@gmail.com    14090468  \n", "20            <EMAIL>    A8008898  \n", "21        U<PERSON><PERSON><PERSON><PERSON>@allstate.com    A8205638  \n", "22        UBer<PERSON><EMAIL>    A8205638  \n", "23              <EMAIL>    11067189  \n", "24          <EMAIL>    A8320498  \n", "25           <EMAIL>    ng028494  \n", "26   <EMAIL>    A8300959  \n", "27      <EMAIL>    70198120  \n", "28            <EMAIL>    11070618  \n", "29        <EMAIL>    17077719  "]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "4922a860de36a9a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}