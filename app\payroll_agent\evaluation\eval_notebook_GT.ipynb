{"cells": [{"cell_type": "markdown", "id": "f83893f1", "metadata": {}, "source": ["### Ground Truth Evaluation Notebook\n", "\n", "#### Overview\n", "This notebook executes ground truth test cases against the payroll email processing API endpoint and collects results for evaluation and analysis.\n", "\n", "#### Purpose\n", "- Load formatted JSON test cases from the ground truth dataset\n", "- Execute test cases against the `/process-email` API endpoint\n", "- Collect and save processing results for each test case\n", "- Identify failed cases for further analysis\n", "- Generate timestamped result files for comparison and tracking\n", "\n", "#### Prerequisites\n", "- Payroll agent API server running on `http://localhost:8000`\n", "- Formatted JSON test cases from `format_GT_test_cases.ipynb`\n", "- API endpoint `/api/v1/process-email` accessible and functional\n", "\n", "#### Input\n", "- **File**: `{date}_Payroll_processing_golden_set.json` (formatted test cases)\n", "- **Location**: `app/payroll_agent/evaluation/ground_truth/`\n", "- **Format**: JSON array of test cases with proper API structure\n", "\n", "#### Output\n", "- **File**: `{timestamp}_Payroll_processing_golden_set_results.json`\n", "- **Location**: `app/payroll_agent/evaluation/ground_truth/`\n", "- **Structure**: Array of objects containing:\n", "  - `New GT ID`: Test case identifier\n", "  - `input`: Original test case data\n", "  - `output`: API response from payroll agent\n", "\n", "#### Configuration Variables\n", "Update these variables before running:\n", "\n", "```python\n", "timestamp = time.strftime('%Y%m%d_%H%M%S')\n", "base_path = r'C:\\Users\\<USER>\\payroll-email-agent\\app\\payroll_agent\\evaluation\\ground_truth'  # Update to your path\n", "input_file = r'\\{date}_Payroll_processing_golden_set.json'  # Update with your date\n", "output_file = rf'\\{timestamp}_Payroll_processing_golden_set_results.json'\n", "```\n", "\n", "```python\n", "# API Configuration\n", "HOST = \"http://localhost:8000\"\n", "API_PREFIX = \"/api/v1\"\n", "ROUTE = \"/process-email\"\n", "```\n", "\n", "#### Usage Instructions\n", "- Ensure the payroll agent API is running and accessible\n", "- Update the configuration variables with correct file paths and dates\n", "- Run all cells in sequence\n", "- Monitor progress via the progress bar\n", "- Check the failed variable for any unsuccessful cases\n", "- Results are automatically saved with timestamp"]}, {"cell_type": "code", "execution_count": 1, "id": "96a16e68-e52f-4ace-b8ff-8e7e8f4353ca", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T17:31:07.650592Z", "start_time": "2025-07-31T17:31:06.797240Z"}}, "outputs": [], "source": ["import os\n", "import re\n", "import time\n", "import json\n", "import requests\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "\n", "import email\n", "\n", "from tqdm import tqdm\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a5cbb3a2", "metadata": {}, "outputs": [], "source": ["timestamp = time.strftime('%Y%m%d_%H%M%S')\n", "base_path = r'C:\\Users\\<USER>\\payroll-email-agent\\app\\payroll_agent\\evaluation\\ground_truth'\n", "input_file = r'\\250911_Payroll_processing_golden_set.json'\n", "output_file = rf'\\{timestamp}_Payroll_processing_golden_set_results.json'"]}, {"cell_type": "code", "execution_count": 5, "id": "4adbbecf", "metadata": {}, "outputs": [], "source": ["# Config\n", "HOST = \"http://localhost:8000\"\n", "API_PREFIX = \"/api/v1\"\n", "ROUTE = \"/process-email\"\n", "ENDPOINT_URL = f\"{HOST}{API_PREFIX}{ROUTE}\""]}, {"cell_type": "markdown", "id": "6f43ecb8-693f-4195-a2a0-2b05f258987e", "metadata": {}, "source": ["## Functions"]}, {"cell_type": "code", "execution_count": 6, "id": "e9fbbe52-1394-493a-b378-bd6b2f5c685b", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T17:31:08.581818Z", "start_time": "2025-07-31T17:31:08.571498Z"}}, "outputs": [], "source": ["def call_api(case, max_retries=5, delay=3):\n", "    last_exception = None\n", "    for attempt in range(max_retries):\n", "        try:\n", "            response = requests.post(ENDPOINT_URL, json=case, timeout=500)\n", "            response.raise_for_status()\n", "            return response.json().get(\"response\", \"{}\")\n", "        except Exception as e:\n", "            last_exception = e\n", "            if attempt < max_retries - 1:\n", "                print(f\"Retry attempt: {attempt}\")\n", "                time.sleep(delay)\n", "    return {\"error\": f\"API call failed after {max_retries} attempts: {str(last_exception)}\"}\n", "\n", "\n", "def load_test_cases(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "def save_json(path, data):\n", "    with open(path, 'w', encoding='utf-8') as f:\n", "      json.dump(data, f, indent=2, ensure_ascii=False)\n", "\n", "# Parallelize API calls using ThreadPoolExecutor\n", "def run_evaluation(test_cases, max_workers=10):\n", "    results= list()\n", "    with ThreadPoolExecutor(max_workers=max_workers) as executor:\n", "        future_to_case = {executor.submit(call_api, case): case for case in test_cases}\n", "        for future in tqdm(as_completed(future_to_case), total=len(test_cases), desc=\"Evaluating\"):\n", "            case = future_to_case[future]\n", "            try:\n", "                result = future.result()\n", "            except Exception as e:\n", "                result = {\"error\": str(e)}\n", "            results.append({\n", "                'New GT ID': case.get(\"comment\").get(\"uid\"),\n", "                \"input\": case,\n", "                \"output\": result\n", "            })\n", "    return results\n", "\n", "# Remove illegal Excel characters\n", "_illegal_chars_re = re.compile(r\"[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]\")\n", "\n", "def remove_illegal_chars(val):\n", "    if isinstance(val, str):\n", "        return _illegal_chars_re.sub(\"\", val)\n", "    return val\n", "\n", "import ast\n", "\n", "def ensure_displayid_list(case, key=\"displayId\"):\n", "    v = case.get(key)\n", "    if v is None:\n", "        case[key] = []\n", "        return case\n", "    if isinstance(v, list):\n", "        return case\n", "    if isinstance(v, str):\n", "        s = v.strip()\n", "        # Try literal_eval first (handles \"['a','b']\" and \"['11111181']\")\n", "        try:\n", "            parsed = ast.literal_eval(s)\n", "            if isinstance(parsed, list):\n", "                case[key] = parsed\n", "            else:\n", "                case[key] = [parsed]\n", "            return case\n", "        except Exception:\n", "            # Fallback: split on commas\n", "            case[key] = [part.strip() for part in s.split(\",\") if part.strip()]\n", "            return case\n", "    # any other type -> wrap in list\n", "    case[key] = [v]\n", "    return case\n"]}, {"cell_type": "markdown", "id": "7e219391-2cca-4c49-9e3d-0878a5b17fbe", "metadata": {}, "source": ["### Run Cases through payroll engine"]}, {"cell_type": "code", "execution_count": null, "id": "ed8f2248", "metadata": {}, "outputs": [], "source": ["# Load in cases\n", "cases = load_test_cases(base_path + input_file)"]}, {"cell_type": "code", "execution_count": null, "id": "c79d2b08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'> ['11111181']\n"]}], "source": ["# Ensure displayId is always a list\n", "cases = [ensure_displayid_list(c) for c in cases]\n", "# verify\n", "print(type(cases[0][\"displayId\"]), cases[0][\"displayId\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f8ceb065", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["178\n"]}, {"data": {"text/plain": ["{'displayId': ['11111181'],\n", " 'comment': {'uid': '1',\n", "  'id': '1',\n", "  'timestamp': '2025-04-15T17:51:06.753000',\n", "  'user_properties': {'string:Sender': '<EMAIL>',\n", "   'string:Sender Domain': '<EMAIL>'},\n", "  'messages': [{'from': '<EMAIL>',\n", "    'to': ['<EMAIL>'],\n", "    'body': {'text': 'Good morning:\\n\\nPlease make and process all payroll for\\n\\nFriday 4-18-2025\\n\\nWeeks Ending 4-4-25 & 4-11-25\\n\\n Aly Wickham = 80 hours\\n\\n Sharva Carter = 70 hours\\n\\n To<PERSON>on = 30 hours\\n\\n<PERSON><PERSON><PERSON> = salary $2600 per pay period\\nAs always, I thank you for your assistance,\\n\\n\\nTodd Rutledge, Esq.\\n[cid:image001.png@01DBAE0D.381D8C30]<https://urldefense.com/v3/__https://accidentinjury.net/__;!!PIfy-9xbww!CI78rR4pHaeF1i2TO6I4HNMc0dnIynV2k_OH1dRMx41Q8FGS7XIPF1H77x2T-ecEMuUB_q0BrK27dnWFaO-Yem-CRlmGG0I$ >\\nRut<PERSON> & Jarrett, PLLC\\n26400 Lahser Road, Suite 125\\nSouthfield, MI 48033\\n************** / (************* (fax)\\n\\nFrom: <PERSON>\\nSent: Thursday, April 3, 2025 2:01 PM\\nTo: <EMAIL>; <EMAIL>\\nCc: <EMAIL>\\nSubject: RE: Client 11111181 - Rutledge & Jarrett PLLC - Payroll for Friday April 4, 2025\\n\\n\\nGood morning:\\n\\nPlease make and process all payroll for\\n\\nFriday 4-4-2025\\n\\nWeeks Ending 3-21-25 & 3-28-25\\n\\n Aly Wickham = 80 hours\\n\\n Sharva Carter = 80 hours\\n\\n Tonja Culberson = 30 hours\\n\\nMichael Sullivan = salary $2600 per pay period\\n\\n  As always, I thank you for your assistance,\\n\\nTodd Rutledge, Esq.\\n[cid:image001.png@01DBAE0D.381D8C30]<https://urldefense.com/v3/__https://accidentinjury.net/__;!!PIfy-9xbww!CI78rR4pHaeF1i2TO6I4HNMc0dnIynV2k_OH1dRMx41Q8FGS7XIPF1H77x2T-ecEMuUB_q0BrK27dnWFaO-Yem-CRlmGG0I$ >\\nRutledge & Jarrett, PLLC\\n26400 Lahser Road, Suite 125\\nSouthfield, MI 48033\\n************** / (************* (fax)'},\n", "    'subject': {'text': 'Subject'}}]}}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Print one example\n", "print(len(cases))\n", "cases[0]"]}, {"cell_type": "code", "execution_count": 12, "id": "30762439-1910-44f4-a7a1-e73c7b00cf42", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T18:16:06.839413Z", "start_time": "2025-07-31T18:01:31.482665Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:   0%|          | 0/178 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 1\n", "Retry attempt: 2\n", "Retry attempt: 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:   1%|          | 2/178 [00:26<40:21, 13.76s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n", "Retry attempt: 1\n", "Retry attempt: 2\n", "Retry attempt: 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:   2%|▏         | 3/178 [00:38<37:48, 12.96s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n", "Retry attempt: 1\n", "Retry attempt: 2\n", "Retry attempt: 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:  54%|█████▍    | 96/178 [17:28<15:25, 11.29s/it]  "]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:  58%|█████▊    | 103/178 [19:46<23:48, 19.04s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating:  69%|██████▊   | 122/178 [23:10<05:23,  5.78s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Retry attempt: 2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|██████████| 178/178 [27:18<00:00,  9.21s/it]\n"]}], "source": ["# run avaluation\n", "results = run_evaluation(cases)"]}, {"cell_type": "code", "execution_count": 13, "id": "21129602", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'New GT ID': '10',\n", " 'input': {'displayId': ['07389140'],\n", "  'comment': {'uid': '10',\n", "   'id': '10',\n", "   'timestamp': '2025-04-10T14:54:50.280000',\n", "   'user_properties': {'string:Sender': nan, 'string:Sender Domain': nan},\n", "   'messages': [{'from': nan,\n", "     'to': [nan],\n", "     'body': {'text': '<PERSON> <PERSON>,\\n\\nPlease see payroll below for April 15th:\\nPlease let me know if you have any questions!\\n\\n   - <PERSON>aried, Plus a bonus of $1,575.10\\n   - Rayna Tribo Salaried, Plus a bonus of  $457.06\\n   - <PERSON>\\n   - <PERSON><PERSON>-  26  Regular Hours\\n   - <PERSON>  89 Regular Hours\\n   - <PERSON>-<PERSON>aried, Plus a bonus of $775.10\\n\\nKind regards,'},\n", "     'subject': {'text': 'Subject'}}]}},\n", " 'output': {'error': 'API call failed after 5 attempts: Out of range float values are not JSON compliant'}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Print 1 example result\n", "results[0]"]}, {"cell_type": "code", "execution_count": 15, "id": "ded274fc-a4ee-4905-9bec-dde0828105bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check how many failed\n", "failed = {i:case[\"input\"] for i, case in enumerate(results) if \"error\" in case[\"output\"]}\n", "len(failed)"]}, {"cell_type": "code", "execution_count": 18, "id": "ad41da45", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: {'displayId': ['07389140'],\n", "  'comment': {'uid': '10',\n", "   'id': '10',\n", "   'timestamp': '2025-04-10T14:54:50.280000',\n", "   'user_properties': {'string:Sender': nan, 'string:Sender Domain': nan},\n", "   'messages': [{'from': nan,\n", "     'to': [nan],\n", "     'body': {'text': '<PERSON> <PERSON>,\\n\\nPlease see payroll below for April 15th:\\nPlease let me know if you have any questions!\\n\\n   - <PERSON>aried, Plus a bonus of $1,575.10\\n   - Rayna Tribo Salaried, Plus a bonus of  $457.06\\n   - <PERSON>\\n   - <PERSON><PERSON>-  26  Regular Hours\\n   - <PERSON>  89 Regular Hours\\n   - <PERSON>-<PERSON>aried, Plus a bonus of $775.10\\n\\nKind regards,'},\n", "     'subject': {'text': 'Subject'}}]}},\n", " 2: {'displayId': ['19008831'],\n", "  'comment': {'uid': '21',\n", "   'id': '21',\n", "   'timestamp': '2025-04-28T14:24:11.100000',\n", "   'user_properties': {'string:Sender': nan, 'string:Sender Domain': nan},\n", "   'messages': [{'from': nan,\n", "     'to': ['<EMAIL>'],\n", "     'body': {'text': \"<PERSON> <PERSON>,\\n\\nPlease see below for the April 30, 2025 Payroll for the Marlboro Township\\nEducation Association #19008831. I am requesting to see a PDF journal\\nreview as I do NOT have the ability for the encrypted version:) Please\\nreach out with any questions or concerns.\\n\\nThank you!\\nFara Bono\\n732-610-5662\\n\\nName: New Jersey Education Association\\nPay Period: 04/01/2025- 04/3102025 Check Date: 04/30/2025\\nCompany ID Worker ID Employee Name Pay Component Hours Rate Amount\\n19008831 58 Angela Fiorello Salary $400.00\\n19008831 17 Katherine Brizzi Salary $525.00\\n19008831 12 Donna Bruno Salary $625.00\\n19008831 4 Lisa Llano Salary $625.00\\n19008831 24 Fara Bono Salary $708.34\\n19008831 9 <PERSON><PERSON> Fitzsimmons Salary $825.00\\n\\n\\n*           Fara Bono*\\n\\nOn <PERSON>e, Apr 15, 2025 at 4:33\\u202f<PERSON>, <PERSON> <<EMAIL>>\\nwrote:\\n\\n> Good Afternoon,\\n> I’m writing to introduce myself as your new point of contact with Paychex.\\n> I can be reached via phone call at ************* x5151794 *or by email at *<EMAIL>\\n> <<EMAIL>>*\\n> Please don’t hesitate to contact me if you have questions. If I’m\\n> unavailable, leave me a message or send me an email. I'll reach out to\\n> schedule a time for us to talk.\\n> I also would like to add that I have been receiving emails sent to your\\n> previous specialist and will be reaching out about any outstanding issues\\n> you may have to find a resolution.\\n> Thank you for your partnership,\\n>\\n> Rich Clarke\\n> Assigned Specialist - SMB Service\\n> P: ************ x5151794\\n> E: <EMAIL>\\n>\\n> Your experience and satisfaction matter to us. Let my manager know how I'm\\n> doing.\\n>\\n> Matt Page | P: ************ x5152120 | E: <EMAIL>\\n>\\n>\\n>\\n> ------------------------------\\n> The information contained in this message may be privileged, confidential\\n> and protected from disclosure. If the reader of this message is not the\\n> intended recipient, or an employee or agent responsible for delivering this\\n> message to the intended recipient, you are hereby notified that any\\n> dissemination, distribution or copying of this communication is strictly\\n> prohibited. If you have received this communication in error, please notify\\n> your representative immediately and delete this message from your computer.\\n> Thank you.\\n>\"},\n", "     'subject': {'text': 'Subject'}}]}},\n", " 3: {'displayId': ['14052843'],\n", "  'comment': {'uid': '23',\n", "   'id': '23',\n", "   'timestamp': '2025-04-08T15:57:36.860000',\n", "   'user_properties': {'string:Sender': nan, 'string:Sender Domain': nan},\n", "   'messages': [{'from': nan,\n", "     'to': [nan],\n", "     'body': {'text': 'Hi\\xa0\\xa0Joshua,\\xa0\\xa0\\xa0\\xa0Here is the payroll for\\xa0 5-2Client ID\\xa0 1405-2843\\xa0\\xa0Name\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 Hours\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0 \\xa0\\xa0 \\xa0\\xa0\\xa0Tips\\xa0De Jthanakon\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 9\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0182\\xa0\\xa0\\xa0Htoo Wah Say\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 5\\xa0\\xa0Jiraporn kneereerattrakul\\xa0\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa014\\xa0 \\xa0 \\xa0 \\xa0 \\xa0\\xa0\\xa0\\xa0Sandra Jumbo\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa015\\xa0Wilasinee Datsiripayak\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 45\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0551\\xa0\\xa0Stephen Luke\\xa0 Marcelo Baluyut\\xa0 \\xa0 \\xa0 \\xa0 \\xa05\\nAnthony\\xa0 Ko\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 0\\nMue Tha Gaw Pleh\\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa0 \\xa05\\xa0\\xa0\\n\\nNotes:\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0Please call if you have any questions.\\xa0\\xa0 Please let me know when this has been completed.\\n\\nThank you\\nBest Regards,\\n\\n\\nGary Rice\\nSweet Basil Thai Restaurant\\n315-878-6124'},\n", "     'subject': {'text': 'Subject'}}]}}}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["failed"]}, {"cell_type": "markdown", "id": "2e6e4f88-0282-4344-86b0-7945232d295a", "metadata": {}, "source": ["### Save results"]}, {"cell_type": "code", "execution_count": 16, "id": "b756a457-a10a-4238-9898-7124c0b23ce0", "metadata": {"ExecuteTime": {"end_time": "2025-07-31T18:16:58.600941Z", "start_time": "2025-07-31T18:16:58.586764Z"}}, "outputs": [], "source": ["save_json(base_path + output_file, results)"]}, {"cell_type": "code", "execution_count": 17, "id": "4e0e9421", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C:\\Users\\<USER>\\payroll-email-agent\\app\\payroll_agent\\evaluation\\ground_truth\\20250910_135730_Payroll_processing_golden_set_results.json\n"]}], "source": ["print(base_path + output_file)"]}, {"cell_type": "code", "execution_count": null, "id": "e0de2a3f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}