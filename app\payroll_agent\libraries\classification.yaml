# Classification Models Configuration for Triage and Tagging

intent_triage:
  description: "Initial triage to determine if an email is relevant, actionable, and payroll-related"
  fields:
    isWorkRelated:
      type: "int"
      required: true
      description: "Confidence level (0-100) that the email is work-related (not spam, promo, or unrelated)"
    isWorkRelated_reasoning:
      type: "str"
      required: true
      description: "Brief explanation for the isWorkRelated confidence score"
    RequiresWork:
      type: "int"
      required: true
      description: "Confidence level (0-100) that the email requires Paychex employee action"
    RequiresWork_reasoning:
      type: "str"
      required: true
      description: "Brief explanation for the RequiresWork confidence score"
    isAboutPayroll:
      type: "int"
      required: true
      description: |
        Likelihood that the email involves entering payroll information, updating payroll information, or a request to process (can also be referred to as run, 
        release) a payroll — including approvals, edits, follow-ups. Customer's asks will sometimes be terse and intent can be inferred from the previous e-mail 
        in the thread (E.g., if a Paychex employee e-mailed this customer for payroll for this period). Also, sometimes the payroll information may be part of 
        the attachment the customer is referencing
    isAboutPayroll_reasoning:
      type: "str"
      required: true
      description: "Brief explanation for the isAboutPayroll confidence score"
    # isOnlyAboutPayroll:
    #   type: "int"
    #   required: true
    #   description: | 
    #    Likelihood that the email contains a single, clear request related to payroll entry, or processing with no other topics, questions,
    #    or additional tasks included. Sometimes payroll entries will have pay amounts (e.g., salary, hours, bonus, tips, etc.) and sometimes 
    #    they’ll also just be a request to process the usual or normal amount or similar to last pay period. Customer's asks will sometimes
    #    be terse and intent can be infersred from the previous e-mail in the thread (E.g., if a Paychex employee e-mailed this customer for 
    #    payroll for this period) Also, sometimes the payroll information may be part of the attachment the customer is referencing
    # isOnlyAboutPayroll_reasoning:
    #   type: "str"
    #   required: true
    #   description: "Brief explanation for the isOnlyAboutPayroll confidence score"
    EnterPayroll:
      type: "int"
      required: true
      description: "Confidence level (0-100) that the customer is providing pay details for payroll entry. Details may be as simple as an employee name and a request to process as normal."
    EnterPayroll_reasoning:
      type: "str"
      required: true
      description: "Brief explanation for the EnterPayroll confidence score"
    # isStandaloneRequest:
    #   type: "int"
    #   required: true
    #   description: "Confidence level (0-100) that this email is a fresh/standalone request suitable for automation. HIGH score = safe to automate (fresh requests, weekly payroll threads, replies to reminders), LOW score = ongoing agent conversations."
    # isStandaloneRequest_reasoning:
    #   type: "str"
    #   required: true
    #   description: "Brief explanation for the isStandaloneRequest confidence score"

request_type_tags:
  description: "Tags to categorize the type and characteristics of payroll requests"
  fields:
    RequestType:
      type: "literal"
      required: true
      options: ["same_as_prior_period", "standard_rates", "custom", "mix", "unclear"]
      description: |
        What the customer is requesting to be filled out:
        - same_as_prior_period: Use the same data as last time
        - standard_rates: Use standard/default rates
        - custom: Custom rates or amounts provided
        - mix: A combination of standard and custom
        - unclear: Cannot determine from email
    
    StandaloneRequest:
      type: "literal"
      required: true
      options: ["standalone", "follow_up", "unknown"]
      description: |
        Whether the email is a standalone payroll request or part of a thread:
        - standalone: New request with no thread
        - follow_up: Continuation of a prior conversation
        - unknown: Cannot determine
    
    PayrollFollowUp:
      type: "literal"
      required: true
      options: ["yes", "no", "unknown"]
      description: "Does the email follow up on a prior payroll request?"
    
    NonPayrollFollowUp:
      type: "literal"
      required: true
      options: ["yes", "no", "unknown"]
      description: "Does the email include a non-payroll-related follow-up?"
    
    EmployeeCount:
      type: "int"
      required: true
      description: "Number of employees mentioned in the request."
    
    EmailIsFromEmployee:
      type: "literal"
      required: true
      options: ["yes", "no", "unknown"]
      description: "Whether the sender refers to themselves as the employee being paid."
    
    NumberOfPayPeriods:
      type: "int"
      required: true
      description: "Number of pay periods referenced."
    
    CallRequested:
      type: "literal"
      required: true
      options: ["no", "yes", "unknown"]
      description: |
        Whether the customer asked to be called:
        - no: No call requested
        - yes: Customer explicitly asked to be called
        - unknown: Cannot determine if a call was requested
    
    PayHoursOnly:
      type: "int"
      required: true
      description: "Number of employees paid based on hours only (e.g., '40 hours')."
    
    PayHoursWithPayRate:
      type: "int"
      required: true
      description: "Number of employees paid based on hours and an explicit rate (e.g., '40 hours at $15/hour')."
    
    PayHoursWithPayRateId:
      type: "int"
      required: true
      description: "Number of employees paid based on hours using a rate ID (e.g., '40 hours at rate R1')."
    
    PayUnitsOnly:
      type: "int"
      required: true
      description: "Number of employees paid based on units only (e.g., '10 units')."
    
    PayUnitsWithPayRate:
      type: "int"
      required: true
      description: "Number of employees paid based on units and an explicit rate (e.g., '10 units at $5/unit')."
    
    PayUnitsWithPayRateId:
      type: "int"
      required: true
      description: "Number of employees paid based on units and a rate ID (e.g., '10 units at rate U3')."
    
    PayAmountOnly:
      type: "int"
      required: true
      description: "Number of employees paid by a flat amount only (e.g., '$1000')."
    
    BonusPayments:
      type: "int"
      required: true
      description: "Number of employees receiving bonus payments."
    
    Tips:
      type: "int"
      required: true
      description: "Number of employees receiving tips."
    
    OverrideStandardHourlyRate:
      type: "int"
      required: true
      description: "Number of employees with overridden hourly rates."
    
    OverrideStandardSalary:
      type: "int"
      required: true
      description: "Number of employees with overridden salary."
    
    Overtime:
      type: "int"
      required: true
      description: "Number of employees with overtime."
    
    VacationTime:
      type: "int"
      required: true
      description: "Number of employees with vacation pay."
    
    SickTime:
      type: "int"
      required: true
      description: "Number of employees with sick time."
    
    OtherEarningsOrHours:
      type: "int"
      required: true
      description: "Number of employees with other earnings or hours."
    
    WhatWereTheOtherEntries:
      type: "str"
      required: true
      description: "Free-form description of other payroll items not classified above."
    
    InformationFormat:
      type: "literal"
      required: true
      options: ["embedded_table", "embedded_rows", "attachments_excel", "attachments_pdf", "attachments_other", "mix", "unclear", "unknown"]
      description: |
        How the payroll information is provided:
        - embedded_table: Information is in a table within the email
        - embedded_rows: Information is in text rows within the email
        - attachments_excel: Information is in an Excel attachment
        - attachments_pdf: Information is in a PDF attachment
        - attachments_other: Information is in another type of attachment
        - mix: Information is provided in multiple formats
        - unclear: Information format is unclear
        - unknown: Cannot determine the information format
    
    AttachmentType:
      type: "literal"
      required: true
      options: ["none", "excel", "pdf", "other", "unknown"]
      description: |
        The type of file attached, if any:
        - none: No attachments
        - excel: Excel or spreadsheet file
        - pdf: PDF document
        - other: Other file type
        - unknown: Attachments exist but type is unknown
    
    EmailLanguage:
      type: "literal"
      required: true
      options: ["english", "spanish", "other", "unclear", "unknown"]
      description: |
        The primary language of the email:
        - english: Email is in English
        - spanish: Email is in Spanish
        - other: Email is in another language
        - unclear: Language is unclear or mixed
        - unknown: Cannot determine the language
    
    PotentialFraud:
      type: "literal"
      required: true
      options: ["none", "low_risk", "medium_risk", "high_risk", "unknown"]
      description: |
        Risk level for potential fraud:
        - none: No fraud indicators
        - low_risk: Minor inconsistencies or unusual patterns
        - medium_risk: Several inconsistencies or unusual requests
        - high_risk: Major red flags or highly unusual requests
        - unknown: Cannot assess fraud risk
    
    InsufficientInformation:
      type: "literal"
      required: true
      options: ["yes", "no"]
      description: |
        Whether the request lacks enough information to proceed:
        - yes: Critical information is missing
        - no: Request contains sufficient information

complexity_tags:
  description: "Tags to assess the complexity of processing the email"
  fields:
    ComplexityTag:
      type: "str"
      required: true
      description: "Complexity level: easy, medium, or hard"
    
    ComplexityTagReason:
      type: "list"
      required: true
      description: "List of reasons that contributed to the assigned complexity"

# Knockout detection for complex scenarios
knockout_detection:
  description: "Binary classification for knockout scenarios that require special handling"
  fields:
    HasKnockout:
      type: "literal"
      required: true
      options: ["yes", "no"]
      description: "Whether the email contains any knockout scenarios"
    
    KnockoutRuleIds:
      type: "list"
      required: true
      description: "List of knockout rule IDs that apply to this email"
    
    KnockoutConfidence:
      type: "int"
      required: true
      description: "Confidence level (0-100) that knockout scenarios are present"