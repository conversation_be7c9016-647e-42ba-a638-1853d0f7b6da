import time
import json
import argparse
import pandas as pd
import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from datetime import datetime
from pathlib import Path
from tqdm import tqdm
from copy import copy

from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.cell.cell import MergedCell

# Config
HOST = "http://localhost:8000"
API_PREFIX = "/api/v1"
ROUTE = "/process-email"
ENDPOINT_URL = f"{HOST}{API_PREFIX}{ROUTE}"
EASY_TEST_FILE = "easy_tests.json"
APRIL_TEST_FILE = "golden_cases.json"
APRIL_13_VALID_TEST_FILE = "250618_SME_emails_100_prepared_valid_case.json"
COMPLEX_TEST_FILE = "complex_tests.json"
NON_PAYROLL_TEST_FILE = "non_payroll_tests.json"
HUNDRED_TESTS_FILE = "hundred_test_cases.json"
BONUS_TEST_FILE = "bonus_pay_tests.json"
GROSS_TEST_FILE = "gross_pay_tests.json"
HOURLY_TEST_FILE = "hourly_pay_tests.json"
MULTIPLE_TEST_FILE = "multiple_pay_types_tests.json"
SALARY_TEST_FILE = "salary_pay_tests.json"
TIPS_TEST_FILE = "tips_pay_tests.json"

# File paths
base_dir = Path(__file__).parent
APRIL_INPUT_FILE = base_dir / "ground_truth" / APRIL_TEST_FILE
APRIL_13_VALID_INPUT_FILE = base_dir / "ground_truth" / APRIL_13_VALID_TEST_FILE
EASY_INPUT_FILE = base_dir / "evaluation_cases" / EASY_TEST_FILE
COMPLEX_INPUT_FILE = base_dir / "evaluation_cases" / COMPLEX_TEST_FILE
NON_PAYROLL_INPUT_FILE = base_dir / "evaluation_cases" / NON_PAYROLL_TEST_FILE
HUNDRED_INPUT_FILE = base_dir / "evaluation_cases" / HUNDRED_TESTS_FILE
BONUS_INPUT_FILE = base_dir / "evaluation_cases" / BONUS_TEST_FILE
GROSS_INPUT_FILE = base_dir / "evaluation_cases" / GROSS_TEST_FILE
HOURLY_INPUT_FILE = base_dir / "evaluation_cases" / HOURLY_TEST_FILE
MULTIPLE_INPUT_FILE = base_dir / "evaluation_cases" / MULTIPLE_TEST_FILE
SALARY_INPUT_FILE = base_dir / "evaluation_cases" / SALARY_TEST_FILE
TIPS_INPUT_FILE = base_dir / "evaluation_cases" / TIPS_TEST_FILE

timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
OUTPUT_FILE_NAME = f"evaluation_results_{timestamp}.json"
OUTPUT_FILE = base_dir / "evaluation_results" / OUTPUT_FILE_NAME
OUTPUT_FILE.parent.mkdir(parents=True, exist_ok=True)

import re

# Remove illegal Excel characters
_illegal_chars_re = re.compile(r"[\x00-\x08\x0B\x0C\x0E-\x1F]")

def remove_illegal_chars(val):
    if isinstance(val, str):
        return _illegal_chars_re.sub("", val)
    return val

def load_test_cases(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

def call_api(case, max_retries=3, delay=3):
    last_exception = None
    for attempt in range(max_retries):
        try:
            response = requests.post(ENDPOINT_URL, json=case, timeout=120)
            response.raise_for_status()
            return response.json().get("response", "{}")
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                print(f"Retry attempt: {attempt}")
                time.sleep(delay)
    return {"error": f"API call failed after {max_retries} attempts: {str(last_exception)}"}

def flatten_dict(d, parent_key='', sep='.'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list) and all(isinstance(i, dict) for i in v):
            for i, subdict in enumerate(v):
                items.extend(flatten_dict(subdict, f"{new_key}[{i}]", sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def format_output(excel_path, reason_col, graph_col, summary, termination_graph, termination_reason, sheet_prefix):
    """
    """
    # constants
    table_startrow       = 4                # zero-based → Excel row 5 for DataFrame header
    table_header_row     = table_startrow + 1  # Excel row 5
    section_header_row   = table_header_row - 2  # Excel row 3

    wb = load_workbook(excel_path)
    ws = wb[f"{sheet_prefix}-Summary"]

    # 1) Big “Results” banner in row 1
    last_col = reason_col + termination_reason.shape[1]
    ws.merge_cells(start_row=1, start_column=1,
                   end_row=1, end_column=last_col)
    b = ws.cell(row=1, column=1)
    b.value     = f"{sheet_prefix} - Results"
    b.font      = Font(bold=True, size=14, color="FFFFFF")
    b.fill      = PatternFill("solid", fgColor="4F81BD")
    b.alignment = Alignment(horizontal="center", vertical="center")

    # 2) Section headers in row 3
    sections = [
        ("Successful",        1,                       summary.shape[1]),
        ("Termination graph", graph_col + 1,          termination_graph.shape[1]),
        ("Termination Reason", reason_col + 1,        termination_reason.shape[1]),
    ]
    for title, sc, width in sections:
        ws.merge_cells(start_row=section_header_row,
                       start_column=sc,
                       end_row=section_header_row,
                       end_column=sc + width - 1)
        cell = ws.cell(row=section_header_row, column=sc)
        cell.value     = title
        cell.font      = Font(bold=True, color="FFFFFF")
        cell.fill      = PatternFill("solid", fgColor="808080")
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # 3) Style the pandas header row on row 5
    header_fill = PatternFill("solid", fgColor="D9D9D9")
    header_font = Font(bold=True)
    for df, base_col in [
        (summary, 1),
        (termination_graph, graph_col + 1),
        (termination_reason, reason_col + 1),
    ]:
        for i in range(df.shape[1]):
            c = ws.cell(row=table_header_row, column=base_col + i)
            c.fill      = header_fill
            c.font      = header_font
            c.alignment = Alignment(horizontal="center", vertical="center")

    # 4) Bold + thick bottom border on the “Grand Total” row
    thin  = Side(style="thin")
    thick = Side(style="thick")
    for df, base_col in [
        (summary, 1),
        (termination_graph, graph_col + 1),
        (termination_reason, reason_col + 1),
    ]:
        gt_row = table_header_row + df.shape[0]
        for i in range(df.shape[1]):
            c = ws.cell(row=gt_row, column=base_col + i)
            c.font   = Font(bold=True)
            c.border = Border(top=thin, bottom=thick)

    # 5) Auto‐fit all columns
    for col_cells in ws.columns:
        non_blank = [c for c in col_cells if c.value not in (None, "")]
        if not non_blank:
            continue
        max_len = max(len(str(c.value)) for c in non_blank)
        col_letter = get_column_letter(col_cells[0].column)
        ws.column_dimensions[col_letter].width = max_len + 2

    # walk every cell in the Summary sheet and force horizontal='left'
    for row_cells in ws.iter_rows():
        for cell in row_cells:
            # preserve whatever vertical alignment you already had
            va = cell.alignment.vertical if cell.alignment else 'center'
            cell.alignment = Alignment(horizontal='left', vertical=va)

    wb.save(excel_path)


def run_evaluation(input_file=None, output_file=None, sheet_prefix=None, test_cases=None, max_workers=10):
    if test_cases is None and input_file is not None:
        test_cases = load_test_cases(input_file)

    results = []

    print(f"Running {len(test_cases)} test cases against {ENDPOINT_URL}...\n")

    # Parallelize API calls using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_case = {executor.submit(call_api, case): case for case in test_cases}
        for future in tqdm(as_completed(future_to_case), total=len(test_cases), desc="Evaluating"):
            case = future_to_case[future]
            try:
                result = future.result()
            except Exception as e:
                result = {"error": str(e)}
            results.append({
                "IMSEPCID": case.get("IMSEPCID"),
                "input": case,
                "output": result
            })

    # Flatten output for inspection
    flat_rows = []
    for row in results:
        flat = {"IMSEPCID": row.get("IMSEPCID")}
        flat.update(flatten_dict(row.get("output", {})))
        flat_rows.append(flat)

    df_flat = pd.DataFrame(flat_rows)
    df_flat = df_flat.map(remove_illegal_chars) # clean illegal characters

    # Create pivot summary for successful_flag + Grand Total
    summary = df_flat.pivot_table(
        index="successful_flag",
        aggfunc="size"
    ).reset_index(name="count")
    total = summary["count"].sum()
    summary = pd.concat([
        summary,
        pd.DataFrame([{"successful_flag": "Grand Total", "count": total}])
    ], ignore_index=True)

    # Termination graph + Grand Total
    termination_graph = df_flat.pivot_table(
        index="termination_dict.termination_graph",
        aggfunc="size"
    ).reset_index(name="count")
    termination_graph.rename(
        columns={"termination_dict.termination_graph": "termination_graph"},
        inplace=True
    )
    tg_total = termination_graph["count"].sum()
    termination_graph = pd.concat([
        termination_graph,
        pd.DataFrame([{"termination_graph": "Grand Total", "count": tg_total}])
    ], ignore_index=True)

    # Termination Reason + Grand Total
    termination_reason = df_flat.pivot_table(
        index=["termination_dict.termination_graph", "termination_dict.termination_reason"],
        aggfunc="size"
    ).reset_index(name="count")
    termination_reason.rename(
        columns={
            "termination_dict.termination_graph": "termination_graph",
            "termination_dict.termination_reason": "termination_reason"
        },
        inplace=True
    )
    tr_total = termination_reason["count"].sum()
    termination_reason = pd.concat([
        termination_reason,
        pd.DataFrame([{"termination_graph": "Grand Total", "termination_reason": "", "count": tr_total}])
    ], ignore_index=True)

    # Write all three tables side-by-side starting at Excel row 5
    excel_path = output_file.with_suffix(".xlsx")
    mode = "w" if not excel_path.exists() else "a"
    sheet_exists = None if mode == "w" else "overlay"
    with pd.ExcelWriter(excel_path, engine="openpyxl", mode=mode, if_sheet_exists=sheet_exists ) as writer:
        table_startrow  = 4  # zero-based → writes header at Excel row 5
        summary.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=0
        )
        graph_col = summary.shape[1] + 2
        termination_graph.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=graph_col,
        )
        reason_col = graph_col + termination_graph.shape[1] + 2
        termination_reason.to_excel(
            writer, sheet_name=f"{sheet_prefix}-Summary",
            index=False,
            startrow=table_startrow,
            startcol=reason_col
        )

        # also dump full results
        df_flat.to_excel(writer, sheet_name=f"{sheet_prefix}-FullResults", index=False)

    # apply the styling + blank rows
    format_output(excel_path, reason_col, graph_col,
                  summary, termination_graph, termination_reason, sheet_prefix)

    print(f"Evaluation completed. Results saved to {output_file} and {excel_path}")
    print(f"Summary saved to {excel_path} under the 'Summary' sheet.")
    print(f"Full results saved to {excel_path} under the 'FullResults' sheet.")


def combine_summary_sheets(excel_path, prefixes, target_name="Summary"):
    """
    Combine multiple per-prefix Summary sheets into one, preserving all formatting,
    merged cells, column widths, and row heights.
    """

    wb = load_workbook(excel_path)
    combined = wb.create_sheet(title=target_name)
    cur_row = 1

    for pref in prefixes:
        src = wb[f"{pref}-Summary"]
        max_row = src.max_row
        max_col = src.max_column
        row_offset = cur_row - 1

        # 1) replicate merged cell ranges with offset
        for merged in list(src.merged_cells.ranges):
            combined.merge_cells(
                start_row=merged.min_row + row_offset,
                start_column=merged.min_col,
                end_row=merged.max_row + row_offset,
                end_column=merged.max_col
            )

        # 2) copy column widths
        for col_letter, dim in src.column_dimensions.items():
            if dim.width:
                combined.column_dimensions[col_letter].width = dim.width

        # 3) copy row heights
        for row_idx, dim in src.row_dimensions.items():
            if dim.height:
                combined.row_dimensions[row_idx + row_offset].height = dim.height

        # 4) copy cell values and styles (skip interior merged cells)
        for r in range(1, max_row + 1):
            for c in range(1, max_col + 1):
                old = src.cell(row=r, column=c)
                # skip merged-cell proxies
                if isinstance(old, MergedCell):
                    continue

                new = combined.cell(row=r + row_offset, column=c)
                new.value = old.value
                if old.has_style:
                    new.font           = copy(old.font)
                    new.fill           = copy(old.fill)
                    new.border         = copy(old.border)
                    new.alignment      = copy(old.alignment)
                    new.number_format  = old.number_format
                    new.protection     = copy(old.protection)

        # leave one blank row before next block
        cur_row += max_row + 1

    # remove old per-prefix summary sheets
    for pref in prefixes:
        del wb[f"{pref}-Summary"]

    wb.save(excel_path)


def combine_fullresults_sheets(excel_path, prefixes, target_name="FullResults"):
    """
    Combine multiple per-prefix FullResults sheets into one, preserving formatting
    and adding a `test_set` column. Copies header only once, then data rows.
    """
    wb = load_workbook(excel_path)
    combined = wb.create_sheet(title=target_name)
    cur_row = 1
    first = True

    for pref in prefixes:
        src = wb[f"{pref}-FullResults"]
        max_row = src.max_row
        max_col = src.max_column

        if first:
            # copy header row
            for c in range(1, max_col + 1):
                old = src.cell(row=1, column=c)
                new = combined.cell(row=cur_row, column=c + 1)
                new.value = old.value
                if old.has_style:
                    new.font           = copy(old.font)
                    new.fill           = copy(old.fill)
                    new.border         = copy(old.border)
                    new.alignment      = copy(old.alignment)
                    new.number_format  = old.number_format
                    new.protection     = copy(old.protection)
            # test_set header cell
            hdr = combined.cell(row=cur_row, column=1)
            hdr.value = "test_set"
            hdr.font = copy(old.font)
            first = False

            # copy data rows
            for r in range(2, max_row + 1):
                new_row = cur_row + (r - 1)
                combined.cell(row=new_row, column=1).value = pref
                for c in range(1, max_col + 1):
                    old = src.cell(row=r, column=c)
                    new = combined.cell(row=new_row, column=c + 1)
                    new.value = old.value
                    if old.has_style:
                        new.font           = copy(old.font)
                        new.fill           = copy(old.fill)
                        new.border         = copy(old.border)
                        new.alignment      = copy(old.alignment)
                        new.number_format  = old.number_format
                        new.protection     = copy(old.protection)
            cur_row += max_row

        else:
            # only data rows (skip header)
            for r in range(2, max_row + 1):
                new_row = cur_row + (r - 2)
                combined.cell(row=new_row, column=1).value = pref
                for c in range(1, max_col + 1):
                    old = src.cell(row=r, column=c)
                    new = combined.cell(row=new_row, column=c + 1)
                    new.value = old.value
                    if old.has_style:
                        new.font           = copy(old.font)
                        new.fill           = copy(old.fill)
                        new.border         = copy(old.border)
                        new.alignment      = copy(old.alignment)
                        new.number_format  = old.number_format
                        new.protection     = copy(old.protection)
            cur_row += (max_row - 1)

    # remove old full-results sheets
    for pref in prefixes:
        del wb[f"{pref}-FullResults"]

    # 6) Style the header row of the FullResults sheet
    fr_ws = wb[f"FullResults"]
    fr_header_fill = PatternFill("solid", fgColor="D9D9D9")
    fr_header_font = Font(bold=True)
    fr_alignment   = Alignment(horizontal="center", vertical="center")

    # iterate over every cell in Excel row 1 of FullResults
    for cell in fr_ws[1]:
        cell.fill      = fr_header_fill
        cell.font      = fr_header_font
        cell.alignment = fr_alignment

    wb.save(excel_path)
    print("Styled “Summary” sheet with blank rows, banner, headers, and Grand Total.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run evaluation on payroll test cases and generate results.")

    # Positional argument: input file
    parser.add_argument(
        "-H", "--hundred",
        help="Whether to run the hundred test cases (default: False)",
        action="store_true",
        default=False
    )

    args = parser.parse_args()

    if args.hundred:
        run_evaluation(HUNDRED_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Hundred_Tests")
        print("Done – single 'Summary' and 'FullResults' sheets with original formatting preserved.")
    else:

        print("Running 100 April Tests...")
        run_evaluation(APRIL_INPUT_FILE, OUTPUT_FILE, sheet_prefix="April_100_TESTS")

        #print("Running 13 April Valid Tests...")
        #run_evaluation(APRIL_13_VALID_INPUT_FILE, OUTPUT_FILE, sheet_prefix="13_VALID_TESTS")

        #print("Running Easy Tests...")
        #run_evaluation(EASY_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Easy_Tests")

        #print("Running Complex Tests...")
        #run_evaluation(COMPLEX_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Complex_Tests")

        #print("Running Non-Payroll Tests...")
        #run_evaluation(NON_PAYROLL_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Non_Payroll_Tests")

        # print("Running Bonus Pay Tests...")
        # run_evaluation(BONUS_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Bonus_Tests")

        # print("Running Gross Pay Tests...")
        # run_evaluation(GROSS_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Gross_Tests")

        # print("Running Hourly Pay Tests...")
        # run_evaluation(HOURLY_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Hourly_Tests")

        # print("Running Multiple Pay Types Tests...")
        # run_evaluation(MULTIPLE_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Multiple_Tests")

        # print("Running Salary Pay Tests...")
        # run_evaluation(SALARY_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Salary_Tests")

        # print("Running Tips Pay Tests...")
        # run_evaluation(TIPS_INPUT_FILE, OUTPUT_FILE, sheet_prefix="Tips_Tests")

        excel_path = OUTPUT_FILE.with_suffix(".xlsx")

        prefixes = ["April_100_TESTS"]
        #prefixes = ["Easy_Tests", "Complex_Tests", "Non_Payroll_Tests", "Bonus_Tests", "Gross_Tests", "Hourly_Tests", "Multiple_Tests", "Salary_Tests", "Tips_Tests"]

        # 2) combine the summary sheets into one "Summary" (keeping all styles)
        combine_summary_sheets(excel_path, prefixes, target_name="Summary")

        # 3) combine the full-results into one "FullResults" with a test_set column
        combine_fullresults_sheets(excel_path, prefixes, target_name="FullResults")

        print("Done – single 'Summary' and 'FullResults' sheets with original formatting preserved.")
