FROM langchain/langgraph-api:3.12

RUN apt-get update && apt-get install -y \
    make curl ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Copy and install custom Paychex root certificate
COPY certs/paychex-root.pem /usr/local/share/ca-certificates/paychex-root.crt
RUN update-ca-certificates

# -- Adding local package . --
ADD . /deps/payroll-email-agent
# -- End of local package . --

# -- Installing all local dependencies --
RUN PYTHONDONTWRITEBYTECODE=1 uv pip install --system --no-cache-dir -c /api/constraints.txt -e /deps/*
# -- End of local dependencies install --
ENV LANGSERVE_GRAPHS='{"payroll_email_agent": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:graph", "account_lookup": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:account_lookup", "classification": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:classification", "payroll_extraction": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:payroll_extraction", "validation": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:validation", "execution": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:execution", "release": "/deps/payroll-email-agent/app/payroll_agent/graph/graph_builder.py:release"}'

# Tell Python/httpx to trust the updated certificate bundle
ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

# -- Ensure user deps didn't inadvertently overwrite langgraph-api
RUN mkdir -p /api/langgraph_api /api/langgraph_runtime /api/langgraph_license && touch /api/langgraph_api/__init__.py /api/langgraph_runtime/__init__.py /api/langgraph_license/__init__.py
RUN PYTHONDONTWRITEBYTECODE=1 uv pip install --system --no-cache-dir --no-deps -e /api
# -- End of ensuring user deps didn't inadvertently overwrite langgraph-api --
# -- Removing build deps from the final image ~<:===~~~ --
RUN pip uninstall -y pip setuptools wheel
RUN rm -rf /usr/local/lib/python*/site-packages/pip* /usr/local/lib/python*/site-packages/setuptools* /usr/local/lib/python*/site-packages/wheel* && find /usr/local/bin -name "pip*" -delete || true
RUN rm -rf /usr/lib/python*/site-packages/pip* /usr/lib/python*/site-packages/setuptools* /usr/lib/python*/site-packages/wheel* && find /usr/bin -name "pip*" -delete || true
RUN uv pip uninstall --system pip setuptools wheel && rm /usr/bin/uv /usr/bin/uvx

WORKDIR /deps/payroll-email-agent