FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Install make and other build tools needed for CI
RUN apt-get update && apt-get install -y \
    make iputils-ping curl ca-certificates dnsutils \
    && rm -rf /var/lib/apt/lists/*

# Copy and install custom Paychex root certificate
COPY certs/paychex-root.pem /usr/local/share/ca-certificates/paychex-root.crt
RUN update-ca-certificates

WORKDIR /app

# Tell Python/httpx to trust the updated certificate bundle
ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

COPY . .

RUN uv sync \
      --locked \
      --no-dev \
      --no-cache-dir

ENV PATH="/app/.venv/bin:$PATH" \
    PYTHONUNBUFFERED=1

# Pre-cache tiktoken encodings so runtime doesn't need outbound internet
ENV TIKTOKEN_CACHE_DIR=/usr/local/share/tiktoken
RUN python - <<'PY'
import os, sys
cache = os.environ.get("TIKTOKEN_CACHE_DIR", "/usr/local/share/tiktoken")
os.makedirs(cache, exist_ok=True)
try:
    import tiktoken
except Exception as e:
    print("tiktoken not installed; skipping pre-cache:", e)
    sys.exit(0)

encodings = [
    "cl100k_base",  # GPT-4/3.5 family
    "o200k_base",   # newer OpenAI models
    "p50k_base",    # Codex/older models
    "r50k_base",    # legacy BPE
]
for name in encodings:
    try:
        tiktoken.get_encoding(name)
        print("pre-cached", name)
    except Exception as e:
        print("failed to cache", name, e)
PY

EXPOSE 8000

ENTRYPOINT ["uvicorn"]
CMD ["app.main:app", "--host=0.0.0.0", "--port", "8000", "--reload", "--timeout-keep-alive", "40", "--workers", "5"]