"""
Configuration helper for Paychex API tests

HOW TO RUN:
    python tests/config.py

SETUP:
    1. Set environment variables:
       export PAYCHEX_CLIENT_ID="your_client_id"
       export PAYCHEX_CLIENT_SECRET="your_client_secret"
       export PAYCHEX_LLM_API_KEY="your_llm_api_key"

    2. OR create/update .env file in project root:
       PAYCHEX_CLIENT_ID=your_client_id
       PAYCHEX_CLIENT_SECRET=your_client_secret
       PAYCHEX_LLM_API_KEY=your_llm_api_key

    3. See .env.example for full configuration options

WHAT IT DOES:
    - Checks if all required credentials are configured
    - Displays configuration status and API URLs
    - Helps troubleshoot credential setup issues
    - Loads environment variables from .env file automatically
"""
import os
from typing import Optional


def load_env_file(env_file: str = ".env") -> None:
    """
    Load environment variables from a .env file
    Simple implementation without external dependencies
    """
    try:
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value
    except FileNotFoundError:
        pass  # .env file is optional


def get_paychex_credentials() -> tuple[Optional[str], Optional[str]]:
    """
    Get Paychex OAuth credentials from environment variables
    Returns: (client_id, client_secret)
    """
    load_env_file()
    
    client_id = os.getenv("PAYCHEX_CLIENT_ID")
    client_secret = os.getenv("PAYCHEX_CLIENT_SECRET")
    
    return client_id, client_secret


def get_llm_api_key() -> Optional[str]:
    """
    Get Paychex LLM API key from environment variables
    """
    load_env_file()
    return os.getenv("OPENAI_API_KEY")


def get_api_urls() -> dict[str, str]:
    """
    Get API URLs from environment variables with defaults
    """
    load_env_file()
    
    return {
        "oauth_url": os.getenv("PAYCHEX_OAUTH_URL", "https://oidc.n2a.paychex.com/oauth/token"),
        "api_base_url": os.getenv("PAYCHEX_API_BASE_URL", "https://api.n2a.paychex.com"),
        "llm_base_url": os.getenv("PAYCHEX_LLM_BASE_URL", "https://service-internal-n2a.paychex.com")
    }


def check_credentials() -> bool:
    """
    Check if required credentials are available
    """
    client_id, client_secret = get_paychex_credentials()
    
    if not client_id or not client_secret:
        print("❌ Missing Paychex OAuth credentials!")
        print("Please set the following environment variables:")
        print("  - PAYCHEX_CLIENT_ID")
        print("  - PAYCHEX_CLIENT_SECRET")
        print("\nYou can:")
        print("1. Set them directly: export PAYCHEX_CLIENT_ID='your_id'")
        print("2. Create a .env file (see .env.example)")
        return False
    
    return True


if __name__ == "__main__":
    print("🔧 Paychex API Configuration Check")
    print("=" * 50)
    
    # Check OAuth credentials
    client_id, client_secret = get_paychex_credentials()
    if client_id and client_secret:
        print(f"✅ OAuth Client ID: {client_id[:10]}...")
        print(f"✅ OAuth Client Secret: {'*' * len(client_secret)}")
    else:
        print("❌ OAuth credentials not found")
    
    # Check LLM API key
    llm_key = get_llm_api_key()
    if llm_key:
        print(f"✅ LLM API Key: {llm_key[:10]}...")
    else:
        print("⚠️  LLM API Key not found (optional)")
    
    # Show API URLs
    urls = get_api_urls()
    print(f"\n📡 API URLs:")
    for name, url in urls.items():
        print(f"  {name}: {url}")
    
    # Overall status
    if check_credentials():
        print(f"\n🎉 Configuration looks good! You can run the test scripts.")
    else:
        print(f"\n💥 Configuration incomplete. Please set missing credentials.")
