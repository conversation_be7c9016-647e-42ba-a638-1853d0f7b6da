import json

from app.cli.logging_utils import setup_logger
from app.payroll_agent.utils.funcs import load_routers_config
from app.payroll_agent.utils.mcp import get_mcp_service

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.states.execution import ExecutionOutputState
from app.payroll_agent.utils.logging import log_runtime
from app.payroll_agent.config.config import settings

# Set up the logger
logger = setup_logger(__name__)

ROUTERS_CONFIG = load_routers_config('routers_config')['execution']


@log_runtime("execution_prepare_execution_payload", "execution_", "execution_output_state")
def execution_prepare_execution_payload(state: PayrollState) -> PayrollState:
    """Build the execution payload grouped by worker, with clear flow and errors.

    This refactor preserves existing behavior while improving readability by:
    - Using small helper functions for failure and earnings building
    - Reducing repeated hasattr/getattr checks
    - Grouping commands by worker_id before building payloads
    """
    logger.info("execution_prepare_execution_payload called")

    def _fail(msg: str, exc: bool = False) -> PayrollState:
        if exc:
            logger.exception(msg)
        else:
            logger.error(msg)
        v = state.execution_output_state.prepare_payload
        v.success = False
        v.error = msg
        state.execution_output_state.should_continue = False
        state.execution_output_state.termination_reason = msg
        return state

    def _build_earnings(command):
        """Create earnings dict for a single command. Returns None if componentId missing."""
        earnings = {}
        amount = getattr(command, "amount", None)
        hours = getattr(command, "hours", None)
        units = getattr(command, "units", None)
        memo = getattr(command, "memo", None)
        rate = getattr(command, "rate", None)
        rate_id = getattr(command, "rate_id", None)
        component_id = getattr(command, "componentId", None)

        # Primary amount/hours/units fields (mutually exclusive in original logic)
        if amount is not None:
            earnings["payAmount"] = amount
        elif hours is not None:
            earnings["payHours"] = hours
            if memo is not None:
                earnings["memo"] = memo
            if rate is not None:
                earnings["payRate"] = rate
            elif rate_id is not None:
                earnings["payRateId"] = rate_id
        elif units is not None:
            earnings["payUnits"] = units
            if memo is not None:
                earnings["memo"] = memo
            if rate is not None:
                earnings["payRate"] = rate
            elif rate_id is not None:
                earnings["payRateId"] = rate_id
        elif rate_id is not None:
            # Preserve behavior: allow only rate_id if no amount/hours/units
            earnings["payRateId"] = rate_id

        # Required component id
        if component_id is None:
            return None
        earnings["componentId"] = component_id
        return earnings

    try:
        pay_period_id = getattr(state.company_worker_lookup_output_state, "payperiodID", None)
        if not pay_period_id:
            return _fail("PayPeriodId not found in company_worker_lookup_output_state")

        processed_payrolls = getattr(state.payroll_processing_output_state, "processed_payrolls", None) or []
        if not processed_payrolls:
            return _fail("No processed payroll commands found")

        # Group commands by worker
        by_worker = {}
        for cmd in processed_payrolls:
            by_worker.setdefault(cmd.worker_id, []).append(cmd)

        payload_list = []
        for worker_id, commands in by_worker.items():
            worker_payload = {worker_id: {"payPeriodId": pay_period_id, "earnings": []}}
            for command in commands:
                earnings = _build_earnings(command)
                if earnings is None:
                    return _fail(f"No componentId found for command: {command}", exc=True)
                worker_payload[worker_id]["earnings"].append(earnings)

            logger.info(f"Prepared payload for worker {worker_id}: {worker_payload}")
            payload_list.append(worker_payload)

        logger.info(f"Total payloads prepared for execution: {len(payload_list)}")
        logger.info(f"Payloads for execution: {payload_list}")

        # Store the payload list
        v = state.execution_output_state.prepare_payload
        v.payload = payload_list
        v.success = True
        v.error = None
        state.execution_output_state.formatted_payroll_entries = payload_list
        state.execution_output_state.should_continue = True
        return state

    except Exception as e:
        return _fail(f"Failed to prepare execution payload: {e}", exc=True)


@log_runtime("execution_prepare_payload_router", "execution_",
             "execution_output_state")
def execution_prepare_payload_router(state: PayrollState) -> PayrollState:
    logger.info(
        "execution_prepare_payload_router called, passing through state"
    )

    # checks
    conditions = [
        state.execution_output_state.prepare_payload.success,
        ]

    # check all conditions are met
    if all(conditions):
        state.execution_output_state.should_continue = True
        logger.info("Payrolls validated successfully")
    else:
        error_message = (
            "Payroll execution failed, error: "
            f"{state.execution_output_state.prepare_payload.error} "
        )
        logger.warning(error_message)
        state.execution_output_state.termination_reason = error_message
    return state


@log_runtime("execution_create_payrolls", "execution_", "execution_output_state")
async def execution_create_payrolls(state: PayrollState) -> PayrollState:  # Make async
    """Create payrolls by calling MCP tool for each worker."""
    logger.info("execution_create_payrolls called...")
    exec_state = state.execution_output_state
    try:
        # Get the prepared payload from execution
        prepared_payload = state.execution_output_state.prepare_payload
        validated_payload = prepared_payload.payload
        
        if not validated_payload:
            error_msg = "No validated payload found"
            logger.error(error_msg)
            exec_state.created_checks=None
            exec_state.created_checks_flag=False
            exec_state.termination_reason=error_msg
            return state

        # Initialize results tracking
        successful_workers = []
        failed_workers = []
        
        # Get MCP service
        mcp_service = get_mcp_service()
        
        # Handle both list and dict payload formats
        if isinstance(validated_payload, list):
            # List format: [{"worker_id": {...}}]
            for worker_dict in validated_payload:
                for worker_id, worker_payload in worker_dict.items():
                    success = await _create_worker_check(mcp_service, worker_id, worker_payload, state)  # Pass state for session_id
                    if success:
                        successful_workers.append(worker_id)
                        logger.info(f"Successfully created check for worker {worker_id}")
                    else:
                        failed_workers.append(worker_id)
                        logger.error(f"Failed to create check for worker {worker_id}")
        
        elif isinstance(validated_payload, dict):
            # Dict format: {"payPeriodId": "xx", "worker_id": {...}}
            for key, value in validated_payload.items():
                if key == "payPeriodId":
                    continue  # Skip the payPeriodId key
                
                worker_id = key
                worker_payload = value
                success = await _create_worker_check(mcp_service, worker_id, worker_payload, state)  # Pass state
                if success:
                    successful_workers.append(worker_id)
                    logger.info(f"Successfully created check for worker {worker_id}")
                else:
                    failed_workers.append(worker_id)
                    logger.error(f"Failed to create check for worker {worker_id}")
        
        # Create execution results
        total_workers = len(successful_workers) + len(failed_workers)
        success_rate = len(successful_workers) / total_workers if total_workers > 0 else 0
        
        logger.info(f"Execution completed: {len(successful_workers)}/{total_workers} workers successful")
        
        # Store results in execution state
        exec_state.created_checks=successful_workers  # List of successful worker IDs
        exec_state.created_checks_flag=len(failed_workers) == 0  # True if all succeeded
        exec_state.failed_payrolls=failed_workers  # Track failed workers
        exec_state.success_rate=success_rate
        exec_state.termination_reason=f"Failed workers: {failed_workers}" if failed_workers else None
        
        logger.debug(f"Created payrolls for {len(successful_workers)} workers")
        return state
        
    except Exception as e:
        error_msg = f"Execution failed: {str(e)}"
        logger.exception(error_msg)
        exec_state.created_checks=None
        exec_state.created_checks_flag=False
        exec_state.termination_reason=error_msg
        return state


async def _create_worker_check(mcp_service, worker_id: str, worker_payload: dict, state: PayrollState) -> bool:
    """Create a check for a single worker using MCP tool.
    Only performs the MCP call when settings.ENV_MODE == 'PROD'.
    In non-PROD modes, it skips the external call to avoid side effects and returns True.
    """
    try:
        # Skip external call unless we are in PROD
        if settings.ENV_MODE != 'PRODUCTION':
            logger.info(
                f"Skipping MCP worker check creation for worker {worker_id} because ENV_MODE={settings.ENV_MODE}"
            )
            return True

        # Follow the same pattern as company_worker_lookup.py
        result = await mcp_service.call_mcp_tool(
            server_name='paychex',
            tool_name='paychex_worker_check',
            tool_input={
                "worker_id": worker_id,
                "check_data": worker_payload
            },
            session_id=state.input_state.x_payx_sid
        )
        
        # Parse JSON response like other calls
        response = json.loads(result)
        
        # Check if the call was successful (following same pattern)
        if response.get('ok', False):
            logger.info(f"Successfully created check for worker {worker_id}")
            return True
        else:
            error_msg = response.get('error', 'Unknown error')
            logger.error(f"MCP tool call failed for worker {worker_id}: {error_msg}")
            return False
            
    except Exception as e:
        logger.exception(f"Exception calling MCP tool for worker {worker_id}: {str(e)}")
        return False


@log_runtime("execution_success_payroll_router", "execution_", "execution_output_state")
def execution_success_payroll_router(state: PayrollState) -> PayrollState:
    logger.info("execution_success_payroll_router called...")

    # Check if all workers were processed successfully
    completed_flag = state.execution_output_state.created_checks_flag
    created_checks = state.execution_output_state.created_checks or []
    
    if completed_flag and len(created_checks) >= 1:
        state.execution_output_state.should_continue = True
        logger.info(f"Payroll execution successful: {len(created_checks)} workers processed")
    else:
        state.execution_output_state.should_continue = False
        failed_payrolls = getattr(state.execution_output_state, 'failed_payrolls', [])
        error_message = (
            f"Payroll execution failed. "
            f"Successful: {len(created_checks)}, "
            f"Failed: {len(failed_payrolls)}, "
        )
        logger.warning(error_message)
        state.execution_output_state.termination_reason = error_message
        state.execution_output_state.termination_node = "execution_success_payroll_router"
    
    return state


@log_runtime("execution_terminate", "execution_", "execution_output_state")
def execution_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("execution_terminate called...")
    reason = state.execution_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.execution_output_state.should_continue = False
    state.execution_output_state.termination_node = execution_terminate.__name__
    logger.debug(f"execution_terminate returning PayrollState: {state}")
    return state


@log_runtime("execution_finishing_node", "execution_", "execution_output_state")
def execution_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("execution_finishing_node called...")
    return state
