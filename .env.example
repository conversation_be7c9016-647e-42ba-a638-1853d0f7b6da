ENV_MODE=LOCAL_DEV
# OpenAI API Configuration
OPENAI_API_KEY=openai_api_key_here

# LLM Configuration
# Option 1: Direct OpenAI API (default for local development)
# LLM_TYPE=OPENAI
# LLM_MODEL_REASONING=gpt-4o
# LLM_MODEL_REASONING_SECOND_AGENT=gpt-4.1

# Option 2: Azure OpenAI Configuration (Model as a Service - MaaS)
# This is the default configuration for Paychex internal environments
LLM_TYPE=AZURE
AZURE_ENDPOINT=https://service-internal-n2a.paychex.com
API_VERSION=2024-12-01-preview
# When using Paychex MaaS, the system will automatically format the endpoint as:
# {AZURE_ENDPOINT}/eps/shared/azure/openai/deployments/{model}

# LangSmith API Configuration
LANGCHAIN_API_KEY=langsmith_api_key_here
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT="https://langchain.paychexai.sandbox.azure.payx/api/v1"
LANGCHAIN_PROJECT="payroll-email-agent"


# AZue Configuration
AZURE_STORAGE_SAS_TOKEN="<sas_token for intake storage blob>"

# MCP Configuration
# These values are already defined in the docker-compose.yml for this service. Only uncomment if running outside docker.
# For a HTTP-based server: 
# MCP_SERVERS__PAYCHEX__URL="http://localhost:9000/mcp"
# MCP_SERVERS__PAYCHEX__TRANSPORT="streamable_http"
# MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="http://localhost:9000/auth/token" #uncomment if using a local run of the server

# New OAuth creds for that server
# NOTE: There is no need to add MCP_SERVERS__PAYCHEX__CLIENT_ID and MCP_SERVERS__PAYCHEX__CLIENT_SECRET to your .env file, as they are already defined in the docker-compose.yml for this service.
# Only uncomment if running outside docker.
# If you need to use a different client_id and client_secret, you can add them here but the should match the ones defined in the payroll-mcp-servers .env file.
# MCP_SERVERS__PAYCHEX__CLIENT_ID="payroll_agent"
# MCP_SERVERS__PAYCHEX__CLIENT_SECRET="key_here"

# MCP TESTING OAuth CA API uncomment if you need to use the CA API for testing
#PAYCHEX_CLIENT_ID="<your_client_id_here>"
#PAYCHEX_CLIENT_SECRET="<your_client_secret_here>"

# Dashboard Integration (Easy Auth)
#ENABLE_DASHBOARD_INTEGRATION=true
#DASHBOARD_API_URL="https://your-dashboard-backend-fqdn"
#AZURE_CLIENT_ID="<dashboard-app-registration-client-id>"
#EASY_AUTH_ENABLED=false               # set false for local dev to send requests without auth
#EASY_AUTH_USE_DEFAULT_CREDENTIAL=false # Only used if EASY_AUTH_ENABLED=true; set false in Azure (Managed Identity).


# Test Intake Generation + Sending
# Endpoint to send the generated intake payloads
#INGEST_EMAIL_URL="https://func-payrollai-emailintake-eastus-prshadow-001.azurewebsites.net/api/v1/ingest-email"

# OAuth2 client credentials for Bearer token
# Use v2.0 with scope (recommended)
#AAD_TENANT_ID="<tenant-guid>"
#AAD_CLIENT_ID="<client-id>"
#AAD_CLIENT_SECRET="<client-secret>"
#AAD_SCOPE="api://<target-app-client-id>/.default"

# Or, optionally, v1.0 with resource
#AAD_RESOURCE="api://<target-app-client-id>"  # if using v1 token endpoint style
