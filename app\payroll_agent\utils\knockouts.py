import yaml
import logging

from typing import List, Literal, Optional
from pydantic import BaseModel, Field, create_model
from pathlib import Path


logger = logging.getLogger(__name__)

def load_knockout_library(config_path: Path) -> list:
    """Load knockout rules from a YAML configuration file."""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    active_rules = []
    for rule in config.get('knockout_rules', []):
        if rule and isinstance(rule, dict) and 'id' in rule and 'description' in rule:
            examples = rule.get('examples', [])
            if examples and any(example and example.strip() for example in examples):
                active_rules.append(rule)
    return active_rules


def chunk_knockout_rules(rules, max_per_chunk=15):
    """Split knockout rules into chunks."""
    chunks = []
    logger.info(f"Total L1 intents in knockout library: {len(rules)}")
    for i in range(0, len(rules), max_per_chunk):
        chunks.append(rules[i:i + max_per_chunk])
    return chunks


def create_knockout_rules_text_for_batch(rule_batch):
    """Create formatted text for a batch of knockout rules, including condition."""
    rule_descriptions = []
    for rule in rule_batch:
        rule_id = rule['id']
        description = rule.get('description', '')
        condition = rule.get('condition', '')
        examples = rule.get('examples', [])

        if examples:
            example_text = "\n    Examples: " + "; ".join(f'"{ex}"' for ex in examples[:3])
        else:
            example_text = ""

        # Include the condition in the output!
        rule_descriptions.append(
            f"- **{rule_id}**: {description}\n  Condition: {condition}{example_text}"
        )

    return "\n".join(rule_descriptions)


def create_knockout_model(rules, model_name="KnockoutIntents"):
    """Create a Pydantic model for knockout rules."""
    fields = {}
    for rule in rules:
        rule_id = rule['id']
        fields[f"{rule_id}_confidence"] = (int, Field(..., description=f"Confidence (0-100) for {rule_id}: {rule['description']}"))
        fields[f"{rule_id}_confidence_reasoning"] = (Optional[str], Field(default=None, description=f"Reasoning for {rule_id}"))
    fields['DetectedKnockoutRules'] = (List[str], Field(default=[], description="List of detected knockout rule IDs"))
    return create_model(model_name, **fields)


def chunk_knockout_rules(rules, max_per_chunk=15):
    """Split knockout rules into chunks."""
    return [rules[i:i + max_per_chunk] for i in range(0, len(rules), max_per_chunk)]


def create_knockout_batch_models(rules, max_rules_per_chunk=15, model_prefix="KnockoutIntentsBatch"):
    """Create batch models for knockout rules."""
    rule_chunks = chunk_knockout_rules(rules, max_rules_per_chunk)
    batch_models = []
    for batch_idx, rule_chunk in enumerate(rule_chunks):
        fields = {}
        for rule in rule_chunk:
            rule_id = rule['id']
            fields[f"{rule_id}_confidence"] = (int, Field(..., description=f"Confidence (0-100) for {rule_id}"))
            fields[f"{rule_id}_confidence_reasoning"] = (Optional[str], Field(default=None, description=f"Reasoning for {rule_id}"))
        fields['DetectedKnockoutRules'] = (List[str], Field(default=[], description="Detected rule IDs"))
        batch_model = create_model(f'{model_prefix}{batch_idx + 1}', **fields)
        batch_models.append({
            'model': batch_model,
            'rules': rule_chunk,
            'batch_name': f'Batch{batch_idx + 1}'
        })
    return batch_models
