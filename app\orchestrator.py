import asyncio
import os
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass
from app.api.models.output import OrchestrationResponse
from app.payroll_agent.utils.blob_storage import blob_client, BlobStorageError, BlobNotFoundError
from app.payroll_agent.utils.dashboard import dashboard_integration
import asyncio
from concurrent.futures import ThreadPoolExecutor
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.dashboard import DashboardIntegration
from app.payroll_agent.utils.langgraph_invocation import (
    get_langgraph_client,
    invoke_remote_graph,
    invoke_local_graph,
)

# Import local graph components (same as /process-email endpoint)
from app.payroll_agent.graph.states.classification import InputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.graph_builder import graph
from app.payroll_agent.utils.funcs import format_payroll_state_response

from app.cli.logging_utils import setup_logger

logger = setup_logger('api.orchestrator')
    
class PayrollOrchestrator:
    """
    Orchestrates the payroll processing workflow:
    1. Fetch payload from blob storage
    2. Prepare payload for processing
    3. Trigger remote LangGraph endpoint
    4. Send results to dashboard
    5. Return status
    """
    
    def __init__(self):
        self.blob_service = blob_client
        self.langgraph_api_url = settings.LANGGRAPH_API_URL
        self.dashboard_service = DashboardIntegration()
        self.dashboard_service.enabled = settings.ENABLE_DASHBOARD_INTEGRATION or True
        self.dashboard_service.dashboard_url = settings.DASHBOARD_API_URL or "https://conagentdasheastusn0001-backend.gentlebush-561de07f.eastus.azurecontainerapps.io"
        self.dashboard_service.timeout = settings.DASHBOARD_TIMEOUT or 15.0

    async def orchestrate_payroll_processing(self, ingest_id: str) -> OrchestrationResponse:
        """Main orchestration method"""
        started_at = datetime.utcnow()
        langgraph_invoke_method = "unknown"
        remote_failed = False
        raw_payload = None
        prepared_payload = None
        processing_result = {"success": False}
        
        try:
            # Step 1: Fetch payload from blob storage
            logger.info(f"Step 1: Fetching payload for ingest_id: {ingest_id}")
            raw_payload = await self._fetch_payload_from_blob(ingest_id)
            
            # Extract initial information from raw payload
            display_id = self._extract_display_id_from_payload(raw_payload)
            
            # Step 2: Prepare payload for processing
            logger.info(f"Step 2: Preparing payload for processing")
            prepared_payload = await self._prepare_payload(raw_payload)
            
            # Step 3: Process with LangGraph (remote first, then local fallback)
            logger.info(f"Step 3: Processing with LangGraph")
            
            # Try remote first if configured
            if self.langgraph_api_url:
                try:
                    logger.info(f"Attempting remote LangGraph processing for ingest_id: {ingest_id}")
                    processing_result = await invoke_remote_graph(
                        self.langgraph_api_url, 
                        prepared_payload
                    )
                    langgraph_invoke_method = "remote"
                    if processing_result.get("success"):
                        logger.info(f"Remote LangGraph processing successful for ingest_id: {ingest_id}")
                    else:
                        remote_failed = True
                        logger.warning(f"Remote LangGraph processing failed for ingest_id: {ingest_id}, falling back to local")
                except Exception as e:
                    remote_failed = True
                    logger.error(f"Remote LangGraph processing error for ingest_id: {ingest_id}: {e}")
            
            # Try local if remote not available or failed
            if not processing_result.get("success", False):
                logger.info(f"Attempting local LangGraph processing for ingest_id: {ingest_id}")
                processing_result = await self._invoke_local_langgraph(prepared_payload)
                langgraph_invoke_method = "local" if not remote_failed else "local_fallback"

        except Exception as e:
            logger.error(f"Processing error for ingest_id: {ingest_id}: {e}", exc_info=True)
            # Create a skeleton processing result for failed cases
            processing_result = self._create_skeleton_processing_result(
                ingest_id, 
                str(e), 
                type(e).__name__,
                prepared_payload,
                raw_payload
            )
            langgraph_invoke_method = "failed"

        finally:
            # Step 4: Send results to dashboard (success or failure)
            logger.info(f"Step 4: Sending results to dashboard for ingest_id: {ingest_id}")
            dashboard_success = await self._send_results_to_dashboard(
                ingest_id, 
                processing_result, 
                started_at,
                raw_payload,
                prepared_payload
            )
            
            # Calculate final timing
            finished_at = datetime.utcnow()
            run_time = (finished_at - started_at).total_seconds()
            
            # Step 5: Prepare final result based on processing outcome
            if not processing_result.get("success", False):
                error_msg = processing_result.get("error", "Unknown processing error")
                logger.error(
                    f"LangGraph processing failed for ingest_id: {ingest_id}. "
                    f"Remote attempted: {self.langgraph_api_url is not None}, "
                    f"Remote failed: {remote_failed}, "
                    f"Error: {error_msg}"
                )
                
                # Extract display_id safely
                display_id = ""
                if prepared_payload:
                    display_id = prepared_payload.get("displayId", "")
                elif raw_payload:
                    display_id = self._extract_display_id_from_payload(raw_payload)
                
                return OrchestrationResponse(
                    ingest_id=ingest_id,
                    status=f"processing_failed: {error_msg}",
                    status_code=500,
                    started_at=started_at.isoformat(),
                    finished_at=finished_at.isoformat(),
                    run_time=run_time,
                    displayId=display_id,
                    temp_langgraph_invoke_method=langgraph_invoke_method,
                )
            
            # Success case
            status = "completed"
            if not dashboard_success:
                status = "completed_dashboard_warning"
                logger.warning(f"Processing completed for ingest_id: {ingest_id} but dashboard logging failed.")

            logger.info(f"Step 5: Orchestration completed successfully for ingest_id: {ingest_id} in {run_time:.2f}s")

            # Extract values from processing result for response fields
            formatted_result = processing_result.get("formatted_result", {})
            
            # Extract fields from the result state
            processing_status = "success" if processing_result.get("success") else "fail"
            
            # Extract display_id safely
            display_id = ""
            if prepared_payload:
                display_id = prepared_payload.get("displayId", "")
            elif raw_payload:
                display_id = self._extract_display_id_from_payload(raw_payload)
            
            # Safely extract nested values from formatted_result
            company_lookup_state = formatted_result.get("company_worker_lookup_output_state", {})
            pay_period_id = company_lookup_state.get("payPeriodId", "")
            payroll_id = company_lookup_state.get("payrollId", "")

            return OrchestrationResponse(
                ingest_id=ingest_id,
                displayId=display_id,
                status=status,
                status_code=200,
                started_at=started_at.isoformat(),
                finished_at=finished_at.isoformat(),
                run_time=run_time,
                processing_status=processing_status,
                payPeriodId=str(pay_period_id) if pay_period_id else "",
                payrollId=str(payroll_id) if payroll_id else "",
                knockout_flag=formatted_result.get("knockout_flag", False),
                knockout_reason=formatted_result.get("knockout_reason", []),
                csa_action_flag=formatted_result.get("csa_action_flag", False),
                csa_note=formatted_result.get("csa_note", {}),
                pre_processing_flag=formatted_result.get("pre_processing_flag", False),
                reports=formatted_result.get("reports", []),
                hold_flag=formatted_result.get("hold_flag", False),
                hold=formatted_result.get("hold", []),
                email_response=formatted_result.get("email_response", {}),
                temp_langgraph_invoke_method=langgraph_invoke_method,
                temp_agent_response=processing_result
            )

    def _create_skeleton_processing_result(
        self, 
        ingest_id: str, 
        error_msg: str, 
        error_type: str,
        prepared_payload: Optional[Dict[str, Any]] = None,
        raw_payload: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a skeleton processing result for failed cases"""
        
        # Extract what we can from available payloads
        display_id = ""
        email_content = ""
        timestamp = ""
        uid = ""
        
        if prepared_payload:
            display_id = prepared_payload.get("displayId", "")
            email_content = prepared_payload.get("EmailContent", "")
            timestamp = prepared_payload.get("timestamp", "")
            uid = prepared_payload.get("uid", "")
        elif raw_payload:
            display_id = self._extract_display_id_from_payload(raw_payload)
            # Extract basic fields from raw payload
            original_payload = raw_payload.get("originalPayload", {})
            comment = original_payload.get("comment", {})
            messages = comment.get("messages", [])
            if messages:
                email_content = messages[0].get("body", {}).get("text", "")
                timestamp = messages[0].get("sent_at", "") or comment.get("timestamp", "")
            uid = raw_payload.get("uid", "")
        
        # Generate missing required fields
        if not uid:
            import uuid
            uid = str(uuid.uuid4())
            logger.warning(f"Generated uid for failed processing: {uid}")
        
        if not timestamp:
            timestamp = datetime.utcnow().isoformat()
            logger.warning(f"Using current time as timestamp for failed processing")
        
        # Create skeleton result_state structure with empty states
        skeleton_result_state = {
            "input_state": {
                "ingestId": ingest_id,
                "uid": uid,
                "displayId": display_id,
                "timestamp": timestamp,
                "EmailContent": email_content,
            },
            "classification_output_state": {},
            "company_worker_lookup_output_state": {},
            "payroll_processing_output_state": {},
            "validation_output_state": {},
            "execution_output_state": {},
            "release_output_state": {}
        }
        
        # Create skeleton formatted_result
        skeleton_formatted_result = {
            "input_state": skeleton_result_state["input_state"],
            "classification_output_state": skeleton_result_state["classification_output_state"],
            "company_worker_lookup_output_state": skeleton_result_state["company_worker_lookup_output_state"],
            "payroll_processing_output_state": skeleton_result_state["payroll_processing_output_state"],
            "validation_output_state": skeleton_result_state["validation_output_state"],
            "execution_output_state": skeleton_result_state["execution_output_state"],
            "release_output_state": skeleton_result_state["release_output_state"],
            "processing_status": f"failed: {error_msg}",
            "knockout_flag": True,
            "knockout_reason": ["MANUAL_CHECKS", "PROCESSING_ERROR"],
            "csa_action_flag": False,
            "csa_note": {},
            "pre_processing_flag": False,
            "reports": [],
            "hold_flag": False,
            "hold": [],
            "email_response": {}
        }
        
        skeleton_processing_result = {
            "success": False,
            "processing_method": "skeleton_failure",
            "error": error_msg,
            "result_state": skeleton_result_state,
            "formatted_result": skeleton_formatted_result,
            "metadata": {
                "error_type": error_type,
                "created_skeleton": True,
                "has_prepared_payload": prepared_payload is not None,
                "has_raw_payload": raw_payload is not None
            }
        }

        logger.debug(f"Created skeleton processing result for failed case: {ingest_id}")
        logger.info(f"Error: {error_msg} ({error_type})")
        logger.debug(f"Extracted data - displayId: '{display_id}', uid: '{uid}', timestamp: '{timestamp}'")
        
        # ADD THE MISSING RETURN STATEMENT!
        return skeleton_processing_result

    def _extract_display_id_from_payload(self, raw_payload: Dict[str, Any]) -> str:
        """Extract displayId (accountNumber) from raw payload"""
        try:
            clients = raw_payload.get("originalPayload", {}).get("clients", [])
            if clients and len(clients) > 0:
                return clients[0].get("accountNumber", "")
        except Exception as e:
            logger.warning(f"Failed to extract displayId from payload: {e}")
        return ""

    async def _fetch_payload_from_blob(self, ingest_id: str) -> Dict[str, Any]:
        """
        Step 1: Fetch payload from blob storage using ingest_id
        
        Args:
            ingest_id: Unique identifier for the stored payload
            
        Returns:
            Dict containing the raw payload data
        """
        logger.debug(f"Fetching payload for ingest_id: {ingest_id}")
        
        try:
            # Use the blob storage client to download the payload
            payload = await self.blob_service.download_json(ingest_id, "payload.json")
            
            logger.info(f"Successfully fetched payload for ingest_id: {ingest_id}")
            logger.info(f"Payload keys: {list(payload.keys())}")
            
            return payload
            
        except BlobNotFoundError:
            logger.error(f"Payload not found for ingest_id: {ingest_id}")
            raise
        except BlobStorageError as e:
            logger.error(f"Blob storage error for ingest_id: {ingest_id}, error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching payload for ingest_id: {ingest_id}, error: {e}")
            raise
    
    async def _prepare_payload(self, raw_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform raw payload into format expected by LangGraph InputState
        
        Args:
            raw_payload: Raw data from blob storage with consistent structure
            
        Returns:
            Dict formatted for LangGraph InputState processing
        """
        logger.info("=== PREPARING PAYLOAD FOR LANGGRAPH ===")
        logger.info(f"Raw payload keys: {list(raw_payload.keys())}")
        
        # Extract the main data sections
        ingest_id = raw_payload.get("ingestId", "")
        display_id_list = [
            client.get("accountNumber")
            for client in raw_payload.get("originalPayload", {}).get("clients", [])
            if client.get("accountNumber")
        ]
        display_id = display_id_list[0] if len(display_id_list) == 1 else display_id_list
        uid = raw_payload.get("uid", "")
        original_payload = raw_payload.get("originalPayload", {})
        comment = original_payload.get("comment") or raw_payload.get("comment", {})
        messages = comment.get("messages", [])
        user_properties = comment.get("user_properties", {})
        
        # Get the first message (primary email)
        first_message = messages[0] if messages else {}
        
        # Extract required fields - fail if they don't exist
        email_content = ""
        if first_message.get("body", {}).get("text"):
            email_content = first_message["body"]["text"]
        
        if not email_content:
            raise ValueError("EmailContent is required but not found in payload")
        
        # Extract timestamp - fail if not found
        timestamp = comment.get("timestamp") or first_message.get("sent_at", "")
        if not timestamp:
            raise ValueError("timestamp is required but not found in payload")
        
        # Rest of the extraction logic...
        # Extract other fields
        sender_from = first_message.get("from", "")
        
        # Extract recipients (to + cc)
        recipients = []
        if first_message.get("to"):
            recipients.extend(first_message["to"])
        if first_message.get("cc"):
            recipients.extend(first_message["cc"])
        
        # Extract subject
        subject = ""
        if first_message.get("subject", {}).get("text"):
            subject = first_message["subject"]["text"]
        
        sender_domain = user_properties.get("string:Sender Domain", "")
        business_unit = user_properties.get("string:Business Unit ID", "")
        
        # Map to InputState format
        prepared_payload = {
            "ingestId": ingest_id,
            "displayId": display_id,
            "timestamp": timestamp,
            "EmailContent": email_content,
            
            # Optional fields
            "companyID": business_unit if business_unit else None,
            "uid": uid if uid else None,
            "id": comment.get("id"),
            "CustomerDomain": sender_domain if sender_domain else None,
            "SourceAddress": sender_from,
            "DestinationAddress": recipients if recipients else None,
            "Subject": subject if subject else None,
        }
        
        # Clean up None values for optional fields only
        cleaned_payload = {k: v for k, v in prepared_payload.items() if v is not None and v != ""}
        
        # Ensure required fields are always present
        cleaned_payload.update({
            "ingestId": ingest_id,
            "displayId": display_id,
            "timestamp": timestamp,
            "EmailContent": email_content
        })
        
        logger.info(f"Prepared payload with required fields: ingestId, displayId, timestamp, EmailContent")
        return cleaned_payload
    
    async def _invoke_local_langgraph(self, prepared_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Step 3: Process payload using local LangGraph (temporary until remote is ready)
        
        Args:
            prepared_payload: Formatted payload ready for processing
            
        Returns:
            Dict containing the processing results and final state
        """
        logger.info("Processing with local LangGraph agent")
        
        try:
            
            # Create InputState from prepared payload
            logger.debug("Creating InputState from prepared payload")
            input_state = InputState(**prepared_payload)
            logger.info(f"Generated session ID for API Call tracing: {input_state.x_payx_sid}")
            
            # Initialize PayrollState
            payroll_state = PayrollState(input_state=input_state)
            logger.debug(f"Initialized PayrollState: {payroll_state!r}")
            
            # Process with local graph
            logger.info("Invoking local LangGraph processing")
            result_state = await graph.ainvoke(payroll_state, config={"recursion_limit": 120})
            logger.info("Local LangGraph processing completed")
            
            # Format the response (same as /process-email)
            formatted_result = format_payroll_state_response(result_state)
            
            # Return structured result for orchestrator
            processing_result = {
                "success": True,
                "processing_method": "local_langgraph",
                "result_state": result_state,  # Keep full state for dashboard logging
                "formatted_result": formatted_result,
                "metadata": {
                    "input_fields": len(prepared_payload),
                    "processing_mode": "local"
                }
            }
            
            logger.debug("Local LangGraph processing completed successfully")
            return processing_result
            
        except Exception as e:
            logger.error(f"Local LangGraph processing failed: {e}", exc_info=True)
            
            # Return error result
            return {
                "success": False,
                "processing_method": "local_langgraph",
                "error": str(e),
                "result_state": None,
                "formatted_result": None,
                "metadata": {
                    "input_fields": len(prepared_payload),
                    "processing_mode": "local",
                    "error_type": type(e).__name__
                }
            }
        
    async def _send_results_to_dashboard(
        self, 
        ingest_id: str, 
        processing_result: Dict[str, Any], 
        started_at: datetime,
        raw_payload: Optional[Dict[str, Any]] = None,
        prepared_payload: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Step 4: Send processing results to dashboard - handles both success and failure cases"""
        logger.info(f"Sending results to dashboard for ingest_id {ingest_id}")
        logger.info(f"Dashboard URL: {self.dashboard_service.dashboard_url}")
        logger.info(f"Processing success: {processing_result.get('success', False)}")
        
        try:
            finished_at = datetime.utcnow()
            run_time = round((finished_at - started_at).total_seconds(), 2)
            result_state = processing_result.get("result_state")
            
            # For both success and failure cases, we should have result_state now
            if not result_state:
                logger.error("No result_state available for dashboard logging - this should not happen")
                return False
            
            # Format the result_state (works for both success and skeleton failure states)
            formatted_result = format_payroll_state_response(result_state)
            
            # Add the additional root-level keys that the dashboard expects
            formatted_result["started_at"] = started_at.isoformat()
            formatted_result["finished_at"] = finished_at.isoformat()
            formatted_result["run_time"] = run_time
            formatted_result["ingest_id"] = ingest_id

            if "termination_dict" not in formatted_result:
                formatted_result["termination_dict"] = {}
            
            # Set processing status for dashboard with error details if failed
            if processing_result.get("success"):
                formatted_result["processing_status"] = "success"
            else:
                error_msg = processing_result.get("error", "Unknown error")
                error_type = processing_result.get("metadata", {}).get("error_type", "UnknownError")
                formatted_result["processing_status"] = f"failed: {error_type} - {error_msg}"
                formatted_result["termination_dict"] = {}
            
            # Ensure required fields are present
            self._ensure_dashboard_fields(formatted_result, ingest_id, raw_payload, prepared_payload)
            
            # PRINT ONLY WHAT GETS SENT TO DASHBOARD
            logger.info("=" * 80)
            logger.info("📤 ACTUAL PAYLOAD SENT TO DASHBOARD")
            logger.info("=" * 80)
            logger.info(f"Processing Status: {formatted_result.get('processing_status', 'unknown')}")
            import json
            logger.info(json.dumps(formatted_result, indent=2, default=str))
            logger.info("=" * 80)
            logger.info("END DASHBOARD PAYLOAD")
            logger.info("=" * 80)
            
            # Send to dashboard with retry logic
            dashboard_success = await self._send_to_dashboard_with_retry(formatted_result, started_at, finished_at)
            
            if dashboard_success:
                logger.info(f"Dashboard logging completed successfully for ingest_id: {ingest_id}")
            else:
                logger.error(f"Dashboard logging failed for ingest_id: {ingest_id}")
                
            return dashboard_success
            
        except Exception as e:
            logger.error(f"Dashboard logging error for ingest_id {ingest_id}: {e}", exc_info=True)
            return False

    def _ensure_dashboard_fields(self, formatted_result: Dict[str, Any], ingest_id: str, raw_payload: Optional[Dict[str, Any]], prepared_payload: Optional[Dict[str, Any]]):
        """Ensure required fields are present in the dashboard payload"""
        if 'input_state' in formatted_result and isinstance(formatted_result['input_state'], dict):
            # Check if uid is missing
            if not formatted_result['input_state'].get('uid'):
                uid = None
                if prepared_payload:
                    uid = prepared_payload.get('uid')
                elif raw_payload:
                    uid = raw_payload.get('uid')
                
                if uid:
                    formatted_result['input_state']['uid'] = uid
                    logger.info(f"Added missing uid to input_state: {uid}")
                else:
                    import uuid
                    generated_uid = str(uuid.uuid4())
                    formatted_result['input_state']['uid'] = generated_uid
                    logger.warning(f"Generated uid for missing session_uid: {generated_uid}")
            
            # Ensure ingest_id is present
            if not formatted_result['input_state'].get('ingest_id'):
                formatted_result['input_state']['ingest_id'] = ingest_id
                logger.info(f"Added ingest_id to input_state: {ingest_id}")

    def _log_dashboard_payload(self, formatted_result: Dict[str, Any]):
        """Log dashboard payload structure for debugging"""
        logger.info("=== DASHBOARD PAYLOAD KEYS ===")
        logger.info(f"Root level keys: {list(formatted_result.keys())}")
        logger.info(f"Root level ingest_id: {formatted_result.get('ingest_id', 'MISSING')}")
        logger.info(f"Processing status: {formatted_result.get('processing_status', 'success')}")
        if 'input_state' in formatted_result:
            logger.info(f"input_state keys: {list(formatted_result['input_state'].keys())}")
            logger.info(f"input_state.uid: {formatted_result['input_state'].get('uid', 'MISSING')}")
            logger.info(f"input_state.ingest_id: {formatted_result['input_state'].get('ingest_id', 'MISSING')}")
        logger.info("=== END DASHBOARD PAYLOAD KEYS ===")

    async def _send_to_dashboard_with_retry(self, formatted_result: Dict[str, Any], started_at: datetime, finished_at: datetime) -> bool:
        """Send to dashboard with retry logic"""
        # Use existing dashboard integration with the formatted result

        dashboard_success = await self.dashboard_service.log_transaction(
            formatted_result,
            started_at.isoformat(),
            finished_at.isoformat()
        )

        
        # If dashboard logging failed, try with a regenerated UUID
        # if not dashboard_success:
        #     logger.warning(f"Dashboard logging failed, trying with regenerated UUID")
        #
        #     try:
        #         import uuid
        #
        #         # Generate a new random UUID and update the formatted result
        #         new_ingest_id = str(uuid.uuid4())
        #         logger.info(f"Generated new ingest_id for retry: {new_ingest_id}")
        #
        #         # Update the ingest_id in BOTH root level and input_state
        #         formatted_result['ingest_id'] = new_ingest_id
        #
        #         if 'input_state' in formatted_result and isinstance(formatted_result['input_state'], dict):
        #             original_ingest_id = formatted_result['input_state'].get('ingest_id', 'unknown')
        #             formatted_result['input_state']['ingest_id'] = new_ingest_id
        #             logger.info(f"Updated input_state ingest_id from {original_ingest_id} to {new_ingest_id}")
        #
        #             # Also ensure uid is still present for retry
        #             if not formatted_result['input_state'].get('uid'):
        #                 new_uid = str(uuid.uuid4())
        #                 formatted_result['input_state']['uid'] = new_uid
        #                 logger.info(f"Added uid for retry: {new_uid}")
        #
        #         # Retry dashboard logging
        #         def retry_dashboard_logging():
        #             try:
        #                 loop = asyncio.new_event_loop()
        #                 asyncio.set_event_loop(loop)
        #                 try:
        #                     return loop.run_until_complete(
        #                         self.dashboard_service.log_transaction(formatted_result, started_at.isoformat(), finished_at.isoformat())
        #                     )
        #                 finally:
        #                     loop.close()
        #             except Exception as e:
        #                 logger.error(f"Dashboard logging retry thread failed: {e}", exc_info=True)
        #                 return False
        #
        #         with ThreadPoolExecutor(max_workers=1) as executor:
        #             retry_future = executor.submit(retry_dashboard_logging)
        #             dashboard_success = retry_future.result(timeout=120)
        #
        #         if dashboard_success:
        #             logger.info(f"Dashboard logging succeeded with regenerated ingest_id: {new_ingest_id}")
        #         else:
        #             logger.error(f"Dashboard logging failed even with regenerated ingest_id: {new_ingest_id}")
        #
        #     except Exception as retry_error:
        #         logger.error(f"Error during dashboard retry with new UUID: {retry_error}", exc_info=True)
        #         dashboard_success = False
        
        return dashboard_success


# Singleton instance
orchestrator = PayrollOrchestrator()