{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6a64dd5a", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:35.439358Z", "start_time": "2025-05-20T20:45:34.690031Z"}}, "outputs": [], "source": ["import json\n", "\n", "import pandas as pd\n", "import requests"]}, {"cell_type": "code", "execution_count": 13, "id": "991ae094", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:37.564846Z", "start_time": "2025-05-20T20:45:35.969751Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Success!\n", "{\n", "  \"response\": {\n", "    \"input_state\": {\n", "      \"AgentVersion\": \"0.2.13\",\n", "      \"AgentReleaseDate\": \"2025-08-28\",\n", "      \"ingestId\": \"test-ingest-attachment2\",\n", "      \"displayId\": \"TT1478TT\",\n", "      \"companyID\": \"TT4UWBZQKEVRRWBWS9TT\",\n", "      \"uid\": \"1\",\n", "      \"id\": \"1000\",\n", "      \"timestamp\": \"2025-04-10T15:43:30.560000\",\n", "      \"CustomerDomain\": \"gmail.com\",\n", "      \"Attachments\": [\n", "        {\n", "          \"name\": \"\",\n", "          \"size\": 0,\n", "          \"content_type\": \"\",\n", "          \"content_hash\": \"\",\n", "          \"inline\": \"\",\n", "          \"attachment_reference\": \"\",\n", "          \"base64\": \"\"\n", "        }\n", "      ],\n", "      \"EmailContent\": \"Good morning We are just reporting hours for <PERSON><PERSON>ly test llc <PERSON> 38 Lejla  44 horus Irina 24 vacation hours Regular hours 19.75 total of 43.75 Thank you Please let us know you got this email. -- HR Team of Bubly Test LLC 123 Abc Rd Universe WA 00000\\n\\n        ---\\n\\n        **\\ud83d\\udcce ATTACHMENT: attachment_m4bup466.pdf**\\n        *File Type: .pdf | Size: 3,665,144 bytes*\\n\\n        -O O% t/1\\n\\nGOODAIR \\u2022\\nINC.\\n\\nEmployment Application  Dc,e,\\u201e\\n\\n'th2-3*\\n\\n3 Please complete this application by typing or printing in ink. INCOMPLETE or UNSIGNED applications will not be cons! eered.\\n3 We are an equal opportunity employer. We do not discriminate on the basis of race, religion, color, sex, age, national origin,\\n\\nmarital status, or disability.\\n\\n3 Do you need an accommodation to participate in the application or interview process?  (11 Yes\\n\\nEmployer: GOOD AIR INC.\\nDesired Pay  $  CO\\n\\nI PERSONAL DATA\\n\\nName  \\\\)\\\\CA-.O1\\n\\n12)063\\n\\nPresent Address\\n\\n1)\\n\\n7\\n'\\n\\n6 -ask0\\n-\\n\\nPhone Mb\\nDriver's License Number:    Z.6.\\nHave you ever been convicted of  crime?\\n\\nReferred by:\\n\\nlindeAdi\\n\\nJob Title'\\n\\nli)4.\\n\\n City\\n\\n)\\n\\n'an\\n\\nState\\n\\n  Email Address f\\n\\nL\\n\\n)\\n\\nZip 231\\n\\nMess3ge Phone (\\n\\nYes   IK   No\\n\\nIf yes, explain:\\n\\nEDUCATION.\\nHigh School Diploma or GED?  Iv/es  q  No\\n\\nPost-Secondary Degree?\\n\\nigh School   C, 1-21\\u2014 G0e\\n\\nName of school beyind\\nTraining Length   1.  eaV\\nMajor\\nApprenticeship Level  l4 VA.!_.\\nIWORK EXPERIENCE (List most recent mirk experience first)\\nCompany Name\\nComplete Address   ZIA)  flW\\n\\n\\u00a3L\\n\\n2\\\".27411\\nStreet/P.p. Box\\n\\nJob Title4\\n\\n)\\n\\n Date Completed    6\\u2014 I 0-'1,\\\" 5\\nMinor\\n\\n In which trade?   .4 u4(.\\n\\n14\\n\\nImmediate Supervisor\\n0k.K\\nCity\\nilk(ami,ork,6) (-0101)Z uvidi\\n\\nState\\n\\nz Zip C8x\\n\\nPhone\\n\\nJob Description (duties, skills, equipment used)\\n\\nDates:  From (inm/yyt\\n\\nI\\n\\nTo (nunlyy)OZ\\n\\n/\\n\\n  Reason for leaving\\n\\nEmployment Application (Rev. 09/2021)\\n\\n\\fiorida\\nc-;;,,,1DRIVER LICEN\\n\\n7r\\n\\n9\\n\\nse\\n\\nr,\\n\\nI FZT\\n\\nUSA\\n\\n!\\n\\nbur) 10/17/2001\\n1011712028\\n\\n4i9 i\\n\\nI/ kist NONE  \\\\\\u2018\\n4.01t)  NONE\\n', N0E fi 41\\n\\n1007/2022\\n\\nlti I Ito  8'.-01\\\"\\n\\n09/10/2018\\nTObill0050.416\\n\\nkij'i  ACLU 1010512021\\n\\n'Iii '* R256-872-01477-0\\n\\ni REYES MARTINEZ\\n/VICTOR LEONEL\\nB 12O0 SW 212TH ST\\n\\nMIAMI, FL *********\\n\\nRIVER\\n\\nOpenitstm ut 4 mates  vollicits\\ncabalists* consoni to uiy\\nwhitely tatut bequbsid by taw\\n\\n\\fCl\\n\\n\\u2022%\\\\\\u201811/4:\\n\\n?MR%\\n\\nYOU\\n\\n'Mlm!\\n\\n,rn\\\\\\\\\\n\\nS rr\\n\\n;Z:,\\\\If/ ...: \\u2022 \\u2022\\u2022\\n\\n....\\\"\\n\\nVAIN:\\n\\n;111\\\\0\\n\\nFRI1\\n\\n/111%\\n\\nSPrurrrs( rAp\\n\\n\\u2022  1,k,\\n\\n4\\n\\nportant\\n\\nline.\\n\\nADULTS: Sign this card in ink immediately.\\nCHILDREN: Do not sign until age 18 or your first job,\\nwhichever is earlier.\\n\\nKeep your card in a safe place to prevent loss or theft.\\nDO NOT CARRY THIS CARD WITH YOU.\\nDo not laminate.\\n\\n0.01110A MULL\\n\\n..  MAIM. SOC.. INCUIMTV\\n\\nO SIAM 0  AWN.>\\n\\nAO\\n\\nIt.\\n\\nOP\\n\\nECIAfratrjriv\\n\\nzita:k.- \\u2022\\n\\n1_5iFT\\n\\n*rti\\n.TA  L SHED FOR\\n=---\\n\\nif Vete\\n\\nt7 18 fir  Ari\\n\\n_THIS NWT\\n\\n\\u2022\\n\\n1 II\\n\\nI\\n*0 IIIVIV Oal  n NO..  cm. or A inin ose lebo\\u2022 VONIIMADealvls aaaNn\\n\\n11111 1111!  1111i1\\n\\nhIp;',)\\n\\n1111111\\n\\nI\\n\\nk\\n\\nfal.\\n\\nW\\n\\nHhill)Hih)H,11)HIIPI IIIOI I .Hh ; I:,'H: l\\n\\nr,,iW;(110OHHIll InPl,\\n\\nWiefinfilvroovvoilinw ao Mtge 01.\\u2022ln 10044NAMana\\u2022 AUWKINI\\n\\n  Nad\\n\\n=t,\\n:AtiGNATERe.::2:1-  \\u2022'\\u2022 \\u2022\\u2022 \\u2022\\n\\n7,71:7;1,\\n\\n:51\\n\\n1.2027\\n\\n\\fM 4\\n\\nForm  W\\n\\nDepartment of the Treasury\\nInternal Revenue Service\\n\\nEmployee's Withholding Certificate\\nComplete Form W-4 so that your employer can withhold the correct federal income tax from your pay.\\nGive Form W-4 to your employer.\\nYour withholding is subject to review by the IRS.\\n\\nOMB No. 1545-0074\\n\\n2,025\\n\\nStep 1:\\n\\nEnter\\nPersonal\\nInformation\\n\\nk(  O\\n\\n(a)  First na  e and middle initial\\n\\nL\\niZcL5-05u)  L\\\\Z\\nCity or town, state, and ZIP code\\n\\nAddress\\n\\nL  2)31-14-\\n(c)  LA Single or Married filing separately\\n\\nL\\n\\nname ei\\n\\n(b)  Social security number\\n\\nDoes your name match the\\nname on your social security\\ncard? If not, to ensure you get\\ncredit for your earnings,\\ncontact SSA at ************\\nor go to www.ssagov.\\n\\nq  Married filing jointly or Qualifying surviving spouse\\nEl Head of household (Check only If you're unmarried and pay more than half the costs of keeping up a home for yourself and a qualifying individual,)'\\n\\nTIP: Consider using the estimator at www.irs.gov/W4App to determine the most accurate withholding for the rest of the year If: you\\nare completing this form after the beginning of the year; expect to work only part of the year; or have changes during the year in your\\nmarital status, number of jobs for you (and/or your spouse if married filing jointly), dependents, other income (not from jobs),\\ndeductions, or credits. Have your most recent pay stub(s) from this year available when using the estimator. At the beginning of next\\nyear, use the estimator again to recheck your withholding.\\n\\nComplete Steps 2-4 ONLY if they apply to you; otherwise, skip to Step 5. See page 2 for more information on each step, who can\\nclaim exemption from withholding, and when to use the estimator at www.irs.gov/W4App.\\n\\nStep 2:\\n\\nMultiple Jobs\\nor Spouse\\nWorks\\n\\nComplete this step if you (1) hold more than one job at a time, or (2) are married filing jointly and your spouse\\nalso works. The correct amount of withholding depends on income earned from all of these jobs.\\nDo only one of the following.\\n(a)  Use the estimator at www.irs.gov/W4App for the most accurate withholding for this step (and Steps 3-4). If\\n\\nyou or your spouse have self-employment income, use this option; or\\n\\n(b) Use the Multiple Jobs Worksheet on page 3 and enter the result in Step 4(c) below; or\\n(c) If there are only two jobs total, you may check this box. Do the same on Form W-4 for the other job. This\\noption is generally more accurate than (b) if pay at the lower paying job is more than half of the pay at the\\nhigher paying job. Otherwise, (b) is more accurate\\n\\nq\\n\\nComplete Steps 3-4(b) on Form W-4 for only ONE of these jobs. Leave those steps blank for the other jobs. (Your withholding will\\nbe most accurate if you complete Steps 3-4(b) on the Form W-4 for the highest paying job.)\\n\\nStep 3:\\n\\nClaim\\nDependent\\nand Other\\nCredits\\n\\nStep 4\\n(optional):\\n\\nOther\\nAdjustments\\n\\nIf your total income will be $200,000 or less ($400,000 or less if married filing jointly):\\n\\nMultiply the number of qualifying children under age 17 by $2,000  $\\n\\nMultiply the number of other dependents by $500\\n\\nAdd the amounts above for qualifying children and other dependents. You may add to\\nthis the amount of any other credits. Enter the total here\\n\\n3\\n\\n(a)  Other  income  (not  from  jobs).  If  you  want  tax  withheld  for  other  income  you\\nexpect this year that won't have withholding, enter the amount of other income here.\\nThis may include Interest, dividends, and retirement income\\n\\n(b) Deductions. If you expect to claim deductions other than the standard deduction and\\nwant to reduce your withholding, use the Deductions Worksheet on page 3 and enter\\nthe result here\\n\\n(c) Extra withholding. Enter any additional tax you want withheld each pay period  .  .\\n\\n4(a)\\n\\n4(b)\\n\\n4(c)\\n\\nStep 5:\\n\\nSign\\nHere\\n\\nUnder penalties of perjury, I declare that this certificate, to the best of my knowledge and belief, is true, correct, and complete.\\n\\nEmploye  's signature (This form is not valid unless you sign it.)\\n\\nEmployers\\nOnly\\n\\nEmployer's name and address\\n\\n  4-et-Z-5\\n\\nDate\\n\\nFirst date of\\nemployment\\n\\nEmployer identification\\nnumber (EIN)\\n\\nFor Privacy Act and Paperwork Reduction Act Notice, see page 3.\\n\\nCat. No, 10220Q\\n\\nForm W-'4 (2025)\\n\\n\\fGOODAIR\\n.\\n(,./ Please complete this application by typing or printing in Ink. INCOMPLETE or UNSIGNED applications will not be considered.\\n\\nEmployment Application  % el\\n\\nv  We are an equal opportunity employer. We do not discriminate on the basis of race, religion, color, sex, age, national origin,\\n\\nmarital status, or disability.\\n\\nV  Do you need an accommodation to participate in the application or intervievy process?  0  Yes  0  No\\n\\u2022  4.\\n\\nEmployer: GOOD AIR INC.\\n\\nReferred by:\\n\\nDesired Pay  $\\n\\nIPERSONAL, DATA\\n\\nName\\n\\nPresent Address\\n\\nJob Title\\n\\nCity\\n\\n)\\n\\nState\\n\\nZip\\n\\n  E-mail Address (\\n\\n)\\n\\nPhone  (\\n)\\nDriver's License Number:\\nHave you ever been convicted of a crime?\\n\\n  Message Phone (\\n\\nYes   -\\\"-\\u2022\\n\\n No\\n\\nIf yes, explain:\\n\\nEDUCATION\\nHigh School Diploma or GED?\\n\\n/ Yes\\n\\nNo\\n\\nO\\n\\nO\\n\\nPost-Secondary Degree?\\n\\nName of school beyond High School  _\\nTraining Length\\nMajor\\nApprenticeship Level\\n\\nDate Completed\\nMinor\\n\\nIn which trade?\\n\\nWORK EXPERIENCE. (List most recent work eXperienae,first)\\nCompany Name\\nComplete Address\\n\\nImmediate Supervisor\\n\\nk e i,'r,,\\n\\nJob Title\\n\\nJob Description (duties, skills, equipment used)\\n\\nPhone\\n\\nStreet/P.O. Box\\n\\nCity\\n\\nState\\n\\nZip Code\\n\\nDates:  From (nrvm\\n\\nTo (mmyy)\\n\\n  Reason for leaving\\n\\nEmployment Application (Rev. 09/2021)\\n\\n\\fFlorida\\n\\nDRIVER LICENSE\\n\\nUSA\\n\\nisICLNT525 - 100 - 80417\\n\\n- 0 9-\\n\\nTOMASONI\\n2CHRISTIAN\\n13184 NW 102ND PL\\nMIAMI, FL 33172\\n,use  01/17/1980 1516  M\\nHMCo  **********  isoo7  5*-06\\\"\\n'i  REST NONE=\\n\\nle Esc NONE\\n\\nSAFE DRIVER\\n\\n1211812022\\n\\n5\\n\\nX632212100413\\n\\nOporatton of a motor ruck  corsototes\\nconsent to arty sobnery asst rociutrect Oy law\\n\\nt2\\n\\n\\u2022\\n\\n4\\n\\n1, A\\n\\n\\fForm W-4\\n\\nDepartment of the Treasury\\nInternal Revenue Service\\n\\nEmployee's Withholding Certificate\\nComplete Form W-4 so that your employer can withhold the correct federal income tax from your pay,\\nGive Form W-4 to your employer.\\nYour withholding is subject to review by the IRS.\\n\\nOMB No. 1545-0074\\n\\n2,025\\n\\nStep 1:\\n\\nEnter\\nPersonal\\nInformation\\n\\n(a)  First name and middle initial\\nC  V\\\\S-A-1\\n\\u2018C\\\\\\n\\nAddress\\n\\nCity or town, state, and ZIP code\\n\\nLast name\\n\\n\\\\\\n\\n90A4'\\n\\n*105\\n\\nt i Vk  \\u00b0MIA\\\\\\n\\nS\\n\\n\\\\ ore\\n\\ns\\n\\n(c)  KSingle or Married filing separately\\n\\nr\\n\\nR\\n\\n(b)  Social security number\\na 7-124\\n5:89 -\\nDoes your name match the\\nname on your social security\\ncard? If not, to ensure you get\\ncredit for your earnings,\\ncontact SSA at ************\\nor go to www,ssa.gov.\\n\\nEl Married filing jointly or Qualifying surviving spouse\\nq  Head of household (Check only if you're unmarried and pay more than half the costs of keeping up a home for yourself and a qualifying Individual.)\\n\\nTIP: Consider using the estimator at www.irs.gov/W4App to determine the most accurate withholding for the rest of the year if: you\\nare completing this form after the beginning of the year; expect to work only part of the year; or have changes during the year in your\\nmarital status, number of jobs for you (and/or your spouse if married filing jointly), dependents, other income (not from jobs),\\ndeductions, or credits. Have your most recent pay stub(s) from this year available when using the estimator. At the beginning of next\\nyear, use the estimator again to recheck your withholding.\\nComplete Steps 2-4 ONLY if they apply to you; otherwise, skip to Step 5. See page 2 for more Information on each step, who can\\nclaim exemption from withholding, and when to use the estimator at www.irs.gov/W4App.\\n\\nStep 2:\\n\\nMultiple Jobs\\nor Spouse\\nWorks\\n\\nComplete this step if you (1) hold more than one job at a time, or (2) are married filing jointly and your spouse\\nalso works. The correct amount of withholding depends on income earned from all of these jobs.\\nDo only one of the following.\\n(a)  Use the estimator at www.irs.gov/W4App for the most accurate withholding for this step (and Steps 3-4). If\\n\\nyou or your spouse have self-employment income, use this option; or\\n\\n(b) Use the Multiple Jobs Worksheet on page 3 and enter the result In Step 4(c) below; or\\n(c) If there are only two jobs total, you may check this box. Do the same on Form W-4 for the other job. This\\noption is generally more accurate than (b) if pay at the lower paying job is more than half of the pay at the\\nhigher paying job. Otherwise, (b) Is more accurate\\n\\nq\\n\\nComplete Steps 3-4(b) on Form W-4 for only ONE of these jobs. Leave those steps blank for the other jobs. (Your withholding will\\nbe most accurate if you complete Steps 3-4(b) on the Form W-4 for the highest paying job.)\\n\\nStep 3:\\n\\nClaim\\nDependent\\nand Other\\nCredits\\n\\nStep 4\\n(optional):\\n\\nOther\\nAdjustments\\n\\nIf your total income will be $200,000 or less ($400,000 or less if married filing jointly):\\n\\nMultiply the number of qualifying children under age 17 by $2,000  $\\n\\nMultiply the number of other dependents by $500\\n\\nAdd the amounts above for qualifying children and other dependents. You may add to\\nthis the amount of any other credits. Enter the total here\\n(a)  Other  income  (not  from  jobs).  If  you  want  tax  withheld  for  other  income  you\\nexpect this year that won't have withholding, enter the amount of other income here.\\nThis may include interest, dividends, and retirement income\\n\\n3\\n\\n4(a)\\n\\n(b) Deductions. If you expect to claim deductions other than the standard deduction and\\nwant to reduce your withholding, use the Deductions Worksheet on page 3 and enter\\nthe result here\\n\\n4(b)\\n\\n(c) Extra withholding. Enter any additional tax you want withheld each pay period .  .\\n\\n4(c)\\n\\nUnder penalties of perjury, I declare that this certificate, to the best of my knowledge and belief, Is true, correct, -nd complete.\\n\\nStep 5:\\n\\nSign\\nHere\\n\\nmployee's signature (This form is not valid unless you sign it.)\\n\\nDate\\n\\nEmployers\\nOnly\\n\\nEmployer's name and address\\n\\nFirst date of\\nemployment\\n\\nEmployer Identification\\nnumber (EIN)\\n\\nFor Privacy Act and Paperwork Reduction Act Notice, see page 3.\\n\\nCat. No, 10220Q\\n\\nForm W-4 (2025)\\n\\n\\fTHIS  N\\n\\nHAS\\n\\nSTABL1\\n\\nR\\n\\nCfiR\\n\\nTURE\\n\\ni I;siiiii:irliiili!i\\n\\niit ztit,t ;. ii. li z\\n\\n.t..\\n\\n'1!.,\\n\\n',:, .: ' .  : .  \\u2022\\n\\u2022 ,\\n\\n.\\n\\n\\u2022\\n\\ntt\\u2022\\n\\na\\n\\n\\fGOODAINC.\\n\\n  Employment Application\\n\\nPlease complete this application by typing or printing In Ink. INCOMPLETE or UNSIGNED applications will not be considered:\\\\\\n\\nmarital status, or disability.\\n\\nv.  We are an equal opportunity employer. We do not discriminate on the basis of race, religion, color, sex, age, national origin,\\nV  Do you need an accommodation to participate in the application or interview process?  O Yes in No\\n\\nEmployer: GOOD AIR INC.\\n\\nDesired Pay   $\\n\\nPERSONAL, DATA\\n\\nName\\n\\nPresent Address'\\n\\nReferred by:\\n\\nJob Title\\n\\n114\\n\\nCity\\n\\nState\\n\\nZip\\n\\nE-mail Address (\\n\\n)\\n\\nPhone  (\\n)\\nDriver's License Number:\\nHave you ever been convicted of a crime?\\n\\nMessage Phone\\n\\nYes\\n\\nNo\\n\\nIf yes, explain:\\n\\nEnuckrintsi\\nHigh School Diploma or GED?\\n\\nYes\\n\\nNo\\n\\nPost-Secondary Degree?\\n\\nName of school beyond High School\\nTraining Length\\nMajor\\nApprenticeship Level\\n\\nDate Completed\\nMinor\\n\\nIn which trade?\\n\\nWORK EXPERIENCE (List most recent work experience first)\\nCompany Name\\nComplete Address\\n\\nStreet/P.O. Box\\n\\nJob Title\\n\\nJob Description (duties, skills, equipment used)\\n\\nImmediate Supervisor\\n\\nCity\\n\\nPhone    (\\n\\nState\\n)\\n\\nZip Code\\n\\nDates:  From (mnvyy)\\n\\n1 6\\n\\n1\\nI\\n\\nTo (mrniyy)\\n\\nReason for leaving\\n\\nEmployment Application (Rev. 09/2021)\\n\\n\\fForm W-4\\n\\nDepartment of the Treasury\\nInternal Revenue Service\\n\\nEmployee's Withholding Certificate\\nComplete Form W-4 so that your employer can withhold the correct federal income tax from your pay.\\nGive Form W-4 to your employer.\\nYour withholding Is subject to review by the IRS.\\n\\nOMB No, 1545-0074\\n\\n2025\\n\\n(a)  First name and middle Initial\\n\\nLast name\\n\\n(b)  Social security number\\n\\nStep 1:\\n\\nEnter\\nPersonal\\nInformation\\n\\nAddress\\n\\nCity or town, state, and ZIP code\\n\\nDoes your name match the\\nname on your social security\\ncard? If not, to ensure you get\\ncredit for your earnings,\\ncontact SSA at ************\\nor go to www,ssa,gov,\\n\\n(C)\\n\\nq  Single or Married filing separately\\nq  Married filing Jointly or Qualifying surviving spouse\\nq  Head of household (Check only If you're unmarried and pay more than half the costs of keeping up a home for yourself and a qualifying Individual.)\\n\\nTIP: Consider using the estimator at www.Irs.gov/W4App to determine the most accurate withholding for the rest of the year If: you\\nare completing this form after the beginning of the year; expect to work only part of the year; or have changes during the year in your\\nmarital status, number of Jobs for you (and/or your spouse If married filing Jointly), dependents, other Income (not from Jobs),\\ndeductions, or credits. Have your most recent pay stub(s) from this year available when using the estimator. At the beginning of next\\nyear, use the estimator again to recheck your withholding.\\n\\nComplete Steps 2-4 ONLY if they apply to you; otherwise, skip to Step 5. See page 2 for more Information on each step, who can\\nclaim exemption from withholding, and when to use the estimator at www,Irs.gov/W4App.\\n\\nStep 2:\\n\\nMultiple Jobs\\nor Spouse\\nWorks\\n\\nComplete this step If you (1) hold more than one Job at a time, or (2) are married filing Jointly and your spouse\\nalso works. The correct amount of withholding depends on income earned from all of these Jobs,\\nDo only one of the following,\\n(a)  Use the estimator at www.Irs.gov/W4App for the most accurate withholding for this step (and Steps 3-4). If\\n\\nyou or your spouse have self-employment income, use this option; or\\n\\n(b) Use the Multiple Jobs Worksheet on page 3 and enter the result In Step 4(c) below; or\\n(c) If there are only two Jobs total, you may check this box. Do the same on Form W-4 for the other Job. This\\noption Is generally more accurate than (b) If pay at the lower paying Job is more than half of the pay at the\\nhigher paying Job. Otherwise, (b) is more accurate\\n\\nq\\n\\nComplete Steps 3-4(b) on Form W-4 for only ONE of these Jobs. Leave those steps blank for the other Jobs, (Your withholding will\\nbe most accurate if you complete Steps 3-4(b) on the Form W-4 for the highest paying Job.)\\n\\nStep 3:\\n\\nClaim\\nDependent\\nand Other\\nCredits\\n\\nStep 4\\n(optional):\\n\\nOther\\nAdjustments\\n\\nIf your total income will be $200,000 or less ($400,000 or less If married filing Jointly);\\n\\nMultiply the number of qualifying children under age 17 by $2,000  $\\n\\nMultiply the number of other dependents by $500\\n\\nAdd the amounts above for qualifying children and other dependents. You may add to\\nthis the amount of any other credits, Enter the total here\\n(a)  Other  income  (not  from  jobs).  If  you  want  tax  withheld  for  other  Income  you\\nexpect this year that won't have withholding, enter the amount of other Income here.\\nThis may include Interest, dividends, and retirement Income\\n\\n(b) Deductions. If you expect to claim deductions other than the standard deduction and\\nwant to reduce your withholding, use the Deductions Worksheet  on page 3 and enter\\nthe result here\\n\\n(c) Extra withholding. Enter any additional tax you want withheld each pay period\\n\\n3\\n\\n4(a)\\n\\n4(b)\\n\\n4(c)\\n\\nUnder penalties of perjury, I deotare that this certificate, to the best of my knowledge and belief, is true, correct, and complete.\\n\\nStep 5:\\n\\nSign\\nHere\\n\\n\\u2022  Employee's signature (This form Is not valid unless you sign It.)\\n\\nDate\\n\\nEmployers\\nOnly\\n\\nEmployer's name and address\\n\\nFirst date of\\nemployment\\n\\nEmployer Identification\\nnumber (EIN)\\n\\nFor Privacy Act and Paperwork Reduction Act Notice, see page 3,\\n\\ncat, No, 10220Q\\n\\nForm W-4 (2025)\\n\\n\\fFloridt2\\n\\n\\u2022\\n\\nDRIVER  LICENSE\\n*L121 -540-69402-0\\n\\n.\\n\\n./4\\n\\nILOPEZ BOLIZO\\na MARILYN\\n68079 W 36TH AVE 7\\nHIALEAH, FL 33018.1606\\n3  DOB  06102/1969 15s.E)  F\\n4b EXP  06/0212028\\n12 REST NONE'  9a ENE'  NONE\\n\\n5%03\\\"\\n\\nIrooNce\\n\\nSAFE DRIVER\\n\\n,ss 03/10/2020\\n\\n5  c x$**********.\\n\\n4-ACED  *********\\n\\nOperation of a Motor veltiedle consittotes\\nconsent to any setae* bin aetporeill by 'acre\\n\\nji\\u2022A\\\\ I\\n\\nI le\\n\\n. 1 1 ,\\n\\n'\\n\\n\\f'Wale.... SPOIL INOUnny AmAxannyvot. Wino\\n\\nOr AMenor.4110e..1. 'MOWRY A01.41allTn/MONUIRRO IITAMI OP P.WRICA BOOMIffOURMYAOYwDRRAlIONIM11f0 OARS OP HANG\\n\\nsic cit.]\\n\\nTHIS NUM,\\n\\nitC,SEIfihr\\n\\nSTA  HED FOII \\u2022\\n\\n71-al  -S- ITP.T:-\\u2022\\n\\nIM\\u2022\\n\\n\\\"\\n\\nat\\n\\n- *0 run.  OWIMNOMYWWRNOY AlRNDlf wmo.\\n\\nMD museum.\\n\\nfGNATill\\n\\n- Ek7\\n\\ni-;\\n\\n\\u2022\\n\\n\\u2022\\nL-.\\n\\ntt,:.\\u2022at\\n\\na\\nII\\n\\ndll llllli ,;  I I :,r; ;II.\\n\\nmane TIMIS VMUWW q  laws II ailmi  NOLL.UICM,pov AOvll>3>TIDdi\\n\\n\\fGooDAw* Employment Application\\n\\n7/  Please complete this application by typing or printing In Ink. INCOMPLETE or UNSIGNED applications will not be considered.\\n3 We are an equal opportunity employer. We do not discriminate on the basis of race, religion, color, sex, age, national origin,\\n\\nmarital status, or disability.\\n\\nV  Do you need an accommodation to participate in the application or interview process?  O Yes  O No\\n\\nEmployer: GOOD AIR INC.\\n\\nDesired Pay  $  V\\n\\n1/4t)\\n\\nPERSONAL, DATA\\n\\nName\\n\\nPresent Address\\n\\nReferred by:\\n\\nJob Title\\n\\nCity\\n\\n)\\n\\nState\\n\\nZip\\n\\nE-mail Address\\n\\nPhone  (\\n)\\nDriver's License Number:\\nHave you ever been convicted of a crime?\\n\\nMessage Phone (\\n\\nYes\\n\\nNo\\n\\nIf yes, explain:\\n\\nEDUCATION\\nHigh School Diploma or GED?\\n\\nYes\\n\\nNo\\n\\nPost-Secondary Degree?\\n\\nName of school beyond High School\\nTraining Length\\nMajor\\nApprenticeship Level\\n\\nDate Completed\\nMinor\\n\\nIn which trade?\\n\\nI WORK EXPERIENCE (List most recent work eXperienCe first)\\nCompany Name\\nComplete Address\\n\\nStreet/P.O. Box\\n\\nImmediate Supervisor\\n\\ni I\\nCity\\n\\nJob Title\\n\\nJob Description (duties, skills, equipment used)\\n\\nState\\n\\nPhone    (\\n\\nZip Code\\n-\\n\\nDates:  From (navyy)\\n\\nI\\n\\nTo (mm/yy)\\n\\n  Reason for leaving\\n\\nEmployment Application (Rev. 09/2021)\\n\\n\\f\\f\\u2022\\n\\n\\u2022\\n\\n\\\".ux,A1\\u2022;1.\\ni\\\\  1\\nurfiA\\n\\npit N.\\n\\n--\\n\\nIon a\\n\\nJ1\\n'J\\n-St-1\\n\\n0\\n)\\n\\n\\u2022\\n\\n1.H619-239.234300-0\\n\\n\\\\'\\n\\nHERNANDEZ. MARTINEZ\\n:OSIEL\\n1.,13(183 SUN RD\\nBROOKSVILLE. FL 341.1J'\\n0511511997  ),  s  M\\n4bEXP  05115420,33  1.6.G:-  5%10\\\"\\n,2Res,  EI:\\nSAFE DRIVER:\\nta\\n\\nNONE\\n\\nrz\\n\\n03117/2025\\nLat25O31\\n\\naverancr  **low\\n:o.nseritt to\\n\\nsobpity\\n\\nrop;strtutoi,\\nt rimiAreer tr.  ' aw\\n\\nUNITED  STATES  OF  AMERICA\\nERMANENT RESIDENT\\n\\nV11103\\n\\n15\\n\\nSurname\\nHERNANDEZ MARTINE#-,\\nGiven Name\\nOSIEL\\nCouliry of Br\\nba\\nuscis#\\n\\nA  n\\n\\nnt%\\n\\nTM  246-164.861  MI6\\n\\nte of Birth\\nSex\\nMAY 1997  M\\n\\nCard Expires:  Resident Since:\\n07/30/34  11/09122\\n\\n\\fForm W-4\\n\\nDepartment of the Treasury\\nInternal Revenue Service\\n\\nEmployee's Withholding Certificate\\nComplete Form W-4 so that your employer can withhold the correct federal income tax from your pay.\\nGive Form W-4 to your employer.\\nYour withholding is subject to review by the IRS.\\n\\nOMB No. 1545-0074\\n\\n2025\\n\\n(a)  First name and middle initial\\n\\nLast name\\n\\n(b)  Social security number\\n\\nStep 1:\\n\\nEnter\\nPersonal\\nInformation\\n\\nAddress\\n\\nCity or town, state, and ZIP code\\n\\nDoes your name match the\\nname on your social security\\ncard? If not, to ensure you get\\ncredit for your earnings,\\ncontact SSA at ************\\nor go to www.ssa.gov.\\n\\n(c)\\n\\nq  Single or Married filing separately\\nO  Married filing jointly or Qualifying surviving spouse\\nq  Head of household (Check only if you're unmarried and pay more than half the costs of keeping up a home for yourself and a qualifying Individual:)'\\n\\nTIP: Consider using the estimator at www.irs.gov/W4App to determine the most accurate withholding for the rest of the year if: you\\nare completing this form after the beginning of the year; expect to work only part of the year; or have changes during the year in your\\nmarital status, number of jobs for you (and/or your spouse if married filing jointly), dependents, other income (not from jobs),\\ndeductions, or credits. Have your most recent pay stub(s) from this year available when using the estimator. At the beginning of next\\nyear, use the estimator again to recheck your withholding.\\n\\nComplete Steps 2-4 ONLY if they apply to you; otherwise, skip to Step 5. See page 2 for more Information on each step, who can\\nclaim exemption from withholding, and when to use the estimator at www.irs.gov/W4App.\\n\\nStep 2:\\n\\nMultiple Jobs\\nor Spouse\\nWorks\\n\\nComplete this step if you (1) hold more than one job at a time, or (2) are married filing jointly and your spouse\\nalso works. The correct amount of withholding depends on income earned from all of these jobs.\\n\\nDo only one of the following.\\n(a)  Use the estimator at www.irs.gov/W4App for the most accurate withholding for this step (and Steps 3-4). If\\n\\nyou or your spouse have self-employment income, use this option; or\\n\\n(b) Use the Multiple Jobs Worksheet on page 3 and enter the result in Step 4(c) below; or\\n(c) If there are only two jobs total, you may check this box. Do the same on Form W-4 for the other job. This\\noption is generally more accurate than (b) if pay at the lower paying job is more than half of the pay at the\\nhigher paying Job. Otherwise, (b) is more accurate\\n\\nq\\n\\nComplete Steps 3-4(b) on Form W-4 for only ONE of these jobs. Leave those steps blank for the other jobs. (Your withholding will\\nbe most accurate if you complete Steps 3-4(b) on the Form W-4 for the highest paying job.)\\n\\nStep 3:\\n\\nClaim\\nDependent\\nand Other\\nCredits\\n\\nStep 4\\n(optional):\\n\\nOther\\nAdjustments\\n\\nIf your total income will be $200,000 or less ($400,000 or less if married filing jointly):\\n\\nMultiply the number of qualifying children under age 17 by $2,000  $\\n\\nMultiply the number of other dependents by $500\\n\\nAdd the amounts above for qualifying children and other dependents. You may add to\\nthis the amount of any other credits. Enter the total here\\n(a)  Other  income  (not  from  jobs).  If  you  want  tax  withheld  for  other  income  you\\nexpect this year that won't have withholding, enter the amount of other income here.\\nThis may include Interest, dividends, and retirement income\\n\\n(b) Deductions. If you expect to claim deductions other than the standard deduction and\\nwant to reduce your withholding, use the Deductions Worksheet on page 3 and enter\\nthe result here\\n\\n3  $\\n\\n4(a)\\n\\n4(b)\\n\\n(c) Extra withholding. Enter any additional tax you want withheld each pay period .  .\\n\\n4(c)\\n\\nStep 5:\\n\\nSign\\nHere\\n\\nEmployers\\nOnly\\n\\nUnder penalties of perjury, I declare that this certificate, to the best of my knowledge and belief, is true, correct, and complete.\\n\\nEmployee's signature (This form is not valid unless you sign it.)\\n\\nDate\\n\\nEmployer's name and address  (wi\\n\\not \\u2018f)\\\\C\\\\  ?) \\\"-,5tY3-, 3\\n\\nFirst date of\\nemployment\\n\\nEmployer identification\\nnumber (EIN)\\n\\noui \\\\61-115-r\\n\\nFor Privacy Act and Paperwork Reduction Act Notice, see page 3.\\n\\nCat. No, 10220Q\\n\\nForm W-4 (2025)\\n\\n\\n\\n        ---\\n        \",\n", "      \"SourceAddress\": \"<EMAIL>\",\n", "      \"DestinationAddress\": [\n", "        \"<EMAIL>\"\n", "      ],\n", "      \"Subject\": \"Bubly test staff payroll\",\n", "      \"received_at\": \"2025-08-28 22:56:49\"\n", "    },\n", "    \"classification_output_state\": {\n", "      \"intent_triage_metadata\": {\n", "        \"isWorkRelated\": 95,\n", "        \"isWorkRelated_reasoning\": \"Email contains employee hours reporting for payroll processing.\",\n", "        \"RequiresWork\": 100,\n", "        \"RequiresWork_reasoning\": \"Employee hours data must be entered and confirmed in the system.\",\n", "        \"isAboutPayroll\": 100,\n", "        \"isAboutPayroll_reasoning\": \"Explicitly reporting hours for payroll.\",\n", "        \"EnterPayroll\": 100,\n", "        \"EnterPayroll_reasoning\": \"Provides specific employee hours that need to be entered.\"\n", "      },\n", "      \"intent_triage_passes_flag\": true,\n", "      \"intent_triage_passes_fail_reasons\": [],\n", "      \"intent_triage_content_policy_violation\": false,\n", "      \"business_knockout_metadata\": {\n", "        \"PEO_confidence\": 0,\n", "        \"UPDATE_EE_DIRECT_DEPOSIT_confidence\": 0,\n", "        \"UPDATE_EE_SSN_confidence\": 0,\n", "        \"CHECK_DATE_LT_1_BUSINESS_DAY_confidence\": 0,\n", "        \"CREATE_OFF_CYCLE_PAY_PERIOD_confidence\": 0,\n", "        \"START_OR_RESUME_BACK_DATED_PAY_PERIOD_confidence\": 0,\n", "        \"ADD_WORKER_confidence\": 0,\n", "        \"TERMINATE_WORKER_confidence\": 0,\n", "        \"UPDATE_CLIENT_OR_EE_PAY_COMPONENTS_confidence\": 0,\n", "        \"UPDATE_EE_RATE_OF_PAY_confidence\": 0,\n", "        \"ADDING_MULTIPLE_CHECKS_TO_AN_EE_confidence\": 0,\n", "        \"UPDATE_CLIENT_LEVEL_TAXES_confidence\": 0,\n", "        \"UPDATE_EE_LEVEL_TAXES_confidence\": 0,\n", "        \"ONE_OFF_TAX_CHANGES_confidence\": 0,\n", "        \"CANCEL_DELETE_PAY_PERIODS_confidence\": 0,\n", "        \"UPDATE_EE_DEMO_ADDRESS_PHONE_EMAIL_confidence\": 0,\n", "        \"UPDATE_EE_POSITION_confidence\": 0,\n", "        \"UPDATE_EE_WITHHOLDINGS_confidence\": 0,\n", "        \"MANUAL_CHECKS_confidence\": 0,\n", "        \"GROSS_TO_NET_CHECKS_confidence\": 0,\n", "        \"NET_TO_GROSS_CHECKS_confidence\": 0,\n", "        \"THIRD_PARTY_SICK_PAY_CHECKS_confidence\": 0,\n", "        \"CHECKS_USING_SYSTEM_TEMPLATES_confidence\": 0,\n", "        \"ADD_MESSAGES_TO_CHECK_STUBS_confidence\": 0,\n", "        \"CHANGES_TO_PACKAGE_AND_DELIVERY_FOR_PAYROLLS_confidence\": 0,\n", "        \"BILLING_OVERRIDES_confidence\": 0,\n", "        \"CHANGE_CHECK_TEMPLATES_FOR_NEW_CHECKS_confidence\": 0,\n", "        \"BLOCK_UNBLOCK_DD_OF_NET_PAY_confidence\": 0,\n", "        \"CHANGES_UPDATES_TO_LABOR_DIST_JOB_COSTING_confidence\": 0,\n", "        \"VENDOR_CHECKS_confidence\": 0,\n", "        \"CHECK_PTO_BALANCE_confidence\": 0,\n", "        \"CALL_REQUESTED_confidence\": 0,\n", "        \"DetectedKnockoutRules\": []\n", "      },\n", "      \"business_knockout_passes_flag\": true,\n", "      \"business_knockout_passes_fail_reasons\": [],\n", "      \"technical_knockout_metadata\": {\n", "        \"IS_THREAD_confidence\": 10,\n", "        \"REPLY_TO_COURTESY_REMINDER_EMAIL_confidence\": 10,\n", "        \"RECURRING_THREAD_confidence\": 10,\n", "        \"REQUESTS_PRE_PROCESSING_CONFIRMATION_confidence\": 10,\n", "        \"MULTI_ID_REQUEST_confidence\": 10,\n", "        \"TECHNICAL_FRAUD_confidence\": 10,\n", "        \"DetectedKnockoutRules\": []\n", "      },\n", "      \"technical_knockout_passes_flag\": true,\n", "      \"technical_knockout_passes_fail_reasons\": [],\n", "      \"request_type_metadata\": {\n", "        \"RequestType\": \"custom\",\n", "        \"StandaloneRequest\": \"standalone\",\n", "        \"PayrollFollowUp\": \"no\",\n", "        \"NonPayrollFollowUp\": \"no\",\n", "        \"EmployeeCount\": 3,\n", "        \"EmailIsFromEmployee\": \"no\",\n", "        \"NumberOfPayPeriods\": 1,\n", "        \"CallRequested\": \"no\",\n", "        \"PayHoursOnly\": 2,\n", "        \"PayHoursWithPayRate\": 0,\n", "        \"PayHoursWithPayRateId\": 0,\n", "        \"PayUnitsOnly\": 0,\n", "        \"PayUnitsWithPayRate\": 0,\n", "        \"PayUnitsWithPayRateId\": 0,\n", "        \"PayAmountOnly\": 0,\n", "        \"BonusPayments\": 0,\n", "        \"Tips\": 0,\n", "        \"OverrideStandardHourlyRate\": 0,\n", "        \"OverrideStandardSalary\": 0,\n", "        \"Overtime\": 0,\n", "        \"VacationTime\": 1,\n", "        \"SickTime\": 0,\n", "        \"OtherEarningsOrHours\": 0,\n", "        \"WhatWereTheOtherEntries\": \"\",\n", "        \"InformationFormat\": \"embedded_rows\",\n", "        \"AttachmentType\": \"none\",\n", "        \"EmailLanguage\": \"english\",\n", "        \"PotentialFraud\": \"none\",\n", "        \"InsufficientInformation\": \"no\"\n", "      },\n", "      \"complexity_metadata\": {\n", "        \"ComplexityTag\": \"medium\",\n", "        \"ComplexityTagReason\": [\n", "          \"EmployeeCount=3 not in easy group\",\n", "          \"NumberOfPayPeriods=1 not in easy group\"\n", "        ]\n", "      },\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"classification_intent_triage\": 3.9404972040001667,\n", "        \"classification_intent_triage_router\": 0.0002109180004481459,\n", "        \"classification_technical_knockout_batched\": 6.669564974000423,\n", "        \"classification_business_knockout_batched\": 7.411699976999444,\n", "        \"classification_business_knockout_router\": 0.0009263450001526508,\n", "        \"classification_technical_knockout_router\": 0.0018312829997739755,\n", "        \"classification_request_type\": 10.395986989999983,\n", "        \"classification_complexity\": 0.0005934540004091104,\n", "        \"classification_finish_node\": 0.00019307100046717096\n", "      },\n", "      \"llm_usage\": {\n", "        \"classification_intent_triage\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 907,\n", "          \"output_tokens_estimated\": 82\n", "        },\n", "        \"classification_business_knockout_classification_batch_3\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 726,\n", "          \"output_tokens_estimated\": 40\n", "        },\n", "        \"classification_business_knockout_classification_batch_1\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 2857,\n", "          \"output_tokens_estimated\": 323\n", "        },\n", "        \"classification_technical_knockout_classification_batch_1\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 1543,\n", "          \"output_tokens_estimated\": 126\n", "        },\n", "        \"classification_business_knockout_classification_batch_2\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 2285,\n", "          \"output_tokens_estimated\": 351\n", "        },\n", "        \"classification_request_type\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 241,\n", "          \"output_tokens_estimated\": 166\n", "        }\n", "      }\n", "    },\n", "    \"company_worker_lookup_output_state\": {\n", "      \"sender_email_metadata\": {\n", "        \"sender_name\": \"HR Team of Bubly Test LLC\",\n", "        \"company_name\": \"Bubly test llc\",\n", "        \"sender_address\": \"123 Abc Rd Universe WA 00000\",\n", "        \"employees_names\": [\n", "          \"Elena\",\n", "          \"<PERSON><PERSON><PERSON>\",\n", "          \"<PERSON><PERSON>\"\n", "        ]\n", "      },\n", "      \"company_scores\": [\n", "        {\n", "          \"company_id\": \"TT4UWBZQKEVRRWBWS9TT\",\n", "          \"confidence\": 100,\n", "          \"reasoning\": \"Only one company found\"\n", "        }\n", "      ],\n", "      \"company_selection_reason\": \"Selected only company with confidence above 79: TT4UWBZQKEVRRWBWS9TT (100)\",\n", "      \"attachments_found\": true,\n", "      \"attachments_processed\": true,\n", "      \"attachment_handwritten_notes_found\": false,\n", "      \"employees_metadata\": [\n", "        \"Elena\",\n", "        \"<PERSON><PERSON><PERSON>\",\n", "        \"<PERSON><PERSON>\"\n", "      ],\n", "      \"workers_search_success\": false,\n", "      \"llm_workers_match\": {\n", "        \"workers\": [\n", "          {\n", "            \"extracted_name\": \"<PERSON>\",\n", "            \"closest_match\": \"<PERSON>\",\n", "            \"closest_match_confidence\": 100,\n", "            \"closest_match_successful\": true,\n", "            \"worker_name\": \"<PERSON>\",\n", "            \"worker_number\": \"TTM9LQF7M3F0S89L3ATT\"\n", "          },\n", "          {\n", "            \"extracted_name\": \"<PERSON><PERSON><PERSON>\",\n", "            \"closest_match\": \"<PERSON><PERSON><PERSON>\",\n", "            \"closest_match_confidence\": 100,\n", "            \"closest_match_successful\": true,\n", "            \"worker_name\": \"<PERSON><PERSON><PERSON>\",\n", "            \"worker_number\": \"TT4UWBZQKZRBE5OL9PTT\"\n", "          },\n", "          {\n", "            \"extracted_name\": \"<PERSON><PERSON>\",\n", "            \"closest_match\": \"<PERSON><PERSON>\",\n", "            \"closest_match_confidence\": 100,\n", "            \"closest_match_successful\": true,\n", "            \"worker_name\": \"<PERSON><PERSON>\",\n", "            \"worker_number\": \"TT4UWBZQKEWVAQOQZDTT\"\n", "          }\n", "        ],\n", "        \"summary\": {\n", "          \"total_extracted\": 3,\n", "          \"successful_matches\": 3,\n", "          \"confidence_threshold\": 70\n", "        }\n", "      },\n", "      \"workers_matched\": {\n", "        \"Elena\": {\n", "          \"workerId\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"employeeId\": \"7\",\n", "          \"workerType\": \"EMPLOYEE\",\n", "          \"exemptionType\": \"NON_EXEMPT\",\n", "          \"workState\": \"WA\",\n", "          \"birthDate\": \"1980-01-01T00:00:00Z\",\n", "          \"sex\": \"FEMALE\",\n", "          \"hireDate\": \"2024-11-05T00:00:00Z\",\n", "          \"name\": {\n", "            \"familyName\": \"<PERSON><PERSON><PERSON>\",\n", "            \"given<PERSON>ame\": \"<PERSON>\"\n", "          },\n", "          \"legalId\": {\n", "            \"legalIdType\": \"SSN\",\n", "            \"legalIdValue\": \"999999999\"\n", "          },\n", "          \"organization\": {\n", "            \"organizationId\": \"TT800291970271TT\",\n", "            \"name\": \"100 Staff\",\n", "            \"number\": \"100\"\n", "          },\n", "          \"currentStatus\": {\n", "            \"workerStatusId\": \"TTDWS906IMW2JSH8AQTT\",\n", "            \"statusType\": \"ACTIVE\",\n", "            \"statusReason\": \"HIRED\",\n", "            \"effectiveDate\": \"2024-11-05T00:00:00Z\"\n", "          },\n", "          \"links\": [\n", "            {\n", "              \"rel\": \"self\",\n", "              \"href\": \"https://api.paychex.com/workers/TTM9LQF7M3F0S89L3ATT\"\n", "            },\n", "            {\n", "              \"rel\": \"communications\",\n", "              \"href\": \"https://api.paychex.com/workers/TTM9LQF7M3F0S89L3ATT/communications\"\n", "            }\n", "          ],\n", "          \"companyId\": \"TT4UWBZQKEVRRWBWS9TT\",\n", "          \"type\": \"worker\",\n", "          \"id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"_upload_session\": \"2ab601ce\",\n", "          \"_source_file\": \"TTcompany_004UWBZQKEVRRWBWS9GU_unknown_00M9LQF7M3F0S89L3AQP.json\",\n", "          \"_upload_timestamp\": \"2025-08-05 19:15:03 UTC\",\n", "          \"_file_hash\": \"0dd217d97b5e69c71d51acb190c0403d\",\n", "          \"_rid\": \"NmJkAN9olDMFmgIAAAAACA==\",\n", "          \"_self\": \"dbs/NmJkAA==/colls/NmJkAN9olDM=/docs/NmJkAN9olDMFmgIAAAAACA==/\",\n", "          \"_etag\": \"\\\"c4005ba0-0000-0100-0000-689258370000\\\"\",\n", "          \"_attachments\": \"attachments/\",\n", "          \"_ts\": 1754421303\n", "        },\n", "        \"Lejla\": {\n", "          \"workerId\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"employeeId\": \"5\",\n", "          \"workerType\": \"EMPLOYEE\",\n", "          \"exemptionType\": \"NON_EXEMPT\",\n", "          \"workState\": \"WA\",\n", "          \"birthDate\": \"1980-01-01T00:00:00Z\",\n", "          \"sex\": \"FEMALE\",\n", "          \"hireDate\": \"2022-02-17T00:00:00Z\",\n", "          \"name\": {\n", "            \"familyName\": \"<PERSON>avric\",\n", "            \"givenName\": \"<PERSON><PERSON><PERSON>\"\n", "          },\n", "          \"legalId\": {\n", "            \"legalIdType\": \"SSN\",\n", "            \"legalIdValue\": \"999999999\"\n", "          },\n", "          \"organization\": {\n", "            \"organizationId\": \"TT800291970271TT\",\n", "            \"name\": \"100 Staff\",\n", "            \"number\": \"100\"\n", "          },\n", "          \"currentStatus\": {\n", "            \"workerStatusId\": \"TTDWS906IMW2JSH8AQTT\",\n", "            \"statusType\": \"ACTIVE\",\n", "            \"statusReason\": \"HIRED\",\n", "            \"effectiveDate\": \"2022-02-17T00:00:00Z\"\n", "          },\n", "          \"links\": [\n", "            {\n", "              \"rel\": \"self\",\n", "              \"href\": \"https://api.paychex.com/workers/TT4UWBZQKZRBE5OL9PTT\"\n", "            },\n", "            {\n", "              \"rel\": \"communications\",\n", "              \"href\": \"https://api.paychex.com/workers/TT4UWBZQKZRBE5OL9PTT/communications\"\n", "            }\n", "          ],\n", "          \"companyId\": \"TT4UWBZQKEVRRWBWS9TT\",\n", "          \"type\": \"worker\",\n", "          \"id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"_upload_session\": \"2ab601ce\",\n", "          \"_source_file\": \"TTcompany_004UWBZQKEVRRWBWS9GU_unknown_004UWBZQKZRBE5OL9PP8.json\",\n", "          \"_upload_timestamp\": \"2025-08-05 19:15:07 UTC\",\n", "          \"_file_hash\": \"bd3498779b7aab8488d3db429b0d1a3e\",\n", "          \"_rid\": \"NmJkAN9olDMfmgIAAAAACA==\",\n", "          \"_self\": \"dbs/NmJkAA==/colls/NmJkAN9olDM=/docs/NmJkAN9olDMfmgIAAAAACA==/\",\n", "          \"_etag\": \"\\\"c40052a1-0000-0100-0000-6892583b0000\\\"\",\n", "          \"_attachments\": \"attachments/\",\n", "          \"_ts\": 1754421307\n", "        },\n", "        \"Irina\": {\n", "          \"workerId\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"employeeId\": \"2\",\n", "          \"workerType\": \"EMPLOYEE\",\n", "          \"exemptionType\": \"NON_EXEMPT\",\n", "          \"workState\": \"WA\",\n", "          \"birthDate\": \"1980-01-01T00:00:00Z\",\n", "          \"sex\": \"FEMALE\",\n", "          \"hireDate\": \"2003-06-01T00:00:00Z\",\n", "          \"name\": {\n", "            \"familyName\": \"<PERSON><PERSON><PERSON>\",\n", "            \"given<PERSON>ame\": \"<PERSON><PERSON>\"\n", "          },\n", "          \"legalId\": {\n", "            \"legalIdType\": \"SSN\",\n", "            \"legalIdValue\": \"999999999\"\n", "          },\n", "          \"organization\": {\n", "            \"organizationId\": \"TT800291970271TT\",\n", "            \"name\": \"100 Staff\",\n", "            \"number\": \"100\"\n", "          },\n", "          \"currentStatus\": {\n", "            \"workerStatusId\": \"TTDWS906IMW2JSH8AQTT\",\n", "            \"statusType\": \"ACTIVE\",\n", "            \"statusReason\": \"HIRED\",\n", "            \"effectiveDate\": \"2003-06-01T00:00:00Z\"\n", "          },\n", "          \"links\": [\n", "            {\n", "              \"rel\": \"self\",\n", "              \"href\": \"https://api.paychex.com/workers/TT4UWBZQKEWVAQOQZDTT\"\n", "            },\n", "            {\n", "              \"rel\": \"communications\",\n", "              \"href\": \"https://api.paychex.com/workers/TT4UWBZQKEWVAQOQZDTT/communications\"\n", "            }\n", "          ],\n", "          \"companyId\": \"TT4UWBZQKEVRRWBWS9TT\",\n", "          \"type\": \"worker\",\n", "          \"id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"_upload_session\": \"2ab601ce\",\n", "          \"_source_file\": \"TTcompany_004UWBZQKEVRRWBWS9GU_unknown_004UWBZQKEWVAQOQZDMD.json\",\n", "          \"_upload_timestamp\": \"2025-08-05 19:15:04 UTC\",\n", "          \"_file_hash\": \"96ab095a3d11bb5f609bc700a304a9a6\",\n", "          \"_rid\": \"NmJkAN9olDMUmgIAAAAACA==\",\n", "          \"_self\": \"dbs/NmJkAA==/colls/NmJkAN9olDM=/docs/NmJkAN9olDMUmgIAAAAACA==/\",\n", "          \"_etag\": \"\\\"c400a1a0-0000-0100-0000-689258380000\\\"\",\n", "          \"_attachments\": \"attachments/\",\n", "          \"_ts\": 1754421304\n", "        }\n", "      },\n", "      \"payperiodID\": \"TT700766042406TT\",\n", "      \"payperiod\": {\n", "        \"status\": \"SUCCESS\",\n", "        \"payPeriodId\": \"TT700766042406TT\",\n", "        \"checkDate\": \"2025-04-30\",\n", "        \"startDate\": \"2025-04-16\",\n", "        \"endDate\": \"2025-04-30\",\n", "        \"submitByDate\": \"2025-04-28\",\n", "        \"reason\": \"No specific pay period dates mentioned in the email; selected the period with the soonest submitByDate (2025-04-28) after the received_on date (2025-04-10).\"\n", "      },\n", "      \"worker_lookup_success\": true,\n", "      \"agent_context\": {\n", "        \"company_notes\": [\n", "          {\n", "            \"message\": \"Email both <PERSON> and <PERSON><PERSON> regarding any issues.\",\n", "            \"startDate\": \"2021-03-18T00:00:00Z\",\n", "            \"endDate\": \"4712-12-31T00:00:00Z\",\n", "            \"noteType\": \"PERSONAL_NOTES\",\n", "            \"priorityType\": \"NON_URGENT\"\n", "          },\n", "          {\n", "            \"message\": \"client is aware of the PPF\",\n", "            \"startDate\": \"2021-03-31T00:00:00Z\",\n", "            \"endDate\": \"4712-12-31T00:00:00Z\",\n", "            \"noteType\": \"PERSONAL_NOTES\",\n", "            \"priorityType\": \"URGENT\"\n", "          }\n", "        ],\n", "        \"pay_components\": [\n", "          {\n", "            \"classificationType\": \"REGULAR\",\n", "            \"description\": \"Use for regular pay.\",\n", "            \"earning_name\": \"Birthday\"\n", "          },\n", "          {\n", "            \"classificationType\": \"SICK_PAY\",\n", "            \"description\": \"Use for non-qualified sick pay.\",\n", "            \"earning_name\": \"Sick\"\n", "          },\n", "          {\n", "            \"classificationType\": \"DEDUCTION\",\n", "            \"description\": \"Use for any normal employee deduction.\",\n", "            \"earning_name\": \"Payactiv\"\n", "          },\n", "          {\n", "            \"classificationType\": \"REGULAR\",\n", "            \"description\": \"Use for regular pay.\",\n", "            \"earning_name\": \"Bereavement\"\n", "          },\n", "          {\n", "            \"classificationType\": \"REGULAR\",\n", "            \"description\": \"Use for vacation pay.\",\n", "            \"earning_name\": \"Vacation\"\n", "          },\n", "          {\n", "            \"classificationType\": \"REGULAR\",\n", "            \"description\": \"Use for regular pay.\",\n", "            \"earning_name\": \"Hourly\"\n", "          },\n", "          {\n", "            \"classificationType\": \"SUPPLEMENTAL\",\n", "            \"description\": \"Use for overtime pay.\",\n", "            \"earning_name\": \"Overtime\"\n", "          }\n", "        ],\n", "        \"workers_info\": {\n", "          \"TTM9LQF7M3F0S89L3ATT\": {\n", "            \"notes\": null,\n", "            \"pay_rate\": {\n", "              \"rateType\": null,\n", "              \"rate\": null\n", "            },\n", "            \"pay_standard\": {\n", "              \"payFrequency\": \"SEMI_MONTHLY\",\n", "              \"overtimeFactor\": \"1.5\",\n", "              \"calculatedPayPeriod\": \"0.00\",\n", "              \"calculatedAnnualSalary\": \"0.00\",\n", "              \"calculatedAnnualSalaryFromHistory\": \"17433\"\n", "            }\n", "          },\n", "          \"TT4UWBZQKZRBE5OL9PTT\": {\n", "            \"notes\": null,\n", "            \"pay_rate\": {\n", "              \"rateType\": null,\n", "              \"rate\": null\n", "            },\n", "            \"pay_standard\": {\n", "              \"payFrequency\": \"SEMI_MONTHLY\",\n", "              \"overtimeFactor\": \"1.5\",\n", "              \"calculatedPayPeriod\": \"0.00\",\n", "              \"calculatedAnnualSalary\": \"0.00\",\n", "              \"calculatedAnnualSalaryFromHistory\": \"27886\"\n", "            }\n", "          },\n", "          \"TT4UWBZQKEWVAQOQZDTT\": {\n", "            \"notes\": null,\n", "            \"pay_rate\": {\n", "              \"rateType\": null,\n", "              \"rate\": null\n", "            },\n", "            \"pay_standard\": {\n", "              \"payFrequency\": \"SEMI_MONTHLY\",\n", "              \"overtimeFactor\": \"1.5\",\n", "              \"calculatedPayPeriod\": \"0.00\",\n", "              \"calculatedAnnualSalary\": \"0.00\",\n", "              \"calculatedAnnualSalaryFromHistory\": \"29467\"\n", "            }\n", "          }\n", "        },\n", "        \"past_pay_periods\": {\n", "          \"1\": \"TT700800235275TT\",\n", "          \"2\": \"TT700795051049TT\"\n", "        }\n", "      },\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"company_worker_lookup_retrieve_company_info\": 0.7488856079999096,\n", "        \"company_worker_lookup_identify_sender\": 1.114802133000012,\n", "        \"company_worker_lookup_process_attachments\": 3.366592588000458,\n", "        \"company_worker_lookup_company_router\": 0.0005014529997424688,\n", "        \"company_worker_lookup_retrieve_workers\": 1.1210299289996328,\n", "        \"company_worker_lookup_get_payperiodID\": 7.407769280999673,\n", "        \"company_worker_lookup_match_workers\": 6.581544548999773,\n", "        \"company_worker_lookup_router\": 0.0005410210005720728,\n", "        \"company_worker_lookup_finishing_node\": 0.0004198240003461251\n", "      },\n", "      \"llm_usage\": {\n", "        \"company_worker_lookup_identify_sender\": {\n", "          \"model\": \"gpt-41\",\n", "          \"input_tokens_estimated\": 428,\n", "          \"output_tokens_estimated\": 54\n", "        }\n", "      }\n", "    },\n", "    \"payroll_processing_output_state\": {\n", "      \"payroll_commands_agent_1\": [\n", "        {\n", "          \"name\": \"<PERSON>\",\n", "          \"worker_id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"registered_name\": \"<PERSON>\",\n", "          \"payHours\": 38.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"registered_name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"payHours\": 44.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 24.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Vacation\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 19.75,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        }\n", "      ],\n", "      \"payroll_commands_agent_2\": [\n", "        {\n", "          \"name\": \"<PERSON>\",\n", "          \"worker_id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"registered_name\": \"<PERSON>\",\n", "          \"payHours\": 38.0,\n", "          \"rate_overwrite\": false,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"registered_name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"payHours\": 44.0,\n", "          \"rate_overwrite\": false,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 24.0,\n", "          \"rate_overwrite\": false,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Vacation\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 19.75,\n", "          \"rate_overwrite\": false,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        }\n", "      ],\n", "      \"processed_payrolls\": [\n", "        {\n", "          \"name\": \"<PERSON>\",\n", "          \"worker_id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"registered_name\": \"<PERSON>\",\n", "          \"payHours\": 38.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"registered_name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"payHours\": 44.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 24.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Vacation\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 19.75,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        }\n", "      ],\n", "      \"agents_matched\": true,\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"payroll_processing_agent_1_run\": 9.676831932999448,\n", "        \"payroll_processing_agent_2_run\": 11.885591297999781,\n", "        \"payroll_processing_router\": 0.00040847500076779397,\n", "        \"payroll_processing_finishing_node\": 0.00018422199991618982\n", "      },\n", "      \"llm_usage\": {\n", "        \"payroll_processing_agent_run\": {\n", "          \"model\": \"gpt-41\",\n", "          \"input_tokens_estimated\": 7930,\n", "          \"output_tokens_estimated\": 286\n", "        },\n", "        \"payroll_processing_agent_2_run\": {\n", "          \"model\": \"gpt-41\",\n", "          \"input_tokens_estimated\": 7930,\n", "          \"output_tokens_estimated\": 286\n", "        }\n", "      }\n", "    },\n", "    \"validation_output_state\": {\n", "      \"validated_payroll_entries\": {\n", "        \"validated_entries\": [\n", "          {\n", "            \"name\": \"<PERSON>\",\n", "            \"worker_id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "            \"registered_name\": \"<PERSON>\",\n", "            \"payHours\": 38.0,\n", "            \"classificationType\": \"REGULAR\",\n", "            \"earning_name\": \"Hourly\"\n", "          },\n", "          {\n", "            \"name\": \"<PERSON><PERSON><PERSON>\",\n", "            \"worker_id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "            \"registered_name\": \"<PERSON><PERSON><PERSON>\",\n", "            \"payHours\": 44.0,\n", "            \"classificationType\": \"REGULAR\",\n", "            \"earning_name\": \"Hourly\"\n", "          },\n", "          {\n", "            \"name\": \"<PERSON><PERSON>\",\n", "            \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "            \"registered_name\": \"<PERSON><PERSON>\",\n", "            \"payHours\": 24.0,\n", "            \"classificationType\": \"REGULAR\",\n", "            \"earning_name\": \"Vacation\"\n", "          },\n", "          {\n", "            \"name\": \"<PERSON><PERSON>\",\n", "            \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "            \"registered_name\": \"<PERSON><PERSON>\",\n", "            \"payHours\": 19.75,\n", "            \"classificationType\": \"REGULAR\",\n", "            \"earning_name\": \"Hourly\"\n", "          }\n", "        ],\n", "        \"success\": true\n", "      },\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"validation_run_agent\": 6.750360647999514,\n", "        \"validation_router\": 0.0002814580002450384,\n", "        \"validation_finishing_node\": 0.00016207499993470265\n", "      },\n", "      \"llm_usage\": {\n", "        \"validation_run_agent\": {\n", "          \"model\": \"o4-mini\",\n", "          \"input_tokens_estimated\": 251,\n", "          \"output_tokens_estimated\": 284\n", "        }\n", "      }\n", "    },\n", "    \"execution_output_state\": {\n", "      \"completed_payrolls\": [\n", "        {\n", "          \"name\": \"<PERSON>\",\n", "          \"worker_id\": \"TTM9LQF7M3F0S89L3ATT\",\n", "          \"registered_name\": \"<PERSON>\",\n", "          \"payHours\": 38.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKZRBE5OL9PTT\",\n", "          \"registered_name\": \"<PERSON><PERSON><PERSON>\",\n", "          \"payHours\": 44.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 24.0,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Vacation\"\n", "        },\n", "        {\n", "          \"name\": \"<PERSON><PERSON>\",\n", "          \"worker_id\": \"TT4UWBZQKEWVAQOQZDTT\",\n", "          \"registered_name\": \"<PERSON><PERSON>\",\n", "          \"payHours\": 19.75,\n", "          \"classificationType\": \"REGULAR\",\n", "          \"earning_name\": \"Hourly\"\n", "        }\n", "      ],\n", "      \"completed_payrolls_flag\": true,\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"execution_create_payrolls\": 0.0002662659999259631,\n", "        \"execution_success_payroll_router\": 0.0002806230004352983,\n", "        \"execution_finishing_node\": 0.00016219600001932122\n", "      },\n", "      \"llm_usage\": {}\n", "    },\n", "    \"release_output_state\": {\n", "      \"proceed_without_verification\": {\n", "        \"status\": true,\n", "        \"errors\": \"\"\n", "      },\n", "      \"release_payroll\": true,\n", "      \"release_dashboard_logging\": false,\n", "      \"release_update_upstream_ticket\": true,\n", "      \"release_send_confirmation\": true,\n", "      \"release_create_summary\": \"- **Report date/source:** Payroll reported via email on April 10, 2025, for Bubly Test LLC.\\n- **Employees processed:** 3 employees (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>).\\n- **Aggregate hours worked:** 125.75 total hours (<PERSON>: 38, <PERSON><PERSON><PERSON>: 44, <PERSON><PERSON>: 24 vacation + 19.75 regular).\\n- **Total payroll cost:** Not available; pay rates were not provided in the submission.\\n- **Validation:** No anomalies detected; all entries matched to active employees.\",\n", "      \"should_continue\": true,\n", "      \"run_time\": {\n", "        \"release_proceed_without_verification\": 1.0386856340001032,\n", "        \"release_router\": 0.0003460489997451077,\n", "        \"release_payroll\": 0.0001529040000605164,\n", "        \"release_dashboard_logging\": 0.0013086839999232325,\n", "        \"release_update_upstream_ticket\": 0.00020946999939042144,\n", "        \"release_send_confirmation\": 0.00020325599962234264,\n", "        \"release_create_summary\": 3.840800823999416,\n", "        \"release_finishing_node\": 0.00017751199993654154\n", "      },\n", "      \"llm_usage\": {\n", "        \"release_proceed_without_verification\": {\n", "          \"model\": \"gpt-41\",\n", "          \"input_tokens_estimated\": 7970,\n", "          \"output_tokens_estimated\": 4\n", "        },\n", "        \"release_create_summary\": {\n", "          \"model\": \"gpt-41\",\n", "          \"input_tokens_estimated\": 16142,\n", "          \"output_tokens_estimated\": 586\n", "        }\n", "      }\n", "    },\n", "    \"started_at\": \"2025-08-28T22:56:49.168541\",\n", "    \"finished_at\": \"2025-08-28T22:57:58.548281\",\n", "    \"ticket_id\": null,\n", "    \"termination_flag\": false,\n", "    \"successful_flag\": true,\n", "    \"run_time\": 69.38\n", "  }\n", "}\n"]}], "source": ["\n", "# Set the endpoint\n", "url = \"http://localhost:8000/api/v1/process-email\"\n", "\n", "# Define one test case (you can also load from file)\n", "\n", "test_case = {\n", "  \"ingestId\": \"test-ingest-attachment2\",\n", "  \"displayId\": \"TT1478TT\",\n", "  \"comment\": {\n", "    \"uid\": \"1\",\n", "    \"id\": \"1000\",\n", "    \"timestamp\": \"2025-04-10T15:43:30.560000\",\n", "    \"user_properties\": {\n", "      \"string:Sender\": \"<EMAIL>\",\n", "      \"string:Sender Domain\": \"gmail.com\"\n", "    },\n", "    \"messages\": [\n", "      {\n", "        \"from\": \"<EMAIL>\",\n", "        \"to\": [\"<EMAIL>\"],\n", "        \"body\": { \"text\": \"Good morning We are just reporting hours for Bubly test llc Elena 38 Lejla  44 horus Irina 24 vacation hours Regular hours 19.75 total of 43.75 Thank you Please let us know you got this email. -- HR Team of Bubly Test LLC 123 Abc Rd Universe WA 00000\" },\n", "        \"subject\": { \"text\": \"Bubly test staff payroll\" }\n", "      }\n", "    ],\n", "     \"attachments\": [\n", "        {\n", "          \"name\": \"\",\n", "          \"size\": 0,\n", "          \"content_type\": \"\",\n", "          \"content_hash\": \"\",\n", "          \"inline\": \"\",\n", "          \"attachment_reference\": \"\",\n", "          \"base64\": \"\"\n", "        }\n", "     ]\n", "  }\n", "}\n", "# Make the request\n", "response = requests.post(url, json=test_case)\n", "\n", "# Handle response\n", "if response.ok:\n", "    print(\"✅ Success!\")\n", "    print(json.dumps(response.json(), indent=2))\n", "else:\n", "    print(\"❌ Error:\")\n", "    print(response.status_code, response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "b16f16af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f0e63bd", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:37.704747Z", "start_time": "2025-05-20T20:45:37.702195Z"}}, "outputs": [], "source": ["def flatten_dict(d, parent_key='', sep='.'):\n", "    \"\"\"Recursively flattens nested dicts with dot notation.\"\"\"\n", "    items = []\n", "    for k, v in d.items():\n", "        new_key = f\"{parent_key}{sep}{k}\" if parent_key else k\n", "        if isinstance(v, dict):\n", "            items.extend(flatten_dict(v, new_key, sep=sep).items())\n", "        elif isinstance(v, list) and all(isinstance(i, dict) for i in v):\n", "            for i, subdict in enumerate(v):\n", "                items.extend(flatten_dict(subdict, f\"{new_key}[{i}]\", sep=sep).items())\n", "        else:\n", "            items.append((new_key, v))\n", "    return dict(items)"]}, {"cell_type": "code", "execution_count": null, "id": "51768a0d", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:39.000498Z", "start_time": "2025-05-20T20:45:38.982843Z"}}, "outputs": [], "source": ["response_data = response.json()\n", "\n", "# Step 2: Decode the nested string\n", "parsed = response_data[\"response\"]\n", "\n", "# Step 3: Flatten the parsed dictionary\n", "flat = flatten_dict(parsed)\n", "\n", "# Step 4: Convert to DataFrame\n", "df_flat = pd.DataFrame([flat])\n", "df_flat.head()"]}, {"cell_type": "code", "execution_count": null, "id": "51d13015", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:39.999191Z", "start_time": "2025-05-20T20:45:39.997391Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1cbc913189aa62bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}