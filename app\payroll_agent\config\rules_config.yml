classification:
  business_knockout:
    max_rules_per_chunk: 15
  technical_knockout:
    max_display_ids: 5
  complexity_rules:
    easy:
      # All possible values for each field are now in the "easy" category
      RequestType: ["same_as_prior_period", "standard_rates", "custom", "mix", "unclear", "unknown"]
      EmployeeCount: ["1_employee", "multiple_employees", "unknown"]
      LineItemsPerPerson: ["1", "2", "3", "4+", "unknown"]
      NumberOfPayPeriods: ["1", "2", "3+", "unclear", "unknown"]
      PayPeriodType: ["weekly", "biweekly", "monthly", "bi_monthly", "quarterly", "bi_annually", "mix", "unclear", "unknown"]
      NonActionableInstructions: ["none", "non_payroll", "future_task", "call_to_action", "change_management", "system_generated", "unclear", "unknown"]
      EmployeeSpecialCases: ["yes", "none", "unknown"]
      CallRequested: ["no", "yes", "unknown"]
      HasAttachments: ["no", "yes", "unknown"]
      AttachmentType: ["none", "excel", "pdf", "other", "unknown"]
      ThirdParty: ["no", "yes", "unknown"]
      InformationFormat: ["embedded_table", "embedded_rows", "attachments_excel", "attachments_pdf", "attachments_other", "mix", "unclear", "unknown"]
      EmailLanguage: ["english", "spanish", "other", "unclear", "unknown"]
      PotentialFraud: ["none", "low_risk", "medium_risk", "high_risk", "unknown"]
      InsufficientInformation: ["yes", "no", "unknown"]
      PayComponentType: ["payHours_only", "payHours_with_payRate", "payHours_with_payRateId", "payUnits_only", "payUnits_with_payRate", "payUnits_with_payRateId", "payAmount_only", "mixed", "unclear", "unknown"]
    # Keep empty medium and hard categories to maintain structure
    medium: {}
    hard:
      Any: []
