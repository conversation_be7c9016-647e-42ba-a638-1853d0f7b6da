import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Fix the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.orchestrator import orchestrator

async def test_orchestrator():
    """Test the orchestrator with a sample ingest_id"""
    sas_token = os.getenv("AZURE_STORAGE_SAS_TOKEN")
    print(f"SAS token configured: {'Yes' if sas_token else 'No'}")
    
    test_ingest_id = "test-ingestid2"
    
    print(f"Testing orchestrator with ingest_id: {test_ingest_id}")
    
    try:
        response = await orchestrator.orchestrate_payroll_processing(test_ingest_id)
        
        print("=" * 50)
        print("ORCHESTRATION RESPONSE:")
        print("=" * 50)
        print(f"Ingest ID: {response.ingest_id}")
        print(f"Status: {response.status}")
        print(f"Status Code: {response.status_code}")
        print(f"Started: {response.started_at}")
        print(f"Finished: {response.finished_at}")
        print(f"Runtime: {response.run_time:.2f}s")
                
    except Exception as e:
        print(f"Error running orchestrator: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_orchestrator())