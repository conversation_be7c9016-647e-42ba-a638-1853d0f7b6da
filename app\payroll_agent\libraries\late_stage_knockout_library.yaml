knockout_rules:

  - id: ADD_MESSAGES_TO_CHECK_STUBS
    description: Include a personalized message or memo on an employee's pay stub
    condition: Customer requests a message to add to "check stubs" or the "memo line", or similar wording
    examples:
    - Please include a message of "Merry Christmas" in the memo of holiday bonus checks


  - id: BILLING_OVERRIDES #waive or credit Paychex fees only; dont want to cover general billing questions or issues
    description: Customer asks Paychex to waive, credit, or discount payroll processing fees or invoice charges
    condition: Customer explicitly uses words like "waive", "credit", "refund", "no charge", "discount", or "block billing" in reference to Paychex processing fees or the payroll invoice; ignore messages about billing failures or payment issues
    examples:
    - Please waive all processing fees for this off-cycle run.
    - Credit today's payroll invoice. No charge per our agreement.
    - Do not bill us for the special bonus payroll this week.
    - I just processed additional payroll. Can you please block the billing?


  - id: BLOCK_UNBLOCK_DD_OF_NET_PAY
    description: Disable direct deposit for an employee's upcoming payroll
    condition: Customer requests "no direct deposit" for either a specific employee, group or everyone in that pay period. These occur commonly when in-house or manual checks are being run.
    examples:
    - please block the entire direct deposit.
    - Would you be able to block <PERSON>'s direct deposit and have his check mailed to our office?


  - id: CHANGES_TO_PACKAGE_AND_DELIVERY
    description: Update where or how payroll checks/reports are delivered (address, contact, shipping method)
    condition: Customer requests changes to the delivery address, contact, or shipping method for payroll checks or reports.
    examples:
    - Can you please update the delivery address for this report?
    - Could you please update the delivery method to be shipped by UPS?


  - id: CHECK_DATE_LT_1_BUSINESS_DAY
    description: Request in which the stated check date is less than one full U.S. banking business day away and the email arrives after 10:00PM on the preceding business day.
    condition: Customer requests to process payroll on the same day as the check-date for that payroll. Or the email's received-timestamp is later than 10:00PM EST on the last business day before the requested check date (after adjusting for weekends and federal banking holidays). Or if the client is requesting to move a check date. Important warning - focus here is when the check needs to be issued within one business day, which is usually referred to as the check date. This should not be confused with releasing or processing the payroll which typically indicates when the payroll should be made official. Releasing payroll can happen quickly and would not meet this condition. For example, if check date is in 5 days, customer's ask to process it tomorrow should not be considered as this condition. Similarly, payroll is typically due a few days earlier than the check data so payroll due date should not be confused with the check date.
    examples:
    - Here is our payroll for this pay period. Check date is today.
    - "Sent 4/24/2025 10:15 PM: \n Can you issue checks for this payroll overnight. Check date is tomorrow 4/25/2025."
    - Can you run payroll but move our checkdate?


  - id: CHECK_WORKING_DAYS_FOR_CHECK_DATE
    description: The check day is less than 2 working days away from the received date of the email
    condition: Always call the tool "paychex_validate_check_date(date=date, check_date=check_date, 2 )" to validate that the check date is at least 2 business day away, if the response has a success = false, then the email is not valid
    examples:
      - "{success=false, min_working_days=min_working_days, message=message}"

  - id: VALIDATE_WORKER_PAYMENTS
    description: Validate that the amounts in the payroll_amounts are not higher than 1.3 the historical payment for the same worker.
    condition: Always call the tool paychex_validate_worker_payment(worker_id=worker_id, amount=amount, multiplier=1.3) to validate that the amounts are not higher than 1.3 the historical payment for the same worker. If the response has a success = false, then the email is not valid
    examples:
      - "{success=false, message=message}"


  - id: CHECK_PTO_BALANCE
    description: Customer is asking whether an employee has enough vacation, sick, or PTO balance before processing time off in payroll
    condition: Customer includes a note to check or confirm an employee's available PTO, vacation, or sick balance before submitting the entry
    examples:
      - Process 8 hours of vacation for John Doe, but before submitting please make sure he has enough remaining


  - id: TERMINATE_WORKER
    description: Remove an employee from payroll due to resignation, termination, or other separation
    condition: Customer includes terms such as "terminate", "separate", "final paycheck", or provides a last-day-worked date. A customer's ask to not include an employee for this pay period or to remove him for this pay period does not meet this condition. Customer has to ask to permanently remove the employee from the company and all of its future payrolls.
    examples:
    - Please accept this email as the formal notification of termination for the following 2 employees. I've attached Signed Termination Forms for your documentation as well. Below is the information on the forms for final payroll we need prepared.
