from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Dict, Any
from typing_extensions import Optional


class BaseGetModel(BaseModel):
    def get(self, key: str, default=None):
        return getattr(self, key, default)

class Span(BaseGetModel):
    content_part: Optional[str] = None
    message_index: Optional[int] = None
    utf16_byte_start: Optional[int] = None
    utf16_byte_end: Optional[int] = None
    char_start: Optional[int] = None
    char_end: Optional[int] = None


class Entity(BaseGetModel):
    id: str
    name: str
    spans: List[Span]
    kind: str
    formatted_value: str
    probability: Optional[float]
    capture_ids: List[Any]
    span: Span


class Label(BaseGetModel):
    name: List[str]
    probability: float


class LabelProperty(BaseGetModel):
    property_id: str
    property_name: str
    value: float


class Subject(BaseGetModel):
    text: str


class Signature(BaseGetModel):
    text: str


class Body(BaseGetModel):
    text: str


class Message(BaseGetModel):
    from_: Optional[str] = Field(None, alias='from')
    to: Optional[List[str]] = None
    sent_at: Optional[str] = None
    body: Body
    subject: Optional[Subject] = {}
    signature: Optional[Signature] = None


class Comment(BaseGetModel):
    uid: Optional[str] = None
    id: Optional[str] = None
    timestamp: str
    thread_id: Optional[str] = None
    user_properties: Optional[Dict[str, Any]] = {}
    messages: List[Message]
    text_format: Optional[str] = None
    attachments: Optional[List[Any]] = None
    source_id: Optional[str] = None
    last_modified: Optional[str] = None
    created_at: Optional[str] = None
    context: Optional[str] = None


class UpstreamModel(BaseGetModel):
    ingestId: Optional[str] = Field(None, description='ingest id')
    displayId: str | list[str] = Field(..., description='display id')
    comment: Comment
    sequence_id: Optional[str] = Field(None, description='sequence id')
    labels: List[Label] = Field(default_factory=list, description='labels' )
    entities: List[Entity] = Field(default_factory=list, description='entities' )
    label_properties: List[LabelProperty] = Field(default_factory=list, alias='label_properties' )
    received_at: str = Field(default_factory=lambda: datetime.utcnow().strftime('%Y-%m-%d %T'),
                             description="Timestamp of when the email was received")
    
