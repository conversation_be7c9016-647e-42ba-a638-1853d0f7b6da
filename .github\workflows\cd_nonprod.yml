name: CD - Payroll Email Agent (nonprod)

on:
  push:
    branches: [ nonprod ]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to nonprod HEAD)'
        required: false
        default: dev

jobs:
  build-and-deploy:
    runs-on: [self-hosted, rhel9, er, nonprod]
    permissions:
      id-token: write
      contents: read
    environment:
      name: nonprod
      url: ${{ steps.deploy.outputs.container-app-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Azure login (OIDC)
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Deploy to Azure Container App (nonprod)
        id: deploy
        uses: ./.github/actions/deploy-container-app-nonprod
        with:
          azure-subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          git-sha: ${{ github.sha }}
        env:
          # Secrets
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          LANGCHAIN_API_KEY: ${{ secrets.LANGCHAIN_API_KEY }}
          MCP_SERVERS__PAYCHEX__CLIENT_ID: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_ID }}
          LANGCHAIN_PROJECT: ${{ secrets.LANGCHAIN_PROJECT }}
          MCP_SERVERS__PAYCHEX__CLIENT_SECRET: ${{ secrets.MCP_SERVERS__PAYCHEX__CLIENT_SECRET }}
          AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
          # Non-secret vars
          LANGCHAIN_TRACING_V2: ${{ vars.LANGCHAIN_TRACING_V2 }}
          MCP_SERVERS__PAYCHEX__TRANSPORT: ${{ vars.MCP_SERVERS__PAYCHEX__TRANSPORT }}
          MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL: ${{ vars.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL }}
          MCP_SERVERS__PAYCHEX__URL: ${{ vars.MCP_SERVERS__PAYCHEX__URL }}
          LANGCHAIN_ENDPOINT: ${{ vars.LANGCHAIN_ENDPOINT }}
          LLM_TYPE: ${{ vars.LLM_TYPE }}
          AZURE_ENDPOINT: ${{ vars.AZURE_ENDPOINT }}
          API_VERSION: ${{ vars.API_VERSION }}
          AZURE_STORAGE_ACCOUNT_URL: ${{ vars.AZURE_STORAGE_ACCOUNT_URL }}
          DASHBOARD_API_URL: ${{ vars.DASHBOARD_API_URL }}
          ENABLE_DASHBOARD_INTEGRATION: ${{ vars.ENABLE_DASHBOARD_INTEGRATION }}
          EASY_AUTH_ENABLED: ${{ vars.EASY_AUTH_ENABLED }}


