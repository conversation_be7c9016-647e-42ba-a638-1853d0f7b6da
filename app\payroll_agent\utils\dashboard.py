import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Union
import httpx
import logging
import json

from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.easy_auth_client import EasyAuthClient

logger = logging.getLogger(__name__)


class DashboardIntegration:
    """Integration with the dashboard API for logging PayrollState data"""

    def __init__(self):
        self.dashboard_url = settings.DASHBOARD_API_URL or "http://api:8000"
        self.enabled = settings.ENABLE_DASHBOARD_INTEGRATION
        self.timeout = settings.DASHBOARD_TIMEOUT
        self.auth_client: Optional[EasyAuthClient] = None
        if settings.EASY_AUTH_ENABLED and settings.AZURE_CLIENT_ID:
            try:
                self.auth_client = EasyAuthClient(
                    app_url=self.dashboard_url,
                    target_app_client_id=settings.AZURE_CLIENT_ID,
                    use_default_credential=settings.EASY_AUTH_USE_DEFAULT_CREDENTIAL,
                )
                logger.info("Dashboard Easy Auth client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Easy Auth client: {e}")
        else:
            if not settings.EASY_AUTH_ENABLED:
                logger.info("Easy Auth disabled; dashboard requests will be unauthenticated")
            else:
                logger.info("AZURE_CLIENT_ID not set; dashboard requests will be unauthenticated")
        
        logger.info(f"Dashboard integration initialized: enabled={self.enabled}, url={self.dashboard_url}")

    async def log_transaction(self, state: Union[Dict, Any], started_at: Optional[str] = None,
                              finished_at: Optional[str] = None) -> bool:
        """Log PayrollState to dashboard with timestamps"""

        if not self.enabled:
            logger.info("Dashboard integration disabled - set ENABLE_DASHBOARD_INTEGRATION=true to enable")
            return False

        try:
            logger.info("Building dashboard payload...")
            payload = self._build_dashboard_payload(state, started_at, finished_at)
            
            ingest_id = payload.get('input_state', {}).get('ingest_id', 'unknown')
            
            logger.info(f"Sending transaction {ingest_id} to dashboard")
            logger.debug(f"Dashboard URL: {self.dashboard_url}")
            logger.debug(f"Dashoard payload {payload}")
            
            success = await self._send_to_dashboard(payload)

            if success:
                logger.info(f"Successfully logged transaction {ingest_id} to dashboard")
            else:
                logger.error(f"Failed to log transaction {ingest_id} to dashboard")

            return success

        except Exception as e:
            logger.error(f"Dashboard integration error: {e}", exc_info=True)
            return False

    def _build_dashboard_payload(self, state: Union[Dict, Any], started_at: Optional[str],
                                 finished_at: Optional[str]) -> Dict[str, Any]:
        """Build payload matching the expected dashboard format - just pass through the agent output"""

        # Convert state to dict if needed
        state_dict = self._safe_to_dict(state)
        
        # Check if ingest_id is already present at root level (from orchestrator)
        if isinstance(state_dict, dict) and state_dict.get('ingest_id'):
            final_ingest_id = state_dict['ingest_id']
            logger.info(f"Using ingest_id from formatted payload: {final_ingest_id}")
        else:
            # Fallback: extract from input_state
            ingest_id = None
            if isinstance(state_dict, dict) and 'input_state' in state_dict:
                input_state = state_dict['input_state']
                if isinstance(input_state, dict):
                    ingest_id = (input_state.get('ingest_id') or 
                               input_state.get('ingestId') or 
                               input_state.get('displayId'))
            
            # Use ingest_id as the identifier, with fallback to UUID generation
            try:
                if ingest_id and ingest_id != 'unknown':
                    # Validate that ingest_id is a proper UUID format
                    uuid.UUID(str(ingest_id))  # This will raise ValueError if not a valid UUID
                    final_ingest_id = str(ingest_id)
                    logger.info(f"Using existing ingest_id: {final_ingest_id}")
                else:
                    raise ValueError("No valid ingest_id found")
            except (ValueError, TypeError) as e:
                # Generate a UUID if ingest_id is not a valid UUID or doesn't exist
                final_ingest_id = str(uuid.uuid4())
                logger.warning(f"ingest_id '{ingest_id}' is not a valid UUID ({e}), generated new ingest_id: {final_ingest_id}")
        
        # Ensure ingest_id is at root level
        payload_with_ingest = {
            "ingest_id": final_ingest_id,
            **state_dict
        }
        
        logger.info(f"Final ingest_id being sent: {final_ingest_id}")
        return payload_with_ingest

    def _safe_to_dict(self, obj):
        """Convert any object to a JSON-serializable dict"""
        try:
            if obj is None:
                return {}

            if isinstance(obj, dict):
                result = {}
                for key, value in obj.items():
                    result[key] = self._safe_to_dict(value)
                return result

            if isinstance(obj, list):
                return [self._safe_to_dict(item) for item in obj]

            if hasattr(obj, 'model_dump'):
                return self._safe_to_dict(obj.model_dump())
            elif hasattr(obj, 'dict'):
                return self._safe_to_dict(obj.dict())

            if isinstance(obj, (str, int, float, bool, type(None))):
                return obj

            if isinstance(obj, datetime):
                return obj.isoformat()

            if hasattr(obj, '__str__') and 'uuid' in str(type(obj)).lower():
                return str(obj)

            return str(obj)

        except Exception as e:
            logger.warning(f"Could not serialize object {type(obj)}: {e}")
            return str(obj) if obj is not None else {}

    async def _send_to_dashboard(self, payload):
        """Send the payload directly to dashboard - it should already be in the correct format"""
        
        try:
            json.dumps(payload, default=str)
            
            dashboard_url = self.dashboard_url.rstrip("/") + "/api/ingestion/event"
            logger.debug(f"Making HTTP request to {dashboard_url}")
            
            headers = {"Content-Type": "application/json"}
            if self.auth_client is not None:
                try:
                    auth_headers = await self.auth_client.get_headers()
                    headers.update(auth_headers)
                    logger.debug("Included Easy Auth bearer token in dashboard request")
                except Exception as e:
                    logger.error(f"Failed to acquire Easy Auth token: {e}")
                    # Continue without auth to avoid losing data if dashboard allows anonymous
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Dashboard payload ingest_id: {payload.get('ingest_id')}")
                response = await client.post(
                    dashboard_url,
                    json=payload,
                    headers=headers,
                )

                logger.info(f"Dashboard API response: {response.status_code}")
                
                if response.status_code in [200, 201, 202]:
                    ingest_id = payload.get('ingest_id', 'unknown')
                    logger.info(f"Dashboard API accepted payload for ingest_id: {ingest_id}")
                    return True
                else:
                    logger.error(f"Dashboard API error: {response.status_code}")
                    logger.error(f"Response body: {response.text[:500]}...")
                    return False

        except Exception as e:
            logger.error(f"Dashboard API error: {e}")
            return False


dashboard_integration = DashboardIntegration()
