from pathlib import Path
import json
from datetime import datetime
from app.payroll_agent.evaluation.run_evaluation import run_evaluation

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

INPUT_DIR = Path("app/payroll_agent/evaluation/evaluation_cases/batch_cases")
OUTPUT_DIR = Path(f"app/payroll_agent/evaluation/evaluation_batch_results/run_{timestamp}")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
CHECKPOINT_FILE = OUTPUT_DIR / "checkpoint.json"

def save_checkpoint(last_batch_index):
    with open(CHECKPOINT_FILE, "w") as f:
        json.dump({"last_completed": last_batch_index}, f)

def load_checkpoint():
    if CHECKPOINT_FILE.exists():
        with open(CHECKPOINT_FILE) as f:
            return json.load(f).get("last_completed", -1)
    return -1

def run_batch_evaluation():
    OUTPUT_DIR.mkdir(exist_ok=True)
    input_files = sorted(INPUT_DIR.glob("*.json"))  # Sort ensures correct order

    last_done = load_checkpoint() 
    for idx, input_path in enumerate(input_files):
        if idx <= last_done:
            continue

        print(f"\n Running batch {idx}: {input_path.resolve()}")
        stem = input_path.stem

        try:
            with open(input_path) as f:
                batch_cases = json.load(f)

            run_evaluation(
                input_file=None,
                output_file=OUTPUT_DIR / f"evaluation_{stem}_batch_{idx}.xlsx",
                sheet_prefix=f"Batch_{idx}",
                test_cases=batch_cases
            )


            save_checkpoint(idx)
        except Exception as e:
            print(f"❌ Error on batch {idx} ({input_path.name}): {e}")
            break

if __name__ == "__main__":
    run_batch_evaluation()