from typing import Optional, List, Union
from pydantic import BaseModel, Field


class WorkerCommand(BaseModel):
    name: str = Field(..., description="Name of the worker mentioned in the email")
    worker_id: str = Field(..., description="Unique identifier for the worker")
    registered_name: str = Field(None, description="Name of the worker as registered in the system")
    hours: Optional[float] = Field(None, description="Hours to pay, if applicable", alias="payHours")
    rate: Optional[float] = Field(None, description="Rate to pay, if applicable", alias="payRate")
    rate_type: Optional[str] = Field(None, description="Type of rate (hourly, salary, etc.)", alias="rateType")
    rate_overwrite: Optional[bool] = Field(None, description="True if the rate is not the default rate, False if it is the default rate")
    amount: Optional[float] = Field(None, description="Amount to pay, if explicitly mention", alias="payAmount")
    memo: Optional[bool] = Field(False, description="Memo for the pay command")
    classificationType: Optional[str] =  Field(None, description="classificationType from the pay_components options in the context", alias="classificationType")
    earning_name: Optional[str] = Field(None, description="name of the pay component type from the pay_components options in the context")
    componentId: Optional[str] = Field(None, description="componentId from the pay_components options in the context")

class PayrollResponse(BaseModel):
    commands: List[WorkerCommand]


class PayAmountInput(BaseModel):
    """
    Schema for pay amount calculation inputs.
    """
    hours: float
    rate: float


class PayAmountOutput(BaseModel):
    """
    Schema for pay amount calculation output.
    """
    amount: float