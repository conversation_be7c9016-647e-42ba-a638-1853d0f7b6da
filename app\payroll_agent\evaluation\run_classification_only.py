import json
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
from tqdm import tqdm
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import yaml
import asyncio

# Add project root to path
project_root = Path(__file__).resolve().parents[3]
sys.path.append(str(project_root))

from app.payroll_agent.graph.graph_builder import create_classification_subgraph
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.utils.funcs import parse_upstream_model, load_routers_config
from app.payroll_agent.utils.prompt import load_prompt
from app.api.models.input import UpstreamModel


async def run_classification_on_case(test_case, case_index):
    """Run classification and return raw result."""
    try:
        # Parse the upstream model
        upstream_model = UpstreamModel.model_validate(test_case)
        
        # Convert to input state
        input_dict = parse_upstream_model(upstream_model)
        initial_state = PayrollState(input_state=input_dict)
        
        # Create and run classification subgraph
        classification_graph = create_classification_subgraph()
        result = await classification_graph.ainvoke(initial_state)  # Change invoke to ainvoke
        
        # Convert result to JSON-serializable format
        if hasattr(result, 'model_dump'):
            result_json = result.model_dump()
        elif isinstance(result, dict):
            result_json = result
        else:
            result_json = str(result)
        
        return {
            "case_index": case_index,
            "company_id": test_case.get("companyID", ""),
            "success": True,
            "raw_result": result_json
        }
        
    except Exception as e:
        return {
            "case_index": case_index,
            "company_id": test_case.get("companyID", ""),
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }


# Update run_cases_parallel to work with async functions
async def run_cases_parallel(test_cases, max_workers=4):
    """Run test cases in parallel with progress tracking."""
    results = [None] * len(test_cases)  # Pre-allocate to maintain order
    success_count = 0
    error_count = 0
    
    # Use a lock for thread-safe counter updates
    counter_lock = threading.Lock()
    
    def update_counters(success):
        nonlocal success_count, error_count
        with counter_lock:
            if success:
                success_count += 1
            else:
                error_count += 1
            return success_count, error_count
    
    # Create tasks for each case
    tasks = []
    for i, test_case in enumerate(test_cases):
        tasks.append(run_classification_on_case(test_case, i))
    
    # Process with progress bar
    with tqdm(total=len(test_cases), desc="Processing cases", unit="case") as pbar:
        for i, future in enumerate(asyncio.as_completed(tasks)):
            result = await future
            index = result["case_index"]
            results[index] = result
            
            # Update counters and progress bar
            current_success, current_error = update_counters(result['success'])
            completed = current_success + current_error
            
            pbar.set_description(f"Case {completed}/{len(test_cases)}: {result['company_id']}")
            pbar.set_postfix({
                'Success': current_success,
                'Errors': current_error,
                'Success Rate': f"{current_success/completed*100:.1f}%" if completed > 0 else "0.0%"
            })
            pbar.update(1)
    
    return results, success_count, error_count


# Update run_cases_sequential to work with async functions
async def run_cases_sequential(test_cases):
    """Run test cases sequentially (original method)."""
    results = []
    success_count = 0
    error_count = 0
    
    with tqdm(total=len(test_cases), desc="Processing cases", unit="case") as pbar:
        for i, test_case in enumerate(test_cases):
            company_id = test_case.get('companyID', 'Unknown')
            
            # Update progress bar description with current case
            pbar.set_description(f"Case {i+1}/{len(test_cases)}: {company_id}")
            
            result = await run_classification_on_case(test_case, i)
            results.append(result)
            
            if result['success']:
                success_count += 1
            else:
                error_count += 1
                
            pbar.set_postfix({
                'Success': success_count, 
                'Errors': error_count,
                'Success Rate': f"{success_count/(i+1)*100:.1f}%"
            })
            
            # Update progress bar
            pbar.update(1)
    
    return results, success_count, error_count


def flatten_dict_states(response_data, target_keys):
    """Flatten dictionary-based state representations into separate columns."""
    row = {}
    
    for key, value in response_data.items():
        if key in target_keys:
            # Handle Pydantic model objects
            if hasattr(value, 'model_dump'):
                # Convert Pydantic model to dict first
                value_dict = value.model_dump()
                # Flatten dictionary one level
                for attr_key, attr_value in value_dict.items():
                    if isinstance(attr_value, (dict, list)):
                        row[f"{key}.{attr_key}"] = json.dumps(attr_value, ensure_ascii=False)
                    else:
                        row[f"{key}.{attr_key}"] = attr_value
                        
            elif isinstance(value, dict):
                # Flatten dictionary one level
                for attr_key, attr_value in value.items():
                    if isinstance(attr_value, (dict, list)):
                        row[f"{key}.{attr_key}"] = json.dumps(attr_value, ensure_ascii=False)
                    else:
                        row[f"{key}.{attr_key}"] = attr_value
            else:
                # Keep as-is for other types (shouldn't happen with current structure)
                row[key] = value
    
    return row


def create_config_dataframes():
    """Create separate DataFrames for routing config, prompts, and classification YAML files."""
    try:
        # Load routing config
        routers_config = load_routers_config('routers_config')['classification']
        
        # Convert routing config to DataFrame
        routing_rows = []
        for section_name, section_config in routers_config.items():
            if isinstance(section_config, dict):
                for config_key, config_value in section_config.items():
                    routing_rows.append({
                        'section': section_name,
                        'key': config_key,
                        'value': config_value
                    })
            else:
                routing_rows.append({
                    'section': section_name,
                    'key': '',
                    'value': section_config
                })
        
        df_routing = pd.DataFrame(routing_rows)
        
        # Load prompts
        prompts = load_prompt("classification")
        
        # Convert prompts to DataFrame
        prompt_rows = []
        for prompt_name, prompt_text in prompts.items():
            prompt_rows.append({
                'prompt_name': prompt_name,
                'prompt_text': prompt_text
            })
        
        df_prompts = pd.DataFrame(prompt_rows)
        
        # Load classification.yaml
        classification_yaml_path = project_root / "app/payroll_agent/models/classification.yaml"
        df_classification_yaml = pd.DataFrame()
        
        if classification_yaml_path.exists():
            with open(classification_yaml_path, 'r', encoding='utf-8') as f:
                classification_config = yaml.safe_load(f)
            
            # Flatten classification YAML structure
            classification_rows = []
            for model_name, model_config in classification_config.items():
                # Add model description
                if 'description' in model_config:
                    classification_rows.append({
                        'model': model_name,
                        'field': '',
                        'type': '',
                        'required': '',
                        'description': model_config['description'],
                        'options': ''
                    })
                
                # Add each field
                for field_name, field_config in model_config.get('fields', {}).items():
                    classification_rows.append({
                        'model': model_name,
                        'field': field_name,
                        'type': field_config.get('type', ''),
                        'required': field_config.get('required', ''),
                        'description': field_config.get('description', ''),
                        'options': ', '.join(field_config.get('options', []))
                    })
            
            df_classification_yaml = pd.DataFrame(classification_rows)
        
        # Load classification_business_knockout_library.yaml
        knockout_yaml_path = project_root / "app/payroll_agent/models/classification_business_knockout_library.yaml"
        df_knockout_yaml = pd.DataFrame()
        
        if knockout_yaml_path.exists():
            with open(knockout_yaml_path, 'r', encoding='utf-8') as f:
                knockout_config = yaml.safe_load(f)
            
            # Flatten knockout rules structure
            knockout_rows = []
            for rule in knockout_config.get('knockout_rules', []):
                if rule and isinstance(rule, dict):
                    knockout_rows.append({
                        'rule_id': rule.get('id', ''),
                        'description': rule.get('description', ''),
                        'condition': rule.get('condition', ''),
                        'examples': '; '.join(rule.get('examples', [])),
                        'trigger_keywords': ', '.join(rule.get('trigger_keywords', [])),
                        'exclusion_keywords': ', '.join(rule.get('exclusion_keywords', []))
                    })
            
            df_knockout_yaml = pd.DataFrame(knockout_rows)
        
        return df_routing, df_prompts, df_classification_yaml, df_knockout_yaml
        
    except Exception as e:
        print(f"Warning: Could not load config data: {e}")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


async def main_async():
    """Run classification on test cases and save to Excel."""
    # Load test cases
    test_file = "app/payroll_agent/evaluation/evaluation_cases/mock_ca_case_single.json"
    
    print(f"Loading test cases from: {test_file}")
    
    with open(test_file, 'r', encoding='utf-8') as f:
        test_cases = json.load(f)
    
    # Configuration options
    PARALLEL_PROCESSING = True  # Set to False for sequential processing
    MAX_WORKERS = 4  # Number of parallel workers (adjust based on your system)
    
    print(f"📋 Running classification on {len(test_cases)} test cases...")
    if PARALLEL_PROCESSING:
        print(f"🚀 Using parallel processing with {MAX_WORKERS} workers")
    else:
        print(f"🐌 Using sequential processing")
    
    # Track timing
    start_time = time.time()
    
    # Run classification (parallel or sequential)
    if PARALLEL_PROCESSING:
        results, success_count, error_count = await run_cases_parallel(test_cases, max_workers=MAX_WORKERS)
    else:
        results, success_count, error_count = await run_cases_sequential(test_cases)
    
    # Calculate timing stats
    total_time = time.time() - start_time
    avg_time_per_case = total_time / len(test_cases)
    
    print(f"\n⏱️  Processing completed in {total_time:.2f} seconds")
    print(f"⏱️  Average time per case: {avg_time_per_case:.2f} seconds")
    print(f"✅ {success_count}/{len(test_cases)} successful ({success_count/len(test_cases)*100:.1f}%)")
    
    if PARALLEL_PROCESSING:
        speedup_estimate = MAX_WORKERS * 0.7  # Rough estimate accounting for overhead
        print(f"🚀 Estimated speedup: ~{speedup_estimate:.1f}x faster than sequential")
    
    # Create DataFrame from successful results
    successful_results = [r for r in results if r and r['success']]
    
    if successful_results:
        print(f"\n📊 Creating Excel output for {len(successful_results)} successful cases...")
        
        # Flatten only input_state and classification_output_state
        target_keys = ['input_state', 'classification_output_state']
        flat_rows = []
        
        for result in tqdm(successful_results, desc="Flattening results", unit="result"):
            # Start with case_index and company_id
            row = {
                "case_index": result['case_index'], 
                "company_id": result['company_id']
            }
            
            # Flatten the target states
            flattened = flatten_dict_states(result['raw_result'], target_keys)
            row.update(flattened)
            flat_rows.append(row)
        
        # Create main results DataFrame
        df_results = pd.DataFrame(flat_rows)
        
        # Create config DataFrames (now returns 4 DataFrames)
        df_routing, df_prompts, df_classification_yaml, df_knockout_yaml = create_config_dataframes()
        
        # Save to Excel with multiple sheets
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        output_dir = Path(__file__).parent / "evaluation_results"
        output_dir.mkdir(parents=True, exist_ok=True)
        excel_file = output_dir / f"classification_evaluation_results_{timestamp}.xlsx"
        
        print(f"💾 Saving to Excel...")
        
        # Create Excel writer and save to multiple sheets
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # Sheet 1: Full results
            df_results.to_excel(writer, sheet_name='Results', index=False)
            
            # Sheet 2: Routing config
            if not df_routing.empty:
                df_routing.to_excel(writer, sheet_name='Routing_Config', index=False)
            
            # Sheet 3: Prompts
            if not df_prompts.empty:
                df_prompts.to_excel(writer, sheet_name='Prompts', index=False)
            
            # Sheet 4: Classification model definitions
            if not df_classification_yaml.empty:
                df_classification_yaml.to_excel(writer, sheet_name='Classification_Models', index=False)
            
            # Sheet 5: Knockout rules library
            if not df_knockout_yaml.empty:
                df_knockout_yaml.to_excel(writer, sheet_name='Knockout_Rules', index=False)
        
        print(f"\n📊 DataFrame created with shape: {df_results.shape}")
        print(f"💾 Excel file saved to: {excel_file}")
        
        # Show sheet information
        print(f"\n📋 Excel sheets created:")
        print(f"  📄 'Results': Classification results ({df_results.shape[1]} columns, {df_results.shape[0]} rows)")
        if not df_routing.empty:
            print(f"  📄 'Routing_Config': Router configuration ({df_routing.shape[0]} settings)")
        if not df_prompts.empty:
            print(f"  📄 'Prompts': Classification prompts ({df_prompts.shape[0]} prompts)")
        if not df_classification_yaml.empty:
            print(f"  📄 'Classification_Models': Model definitions ({df_classification_yaml.shape[0]} fields)")
        if not df_knockout_yaml.empty:
            print(f"  📄 'Knockout_Rules': Knockout rule library ({df_knockout_yaml.shape[0]} rules)")
        
        # Show performance summary
        print(f"\n📈 Performance Summary:")
        print(f"  ⏱️  Total time: {total_time:.2f}s")
        print(f"  ⏱️  Average per case: {avg_time_per_case:.2f}s")
        print(f"  ✅ Success rate: {success_count/len(test_cases)*100:.1f}%")
        if PARALLEL_PROCESSING:
            print(f"  🚀 Parallel workers: {MAX_WORKERS}")

    else:
        print("❌ No successful results to save")
        
        # Save failed results for debugging
        if results:
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            output_dir = Path(__file__).parent / "evaluation_results"
            output_dir.mkdir(parents=True, exist_ok=True)
            error_file = output_dir / f"classification_errors_{timestamp}.json"
            
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump([r for r in results if r], f, indent=2, ensure_ascii=False)
            
            print(f"💾 Error details saved to: {error_file}")
    
    return results


def main():
    """Entry point that runs the async main function."""
    return asyncio.run(main_async())


if __name__ == "__main__":
    results = main()