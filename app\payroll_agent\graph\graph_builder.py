from langgraph.graph.state import END, START, StateGraph

from app.payroll_agent.graph.states.common import PayrollState


from app.payroll_agent.nodes.classification import (classification_intent_triage,
                                                    classification_intent_triage_router,
                                                    classification_business_knockout_batched,
                                                    classification_business_knockout_router,
                                                    classification_technical_knockout_batched,
                                                    classification_technical_knockout_router,
                                                    classification_branches_router,
                                                    classification_request_type,
                                                    classification_complexity,
                                                    classification_terminate,
                                                    classification_finish_node)

from app.payroll_agent.nodes.company_worker_lookup import (
    company_worker_lookup_retrieve_company_info,
    company_worker_lookup_identify_sender,
    company_worker_lookup_process_attachments,
    company_worker_lookup_retrieve_workers,
    company_worker_lookup_match_workers,
    company_worker_lookup_get_payperiodID,
    company_worker_get_agent_context,
    company_worker_lookup_terminate,
    company_worker_lookup_router,
    company_worker_lookup_initial_router,
    company_worker_lookup_finishing_node,
)

from app.payroll_agent.nodes.payroll_processing import (payroll_processing_agent_run,
                                                        payroll_processing_agent_2_run,
                                                        payroll_processing_finishing_node,
                                                        payroll_processing_router,
                                                        payroll_processing_terminate)

from app.payroll_agent.nodes.execution import (execution_prepare_execution_payload,
                                               execution_prepare_payload_router,
                                               execution_create_payrolls,
                                               execution_success_payroll_router,
                                               execution_terminate,
                                               execution_finishing_node)

from app.payroll_agent.nodes.validation import (validation_run_agent,
                                                validation_finishing_node,
                                                validation_terminate,
                                                validation_router,
                                                )

from app.payroll_agent.nodes.release import (release_proceed_without_verification,
                                             release_payroll,
                                             release_router,
                                             release_terminate,
                                             release_dashboard_logging,
                                             release_create_summary,
                                             release_update_upstream_ticket,
                                             release_send_confirmation,release_finishing_node)
# Set up the logger
from app.cli.logging_utils import setup_logger
logger = setup_logger(__name__)


# useful nodes
def converge_node(state: PayrollState) -> PayrollState:
    """A convergence node that waits for parallel paths to complete."""
    logger.info("convergence node reached! Parallel paths have completed.")
    return state

def branching_node(state: PayrollState) -> PayrollState:
    """Simple branching node for multiple parallel paths."""
    logger.info("branching_node called - proceeding to parallel execution")
    return state



# Create subgraphs for different stages of the workflow
# Each subgraph can be used independently or as part of the main graph
def create_classification_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("classification__intent_triage", classification_intent_triage)
    workflow.add_node("classification__intent_triage_router", classification_intent_triage_router)
    workflow.add_node("classification__branching_node", branching_node)
    workflow.add_node("classification__business_knockout", classification_business_knockout_batched)
    workflow.add_node("classification__business_knockout_router", classification_business_knockout_router)
    workflow.add_node("classification__technical_knockout", classification_technical_knockout_batched)
    workflow.add_node("classification__technical_knockout_router", classification_technical_knockout_router)
    workflow.add_node("classification__branches_router", classification_branches_router)
    workflow.add_node("classification__request_type", classification_request_type)
    workflow.add_node("classification__complexity", classification_complexity)
    workflow.add_node("classification__terminate", classification_terminate)
    workflow.add_node("classification__finish_node", classification_finish_node)

    # add edges
    workflow.add_edge(START, "classification__intent_triage")
    workflow.add_edge("classification__intent_triage", "classification__intent_triage_router")

    # Stop if triage fails
    workflow.add_conditional_edges(
        "classification__intent_triage_router",
        lambda x: x.classification_output_state.intent_triage_passes_flag,
        {
            True: "classification__branching_node",
            False: "classification__terminate"
        }
    )

    # branching
    workflow.add_edge("classification__branching_node", "classification__business_knockout")
    workflow.add_edge("classification__branching_node", "classification__technical_knockout")

    # Business knockout & Technical knockout
    workflow.add_edge("classification__business_knockout", "classification__business_knockout_router")
    workflow.add_edge("classification__technical_knockout", "classification__technical_knockout_router")
    workflow.add_edge(["classification__business_knockout_router", "classification__technical_knockout_router"], "classification__branches_router")

    # Branches router
    workflow.add_conditional_edges(
        "classification__branches_router",
        lambda x: x.classification_output_state.branches_router,
        {
            True: "classification__request_type",
            False: "classification__terminate"
        }
    )

    workflow.add_edge("classification__request_type", "classification__complexity")
    workflow.add_edge("classification__complexity", "classification__finish_node")
    workflow.add_edge("classification__terminate", END)
    workflow.add_edge("classification__finish_node", END)

    return workflow.compile()


def create_company_worker_lookup_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    
    # Parallel processing nodes that can run immediately
    workflow.add_node("company_worker_lookup__retrieve_company_info", company_worker_lookup_retrieve_company_info)
    workflow.add_node("company_worker_lookup__identify_sender_workers", company_worker_lookup_identify_sender)
    workflow.add_node("company_worker_lookup__process_attachments", company_worker_lookup_process_attachments)
    
    # Nodes that depend on company info
    workflow.add_node("company_worker_lookup__retrieve_workers", company_worker_lookup_retrieve_workers)
    workflow.add_node("company_worker_lookup__get_payperiodID", company_worker_lookup_get_payperiodID)

    # Convergence and processing nodes
    workflow.add_node("company_worker_lookup_match_workers", company_worker_lookup_match_workers)
    workflow.add_node("company_worker_lookup__initial_router", company_worker_lookup_initial_router)
    workflow.add_node("company_worker_lookup__branching", branching_node)
    workflow.add_node("company_worker_lookup__final_router", company_worker_lookup_router)

    # Final nodes
    workflow.add_node("company_worker_lookup__get_agent_context", company_worker_get_agent_context)
    workflow.add_node("company_worker_lookup__finish_node", company_worker_lookup_finishing_node)
    workflow.add_node("company_worker_lookup__terminate", company_worker_lookup_terminate)

    ### Start 3 independent paths that converge at router
    
    # Start all 3 paths in parallel
    workflow.add_edge(START, "company_worker_lookup__retrieve_company_info")
    workflow.add_edge(START, "company_worker_lookup__identify_sender_workers")
    workflow.add_edge(START, "company_worker_lookup__process_attachments")
    
    # ALL PATHS CONVERGE: Wait for all 3 to complete before router
    workflow.add_edge([
        "company_worker_lookup__retrieve_company_info",
        "company_worker_lookup__identify_sender_workers", 
        "company_worker_lookup__process_attachments"
    ], "company_worker_lookup__initial_router")

    # INITIAL ROUTER: Validates first parallel phase and decides whether to continue
    # This validates company info, sender identification, and attachment processing
    workflow.add_conditional_edges(
        "company_worker_lookup__initial_router",
        lambda x: x.company_worker_lookup_output_state.initial_validation_success,
        {
            True: "company_worker_lookup__branching",
            False: "company_worker_lookup__terminate"
        }
    )

    # Branching node starts both workers and payperiods in parallel
    workflow.add_edge(
        "company_worker_lookup__branching",
        "company_worker_lookup__retrieve_workers"
    )
    workflow.add_edge(
        "company_worker_lookup__branching",
        "company_worker_lookup__get_payperiodID"
    )

    # Workers path: retrieve → match workers
    workflow.add_edge(
        "company_worker_lookup__retrieve_workers",
        "company_worker_lookup_match_workers"
    )

    # Both paths converge at the final router for complete validation
    workflow.add_edge(
        ["company_worker_lookup_match_workers", "company_worker_lookup__get_payperiodID"],
        "company_worker_lookup__final_router"
    )
    
    # Final router validates all data and routes to success or failure
    workflow.add_conditional_edges(
        "company_worker_lookup__final_router",
        lambda x: x.company_worker_lookup_output_state.worker_lookup_success,
        {
            True: "company_worker_lookup__get_agent_context",
            False: "company_worker_lookup__terminate"
        }
    )

    # Final step: get agent context and finish
    workflow.add_edge(
        "company_worker_lookup__get_agent_context",
        "company_worker_lookup__finish_node"
    )
    workflow.add_edge("company_worker_lookup__terminate", END)
    workflow.add_edge("company_worker_lookup__finish_node", END)

    return workflow.compile()


def create_payroll_processing_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("payroll_processing_branching_node", branching_node)
    workflow.add_node("payroll_processing_payroll_agent_1", payroll_processing_agent_run)
    workflow.add_node("payroll_processing_payroll_agent_2", payroll_processing_agent_2_run)
    workflow.add_node("payroll_processing_router", payroll_processing_router)
    workflow.add_node("payroll_processing_terminate", payroll_processing_terminate)
    workflow.add_node("payroll_processing_finishing_node", payroll_processing_finishing_node)

    # add edges
    workflow.add_edge(START, "payroll_processing_branching_node")
    workflow.add_edge("payroll_processing_branching_node", "payroll_processing_payroll_agent_1")
    workflow.add_edge("payroll_processing_branching_node", "payroll_processing_payroll_agent_2")
    workflow.add_edge(["payroll_processing_payroll_agent_1", "payroll_processing_payroll_agent_2"], "payroll_processing_router")
    workflow.add_conditional_edges("payroll_processing_router",
        lambda x: x.payroll_processing_output_state.agents_matched,
        {
            True: "payroll_processing_finishing_node",
            False: "payroll_processing_terminate"
        }
    )
    workflow.add_edge("payroll_processing_terminate", END)
    workflow.add_edge("payroll_processing_finishing_node", END)

    return workflow.compile()


def create_execution_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("execution_prepare_execution_payload", execution_prepare_execution_payload)
    workflow.add_node("execution_prepare_payload_router", execution_prepare_payload_router)
    workflow.add_node("execution_create_payrolls", execution_create_payrolls)
    workflow.add_node("execution_execution_success_payroll_router", execution_success_payroll_router)
    workflow.add_node("execution_terminate", execution_terminate)
    workflow.add_node("execution_finishing_node", execution_finishing_node)

    # add edges
    workflow.add_edge(START, "execution_prepare_execution_payload")
    workflow.add_edge("execution_prepare_execution_payload", "execution_prepare_payload_router")
    workflow.add_conditional_edges("execution_prepare_payload_router",
        lambda x: x.execution_output_state.prepare_payload.success,
            {
                True: "execution_create_payrolls",
                False: "execution_terminate"
            }
    )
    workflow.add_edge("execution_create_payrolls", "execution_execution_success_payroll_router")
    workflow.add_conditional_edges("execution_execution_success_payroll_router",
        lambda x: x.execution_output_state.created_checks_flag,
            {
                True: "execution_finishing_node",
                False: "execution_terminate"
            }
    )
    workflow.add_edge("execution_terminate", END)
    workflow.add_edge("execution_finishing_node", END)

    return workflow.compile()


def create_validation_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("validation_agent", validation_run_agent)
    workflow.add_node("validation_router", validation_router)
    workflow.add_node("validation_terminate", validation_terminate)
    workflow.add_node("validation_finishing_node", validation_finishing_node)

    # add edges
    workflow.add_edge(START, "validation_agent")
    workflow.add_edge("validation_agent", "validation_router")
    workflow.add_conditional_edges("validation_router",
        lambda x: x.validation_output_state.validated_payroll_entries.success,
        {
            True: "validation_finishing_node",
            False: "validation_terminate"
        }
    )
    workflow.add_edge("validation_terminate", END)
    workflow.add_edge("validation_finishing_node", END)

    return workflow.compile()


def create_release_subgraph() -> StateGraph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("release_proceed_without_verification", release_proceed_without_verification)
    workflow.add_node("release_payroll", release_payroll)
    workflow.add_node("release_router", release_router)
    workflow.add_node("release_terminate", release_terminate)
    workflow.add_node("release_dashboard_logging", release_dashboard_logging)
    workflow.add_node("release_create_summary", release_create_summary)
    workflow.add_node("release_update_upstream_ticket", release_update_upstream_ticket)
    workflow.add_node("release_send_confirmation", release_send_confirmation)
    workflow.add_node("release_finishing_node", release_finishing_node)

    # add edges
    workflow.add_edge(START, "release_proceed_without_verification")
    workflow.add_edge("release_proceed_without_verification", "release_router")
    workflow.add_conditional_edges(
        "release_router",
        lambda x: x.release_output_state.should_continue,
        {
            True: "release_payroll",
            False: "release_terminate"
        }
    )
    workflow.add_edge("release_payroll", "release_dashboard_logging")
    workflow.add_edge("release_dashboard_logging", "release_update_upstream_ticket")
    workflow.add_edge("release_update_upstream_ticket", "release_send_confirmation")
    workflow.add_edge("release_send_confirmation", "release_create_summary")
    workflow.add_edge("release_create_summary", "release_finishing_node")
    workflow.add_edge("release_finishing_node", END)
    workflow.add_edge("release_terminate", END)

    return workflow.compile()


# Main graph that combines all subgraphs
# This is the full end-to-end payroll processing graph
def create_graph() -> StateGraph:
    """Full end-to-end payroll processing graph with classification before account lookup."""
    workflow = StateGraph(PayrollState, output=PayrollState)

    # Add subgraphs
    workflow.add_node("classification__", create_classification_subgraph())
    workflow.add_node("company_worker_lookup__", create_company_worker_lookup_subgraph())
    workflow.add_node("payroll_processing__", create_payroll_processing_subgraph())
    workflow.add_node("execution__", create_execution_subgraph())
    workflow.add_node("validation__", create_validation_subgraph())
    workflow.add_node("release__", create_release_subgraph())

    # Start with classification
    workflow.add_edge(START, "classification__")

    # If classification should continue, proceed to account lookup
    workflow.add_conditional_edges("classification__",
        lambda s: s.classification_output_state.should_continue,
        {
            True:  "company_worker_lookup__",
            False: END,
        }
    )
    # if account lookup terminated early, go to END; otherwise go to payroll_processing__
    workflow.add_conditional_edges("company_worker_lookup__",
        lambda s: s.company_worker_lookup_output_state.should_continue,
        {
            True:  "payroll_processing__",
            False: END,
        }
    )
    # if payroll_extraction terminated early, go to END; otherwise go to execution__
    workflow.add_conditional_edges("payroll_processing__",
        lambda s: s.payroll_processing_output_state.should_continue,
        {
            True:  "execution__",
            False: END,
        }
    )
    # if execution terminated early, go to END; otherwise go to validation__
    workflow.add_conditional_edges("execution__",
        lambda s: s.execution_output_state.should_continue,
        {
            True:  "validation__",
            False: END,
        }
    )

    # if payroll_extraction terminated early, go to END; otherwise go to release__
    workflow.add_conditional_edges("validation__",
        lambda s: s.validation_output_state.should_continue,
        {
            True:  "release__",
            False: END,
        }
    )

    #workflow.add_edge("release", END)

    return workflow.compile()


# Main graph (used in playground/dev)
graph = create_graph()

# Named subgraphs (for testing or playground clarity)
account_lookup = create_company_worker_lookup_subgraph()
classification = create_classification_subgraph()
payroll_extraction = create_payroll_processing_subgraph()
validation = create_validation_subgraph()
execution = create_execution_subgraph()
release = create_release_subgraph()


# Subgraph entry points (for CLI, playground, or testing)
SUBGRAPHS = {
    "account_lookup": create_company_worker_lookup_subgraph,
    "classification": create_classification_subgraph,
    "payroll_extraction": create_payroll_processing_subgraph,
    "validation": create_validation_subgraph,
    "execution": create_execution_subgraph,
    "release": create_release_subgraph
    }
