from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any

from app.payroll_agent.models.company_worker_lookup import (SenderEmailMetadata, ValidateSenderMetadata, WorkersMatch,
                                                            PayPeriod, MatchedPayPeriod)


class CompanyWorkerLookupOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    sender_email_metadata: Optional[SenderEmailMetadata] = Field(None, description="Metadata about the sender")
    validate_sender_metadata: Optional[ValidateSenderMetadata] = Field(None, description="Validation about the sender", exclude=True)
    company_scores: Optional[Any] = Field(None, description="Scores for the companies")
    company_selection_reason: Optional[str] = Field(None, description="Reason for company selection")
    company_lookup: Optional[Dict] = Field(None, description="Company lookup result")
    company_id_success: bool = Field(False, description="Whether the MCP retrieved the company ID or not")
    initial_validation_success: bool = Field(False, description="Whether initial parallel phase validation passed")
    attachments_found: bool = Field(False, description="Whether the email contains attachments")
    attachments_processed: bool = Field(False, description="Whether attachments have been successfully processed")
    attachment_handwritten_notes: Optional[list] = Field(None, description="Handwritten notes extracted from attachments")
    attachment_handwritten_notes_found: bool = Field(False, description="Whether handwritten notes were detected in attachments")
    employees_metadata: Optional[list[str]] = Field(None, description="List of names of the clients")
    workers_roster: Optional[Dict] = Field(None, description="List of workers")
    workers_search_success: bool = Field(False, description="Whether the MCP retrieved workers or not")
    llm_workers_match: Optional[WorkersMatch] = Field(None, description="List of matched workers")
    workers_matched: Optional[dict] = Field(None, description="Dict of matched workers")
    pay_periods: Optional[List[PayPeriod]] = Field(None, description="Retrieved pay periods")
    payperiodID: Optional[str] = Field(None, description="Payperiod ID")
    payperiod: Optional[MatchedPayPeriod] = Field(None, description="Payroll period to be use for process the payroll commands")
    worker_lookup_success: Optional[bool] = Field(False, description="Whether the client ID is unique and equal to the hint ID")
    agent_context: Optional[Dict[str, Any]] = Field(None, description="Agent context")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")
    run_time: Optional[dict] = Field(None, description="Time taken to run the classification graph")
    llm_usage: Optional[dict] = Field(default_factory=dict, description="LLM usage info: model, input tokens, output tokens")

